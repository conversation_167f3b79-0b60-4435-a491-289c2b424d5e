import { useState } from 'react';
import { useLanguageContext } from './LanguageProvider';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';

export function SettingsModal() {
  const { t } = useLanguageContext();
  
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <>
      {/* Temporary button to open modal */}
      <Button onClick={() => setIsOpen(true)} className="fixed bottom-4 left-4 z-50">Open Settings</Button>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{t('settingsTitle')}</DialogTitle>
          </DialogHeader>
          
          <div className="p-4">
            <h3 className="text-lg font-medium mb-4">{t('appearanceSettings')}</h3>
            <p className="mb-4">Settings content will be displayed here.</p>
            <Input placeholder={t('enterWord')} className="mb-4" />
            <div className="flex justify-end">
              <Button variant="outline" onClick={() => setIsOpen(false)} className="mr-2">{t('cancel')}</Button>
              <Button onClick={() => setIsOpen(false)}>{t('saveChanges')}</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
