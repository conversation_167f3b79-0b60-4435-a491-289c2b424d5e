<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate-key="app.title">GrammarPro</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Light mode styles (default) */
        body { background-color: #f9fafb; color: #1f2937; }
        header { background-color: #ffffff; border-bottom: 1px solid #e5e7eb; }
        h1 { color: #111827; }
        button, .button-like { background-color: #3b82f6; color: #ffffff; padding: 0.5rem 1rem; border-radius: 0.375rem; cursor: pointer; }
        button:hover, .button-like:hover { background-color: #2563eb; }
        aside { background-color: #ffffff; border-right: 1px solid #e5e7eb; }
        .mode-card { border: 1px solid #d1d5db; }
        .mode-card.active { border-color: #3b82f6; }
        .stats-grid div { background-color: #ffffff; border: 1px solid #e5e7eb; }
        .text-gray-600 { color: #4b5563; }
        .text-gray-800 { color: #1f2937; }
        .editor-toolbar { background-color: #ffffff; border-bottom: 1px solid #e5e7eb; }
        textarea { background-color: #ffffff; color: #1f2937; border: 1px solid #d1d5db; }
        .suggestion-card { background-color: #f3f4f6; border: 1px solid #e5e7eb; }
        .suggestion-card button { background-color: #10b981; }
        .suggestion-card button:hover { background-color: #059669; }

        /* Dark mode styles */
        body.dark { background-color: #111827; color: #f3f4f6; } /* bg-gray-900 */
        header.dark { background-color: #1f2937; border-bottom: 1px solid #374151; } /* bg-gray-800, border-gray-700 */
        h1.dark { color: #ffffff; }
        .dark button, .dark .button-like { background-color: #2563eb; color: #ffffff; }
        .dark button:hover, .dark .button-like:hover { background-color: #1d4ed8; }
        aside.dark { background-color: #1f2937; border-right: 1px solid #374151; } /* bg-gray-800, border-gray-700 */
        .dark .mode-card { border: 1px solid #4b5563; } /* border-gray-600 */
        .dark .mode-card.active { border-color: #2563eb; }
        .dark .stats-grid div { background-color: #1f2937; border: 1px solid #374151; } /* bg-gray-800, border-gray-700 */
        .dark .text-gray-400 { color: #9ca3af; }
        .dark .text-gray-300 { color: #d1d5db; }
        .dark .editor-toolbar { background-color: #1f2937; border-bottom: 1px solid #374151; } /* bg-gray-800, border-gray-700 */
        .dark .editor-toolbar button { color: #9ca3af; } /* text-gray-400 */
        
        /* Styles for the text editor and its overlay - these classes are added by JS or directly in HTML */
        /* Both .text-editor (textarea) and .text-editor-overlay (div) use Tailwind's p-4 and rounded-b-lg. */
        /* This ensures padding and border-radius match. */
        .text-editor, .text-editor-overlay {
            white-space: pre-wrap;      /* Match textarea behavior */
            word-wrap: break-word;       /* Match textarea behavior */
            font-family: inherit;        /* Inherit from body/parent for consistency */
            font-size: inherit;          /* Inherit for consistency */
            line-height: inherit;        /* Inherit for consistency */
            box-sizing: border-box;      /* Essential for absolute positioning and padding */
            /* The 'absolute inset-0' on both elements ensures they occupy the same space within their relative parent. */
            /* Tailwind's 'border' class on the textarea provides its border. */
        }

        /* Specifics for the textarea */
        textarea.text-editor {
            /* Tailwind classes define bg, text color, and border for light mode. */
            /* e.g., border-gray-300, bg-white, text-gray-800 */
            caret-color: #1f2937;       /* Default caret for light mode (text-gray-800) */
            /* resize-none is also a good class for textareas in this setup */
        }

        /* Specifics for the editor overlay */
        .text-editor-overlay {
            border: 1px solid transparent; /* Crucial: Overlay border must be transparent to show textarea's actual border */
            color: #1f2937;             /* Default text color (light mode), error spans will override */
            overflow: auto;              /* Match textarea's scroll behavior */
            pointer-events: none;        /* Already on the element, ensures clicks pass through */
        }

        /* Dark mode styles for the textarea */
        .dark textarea.text-editor {
            /* Tailwind classes: .dark:bg-gray-700, .dark:text-white, .dark:border-gray-600 */
            caret-color: #f3f4f6;       /* Caret color for dark mode (text-gray-100) */
        }
        /* The general .dark textarea rule is good but ensure it doesn't conflict if .text-editor is more specific */
        /* It seems consistent, so it's fine. */
         .dark textarea { background-color: #374151; color: #ffffff; border-color: #4b5563; caret-color: #f3f4f6;}


        /* Dark mode styles for the editor overlay */
        .dark .text-editor-overlay {
            color: #f3f4f6;             /* Default text color (dark mode) */
            /* border remains transparent */
        }

        /* Error highlighting styles */
        .error-spelling, .error-grammar {
            border-radius: 3px;         /* Slightly more pronounced rounding */
            padding: 0.5px 0;           /* Minimal vertical padding to avoid increasing line height too much */
        }
        .error-spelling { 
            background-color: rgba(255, 192, 203, 0.7); /* Pink, softer than orange */
        }
        .dark .error-spelling { 
            background-color: rgba(220, 20, 60, 0.5);   /* Crimson, more visible dark mode spelling */
        }
        .error-grammar { 
            background-color: rgba(173, 216, 230, 0.7); /* Light blue, existing */
        }
        .dark .error-grammar { 
            background-color: rgba(70, 130, 180, 0.6);  /* SteelBlue, slightly adjusted alpha for dark mode */
        }
        .error-style { /* Style suggestion highlighting */
            background-color: rgba(224, 255, 255, 0.8); /* LightCyan */
            border-bottom: 1px dashed #008b8b; /* DarkCyan dashed underline */
        }
        .dark .error-style {
            background-color: rgba(72, 61, 139, 0.5);  /* DarkSlateBlue */
            border-bottom: 1px dashed #98fb98; /* PaleGreen dashed underline */
        }
        .error-harmony { /* Vowel Harmony error highlighting */
            background-color: rgba(255, 235, 59, 0.4); /* Yellow, more distinct */
            text-decoration: underline wavy red;
        }
        .dark .error-harmony {
            background-color: rgba(255, 193, 7, 0.4); /* Amber, for dark mode */
            text-decoration: underline wavy #ff5252; /* Lighter red for dark mode underline */
        }

        .dark .suggestion-card { background-color: #374151; border: 1px solid #4b5563; } /* bg-gray-700, border-gray-600 */
        .dark .suggestion-card button { background-color: #059669; }
        .dark .suggestion-card button:hover { background-color: #047857; }
        .dark .text-gray-600 { color: #9ca3af; } /* Added to fix text color in dark mode */
        .dark .text-gray-800 { color: #d1d5db; } /* Added to fix text color in dark mode */

        /* Font size classes */
        .font-size-small { font-size: 85%; }
        .font-size-normal { font-size: 100%; }
        .font-size-large { font-size: 115%; }

        /* Active state for theme/font buttons */
        .active-theme-btn, .active-fontsize-btn {
            background-color: #3b82f6 !important; /* Tailwind blue-500 */
            color: #ffffff !important;
        }
        .dark .active-theme-btn, .dark .active-fontsize-btn {
             background-color: #2563eb !important; /* Tailwind blue-600 for dark active */
        }
    </style>
</head>
<body class="font-sans font-size-normal"> <!-- Default font size -->
    <header class="p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <h1 data-translate-key="header.title" class="text-2xl font-bold">GrammarPro</h1>
            <div class="flex items-center space-x-4">
                <!-- Old darkModeToggle button removed -->
                <label for="fileUpload" class="button-like"><span data-translate-key="header.uploadFile">Upload File</span></label>
                <input type="file" id="fileUpload" class="hidden" accept=".txt">
                <button id="exportBtn" class="button-like" data-translate-key="header.export">Export</button>
                <button id="openSettingsModalBtn" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" title="Ayarlar" data-translate-key="header.settings.tooltip">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </div>
    </header>

    <div class="container mx-auto flex mt-6">
        <!-- Left Sidebar -->
        <aside class="w-1/4 p-4 mr-4">
            <h2 data-translate-key="sidebar.modes.title" class="text-xl font-semibold mb-4 text-gray-800">Modes</h2>
            <div class="space-y-2">
                <div class="mode-card p-3 rounded-lg cursor-pointer active" data-translate-key="sidebar.modes.standard">Standard</div>
                <div class="mode-card p-3 rounded-lg cursor-pointer" data-translate-key="sidebar.modes.advanced">Advanced</div>
            </div>
            <h2 data-translate-key="sidebar.statistics.title" class="text-xl font-semibold mt-6 mb-4 text-gray-800">Statistics</h2>
            <div class="stats-grid grid grid-cols-2 gap-4">
                <div class="p-3 rounded-lg shadow">
                    <p data-translate-key="sidebar.statistics.errors" class="text-sm text-gray-600">Errors</p>
                    <p class="text-2xl font-bold text-gray-800">0</p>
                </div>
                <div class="p-3 rounded-lg shadow">
                    <p data-translate-key="sidebar.statistics.corrections" class="text-sm text-gray-600">Corrections</p>
                    <p class="text-2xl font-bold text-gray-800">0</p>
                </div>
            </div>
            <h2 data-translate-key="sidebar.settings.title" class="text-xl font-semibold mt-6 mb-4 text-gray-800">Settings</h2>
            <label class="flex items-center space-x-2">
                <input type="checkbox" class="form-checkbox">
                <span data-translate-key="sidebar.settings.autocorrect" class="text-gray-800">Auto-correct</span>
            </label>
        </aside>

        <!-- Main Content Area -->
        <main class="w-3/4 flex flex-col">
            <div class="editor-toolbar p-2 rounded-t-lg flex justify-between items-center">
                <div> <!-- Group for formatting buttons -->
                    <button class="mr-2 text-gray-600 hover:text-gray-900" data-translate-key="toolbar.bold">Bold</button>
                    <button class="mr-2 text-gray-600 hover:text-gray-900" data-translate-key="toolbar.italic">Italic</button>
                </div>
                <div> <!-- Group for action buttons -->
                    <button id="clearBtn" class="button-like text-sm" data-translate-key="toolbar.clear">Temizle</button>
                </div>
            </div>
            <div class="relative w-full h-64"> <!-- Container for textarea and overlay -->
                <textarea id="editor" class="absolute inset-0 w-full h-full p-4 border rounded-b-lg focus:ring-blue-500 focus:border-blue-500 resize-none text-editor" data-translate-key="editor.placeholder"></textarea>
                <div id="editorOverlay" class="absolute inset-0 w-full h-full p-4 rounded-b-lg pointer-events-none hidden text-editor-overlay"></div>
            </div>

            <div class="mt-6">
                <h2 data-translate-key="suggestions.title" class="text-xl font-semibold mb-4 text-gray-800">Suggestions</h2>
                <div class="suggestions-container space-y-3">
                    <!-- Suggestion cards will be dynamically inserted here by JS -->
                    <!-- Example of a suggestion card for key reference, though content is dynamic -->
                    <!-- 
                    <div class="suggestion-card p-3 rounded-lg shadow flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-600"><span data-translate-key="suggestions.card.incorrect">Incorrect</span>: <span class="text-red-500">teh</span></p>
                            <p class="text-sm text-gray-600"><span data-translate-key="suggestions.card.correct">Correct</span>: <span class="text-green-500">the</span></p>
                        </div>
                        <button class="text-sm" data-translate-key="suggestions.card.apply">Apply</button>
                    </div>
                     -->
                </div>
            </div>
            <button id="demoBtn" class="button-like mt-6" data-translate-key="demo.button">Run Demo</button>
        </main>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center no-print">
        <div class="relative p-8 bg-white dark:bg-gray-800 w-full max-w-2xl mx-auto rounded-lg shadow-xl">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 data-translate-key="settings.modal.title" class="text-2xl font-semibold text-gray-900 dark:text-white">Ayarlar</h2>
                <button id="closeSettingsModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-2xl"></i>
                </button>
            </div>

            <!-- Modal Body (Tabs for sections) -->
            <div class="space-y-6">
                <!-- Language Settings Section -->
                <div>
                    <h3 data-translate-key="settings.language.title" class="text-lg font-medium text-gray-800 dark:text-white mb-3">Dil Ayarları</h3>
                    <label for="languageSelect" data-translate-key="settings.language.selectLabel" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Arayüz Dili</label>
                    <select id="languageSelect" class="mt-1 block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-gray-900 dark:text-white">
                        <option value="en" data-translate-key="settings.language.english">English</option>
                        <option value="tr" data-translate-key="settings.language.turkish">Türkçe</option>
                        <option value="ar" data-translate-key="settings.language.arabic">العربية</option>
                        <option value="es" data-translate-key="settings.language.spanish">Español</option>
                    </select>
                </div>

                <!-- Appearance Settings Section -->
                <div>
                    <h3 data-translate-key="settings.appearance.title" class="text-lg font-medium text-gray-800 dark:text-white mb-3">Görünüm Ayarları</h3>
                    <!-- Theme Toggle -->
                    <div class="mb-4">
                        <label data-translate-key="settings.appearance.themeLabel" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tema</label>
                        <div class="flex space-x-2">
                            <button data-theme-option="light" data-translate-key="settings.appearance.theme.light" class="theme-option-btn px-4 py-2 border rounded-md">Light</button>
                            <button data-theme-option="dark" data-translate-key="settings.appearance.theme.dark" class="theme-option-btn px-4 py-2 border rounded-md">Dark</button>
                            <button data-theme-option="system" data-translate-key="settings.appearance.theme.system" class="theme-option-btn px-4 py-2 border rounded-md">System</button>
                        </div>
                    </div>
                    <!-- Font Size Toggle -->
                    <div>
                        <label data-translate-key="settings.appearance.fontSizeLabel" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Yazı Tipi Boyutu</label>
                        <div class="flex space-x-2">
                            <button data-fontsize-option="small" data-translate-key="settings.appearance.fontSize.small" class="fontsize-option-btn px-4 py-2 border rounded-md">Small</button>
                            <button data-fontsize-option="normal" data-translate-key="settings.appearance.fontSize.normal" class="fontsize-option-btn px-4 py-2 border rounded-md">Normal</button>
                            <button data-fontsize-option="large" data-translate-key="settings.appearance.fontSize.large" class="fontsize-option-btn px-4 py-2 border rounded-md">Large</button>
                        </div>
                    </div>
                </div>

                <!-- Personal Dictionary Section -->
                <div>
                    <h3 data-translate-key="settings.dictionary.title" class="text-lg font-medium text-gray-800 dark:text-white mb-3">Kişisel Sözlük</h3>
                    <div class="flex mb-2">
                        <input type="text" id="personalDictionaryWordInput" data-translate-key="settings.dictionary.addPlaceholder" placeholder="Sözlüğe kelime ekle..." class="flex-grow p-2 border border-gray-300 dark:border-gray-600 rounded-l-md dark:bg-gray-700 dark:text-white">
                        <button id="addWordToDictionaryBtn" class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"><i class="fas fa-plus"></i> <span data-translate-key="settings.dictionary.addButton">Ekle</span></button>
                    </div>
                    <div id="personalDictionaryList" class="max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md p-2 bg-gray-50 dark:bg-gray-700" data-translate-key="settings.dictionary.empty" aria-live="polite">
                        <!-- Words will be listed here, placeholder for empty state if needed -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Old setupDarkMode function is removed. Theme handling is now part of TurkishGrammarChecker.

        // Custom styles for elements that need specific overrides in dark mode, not covered by simple .dark selectors
        // This dynamic stylesheet might still be useful for some specific overrides if needed.
        // For now, most dark mode styling is handled by Tailwind's `dark:` prefix or direct CSS.
        // const styleSheet = document.createElement("style");
        // styleSheet.type = "text/css";
        // styleSheet.innerText = `
        //     .dark .dark-text-override { color: #d1d5db; /* Tailwind gray-300 */ }
        //     header.dark button, header.dark .button-like { background-color: #1e40af; /* More specific than general .dark button */ }
        //     header.dark button:hover, header.dark .button-like:hover { background-color: #1c3bab; }
        //     /* Ensure editor toolbar buttons also change color */
        //     .dark .editor-toolbar button { color: #9ca3af; /* text-gray-400 */ }
        //     .dark .editor-toolbar button:hover { color: #d1d5db; /* text-gray-300 */ }
        //     /* Ensure settings checkbox label text color changes */
        //     .dark label span { color: #d1d5db; /* text-gray-300 */ }
        //     /* Ensure text within suggestion cards adapts */
        //     .dark .suggestion-card p { color: #d1d5db; /* text-gray-300 */ }
        //     .dark .suggestion-card span.text-red-500 { color: #f87171; /* Tailwind red-400 (lighter red for dark bg) */ }
        //     .dark .suggestion-card span.text-green-500 { color: #6ee7b7; /* Tailwind green-300 (lighter green for dark bg) */ }
        // `;
        // document.head.appendChild(styleSheet);

        // setupDarkMode(); // Old call removed
        
        
        class GrammarChecker {
            constructor(textEditorId, editorOverlayId) {
                this.textEditor = document.getElementById(textEditorId);
                this.editorOverlay = document.getElementById(editorOverlayId);
                this.settingsModal = document.getElementById('settingsModal');
                this.languageSelect = document.getElementById('languageSelect');
                this.openSettingsModalBtn = document.getElementById('openSettingsModalBtn');
                this.closeSettingsModalBtn = document.getElementById('closeSettingsModalBtn');

                this.errors = []; 
                this.translations = {
                    'app.title': { 'en': 'GrammarPro', 'tr': 'Dilbilgisi Uzmanı', 'ar': 'جرامر برو', 'es': 'GrammarPro' },
                    'header.title': { 'en': 'GrammarPro', 'tr': 'Dilbilgisi Uzmanı', 'ar': 'جرامر برو', 'es': 'GrammarPro' },
                    'header.toggleDarkMode': { 'en': 'Toggle Dark Mode', 'tr': 'Karanlık Modu Değiştir', 'ar': 'تبديل الوضع الداكن', 'es': 'Alternar Modo Oscuro' },
                    'header.uploadFile': { 'en': 'Upload File', 'tr': 'Dosya Yükle', 'ar': 'تحميل ملف', 'es': 'Subir Archivo' },
                    'header.export': { 'en': 'Export', 'tr': 'Dışa Aktar', 'ar': 'تصدير', 'es': 'Exportar' },
                    'header.settings.tooltip': { 'en': 'Settings', 'tr': 'Ayarlar', 'ar': 'الإعدادات', 'es': 'Configuración' },
                    'sidebar.modes.title': { 'en': 'Modes', 'tr': 'Modlar', 'ar': 'الأوضاع', 'es': 'Modos' },
                    'sidebar.modes.standard': { 'en': 'Standard', 'tr': 'Standart', 'ar': 'قياسي', 'es': 'Estándar' },
                    'sidebar.modes.advanced': { 'en': 'Advanced', 'tr': 'Gelişmiş', 'ar': 'متقدم', 'es': 'Avanzado' },
                    'sidebar.statistics.title': { 'en': 'Statistics', 'tr': 'İstatistikler', 'ar': 'الإحصائيات', 'es': 'Estadísticas' },
                    'sidebar.statistics.errors': { 'en': 'Errors', 'tr': 'Hatalar', 'ar': 'الأخطاء', 'es': 'Errores' },
                    'sidebar.statistics.corrections': { 'en': 'Corrections', 'tr': 'Düzeltmeler', 'ar': 'التصحيحات', 'es': 'Correcciones' },
                    'sidebar.statistics.words': { 'en': 'Words', 'tr': 'Kelimeler', 'ar': 'الكلمات', 'es': 'Palabras' },
                    'sidebar.statistics.chars': { 'en': 'Characters', 'tr': 'Karakterler', 'ar': 'الأحرف', 'es': 'Caracteres' },
                    'sidebar.statistics.sentences': { 'en': 'Sentences', 'tr': 'Cümleler', 'ar': 'الجمل', 'es': 'Frases' },
                    'sidebar.settings.title': { 'en': 'Settings', 'tr': 'Ayarlar', 'ar': 'الإعدادات (القائمة الجانبية)', 'es': 'Configuración (barra lateral)' },
                    'sidebar.settings.autocorrect': { 'en': 'Auto-correct', 'tr': 'Otomatik Düzelt', 'ar': 'التصحيح التلقائي', 'es': 'Autocorrección' },
                    'toolbar.bold': { 'en': 'Bold', 'tr': 'Kalın', 'ar': 'غامق', 'es': 'Negrita' },
                    'toolbar.italic': { 'en': 'Italic', 'tr': 'İtalik', 'ar': 'مائل', 'es': 'Cursiva' },
                    'toolbar.clear': { 'en': 'Clear', 'tr': 'Temizle', 'ar': 'مسح', 'es': 'Limpiar' },
                    'editor.placeholder': { 'en': 'Write or paste your text here...', 'tr': 'Metninizi buraya yazın veya yapıştırın...', 'ar': 'اكتب أو الصق النص هنا...', 'es': 'Escriba o pegue su texto aquí...' },
                    'suggestions.title': { 'en': 'Suggestions', 'tr': 'Öneriler', 'ar': 'الاقتراحات', 'es': 'Sugerencias' },
                    'suggestions.card.incorrect': {'en': 'Incorrect', 'tr': 'Yanlış', 'ar': 'غير صحيح', 'es': 'Incorrecto'},
                    'suggestions.card.correct': {'en': 'Correct', 'tr': 'Doğru', 'ar': 'صحيح', 'es': 'Correcto'},
                    'suggestions.card.apply': {'en': 'Apply', 'tr': 'Uygula', 'ar': 'تطبيق', 'es': 'Aplicar'},
                    'demo.button': { 'en': 'Run Demo', 'tr': 'Demoyu Çalıştır', 'ar': 'تشغيل العرض التوضيحي', 'es': 'Ejecutar Demo' },
                    'settings.modal.title': { 'en': 'Settings', 'tr': 'Ayarlar', 'ar': 'الإعدادات', 'es': 'Configuración' },
                    'settings.language.title': { 'en': 'Language Settings', 'tr': 'Dil Ayarları', 'ar': 'إعدادات اللغة', 'es': 'Configuración de Idioma' },
                    'settings.language.selectLabel': { 'en': 'Interface Language', 'tr': 'Arayüz Dili', 'ar': 'لغة الواجهة', 'es': 'Idioma de la Interfaz' },
                    'settings.language.english': { 'en': 'English', 'tr': 'İngilizce', 'ar': 'الإنجليزية', 'es': 'Inglés' },
                    'settings.language.turkish': { 'en': 'Turkish', 'tr': 'Türkçe', 'ar': 'التركية', 'es': 'Turco' },
                    'settings.language.arabic': { 'en': 'Arabic', 'tr': 'Arapça', 'ar': 'العربية', 'es': 'Árabe' },
                    'settings.language.spanish': { 'en': 'Spanish', 'tr': 'İspanyolca', 'ar': 'الإسبانية', 'es': 'Español' },
                    'settings.appearance.title': { 'en': 'Appearance Settings', 'tr': 'Görünüm Ayarları', 'ar': 'إعدادات المظهر', 'es': 'Configuración de Apariencia' },
                    'settings.appearance.themeLabel': { 'en': 'Theme', 'tr': 'Tema', 'ar': 'المظهر', 'es': 'Tema' },
                    'settings.appearance.theme.light': { 'en': 'Light', 'tr': 'Açık', 'ar': 'فاتح', 'es': 'Claro' },
                    'settings.appearance.theme.dark': { 'en': 'Dark', 'tr': 'Karanlık', 'ar': 'داكن', 'es': 'Oscuro' },
                    'settings.appearance.theme.system': { 'en': 'System', 'tr': 'Sistem', 'ar': 'النظام', 'es': 'Sistema' },
                    'settings.appearance.fontSizeLabel': { 'en': 'Font Size', 'tr': 'Yazı Tipi Boyutu', 'ar': 'حجم الخط', 'es': 'Tamaño de Fuente' },
                    'settings.appearance.fontSize.small': { 'en': 'Small', 'tr': 'Küçük', 'ar': 'صغير', 'es': 'Pequeño' },
                    'settings.appearance.fontSize.normal': { 'en': 'Normal', 'tr': 'Normal', 'ar': 'عادي', 'es': 'Normal' },
                    'settings.appearance.fontSize.large': { 'en': 'Large', 'tr': 'Büyük', 'ar': 'كبير', 'es': 'Grande' },
                    'settings.dictionary.title': { 'en': 'Personal Dictionary', 'tr': 'Kişisel Sözlük', 'ar': 'القاموس الشخصي', 'es': 'Diccionario Personal' },
                    'settings.dictionary.addPlaceholder': { 'en': 'Add word to dictionary...', 'tr': 'Sözlüğe kelime ekle...', 'ar': 'أضف كلمة إلى القاموس...', 'es': 'Añadir palabra al diccionario...' },
                    'settings.dictionary.addButton': { 'en': 'Add', 'tr': 'Ekle', 'ar': 'إضافة', 'es': 'Añadir' },
                    'settings.dictionary.empty': { 'en': 'Your personal dictionary is empty.', 'tr': 'Kişisel sözlüğünüz boş.', 'ar': 'قاموسك الشخصي فارغ.', 'es': 'Su diccionario personal está vacío.' }
                };
                
                if (!this.textEditor || !this.editorOverlay || !this.settingsModal || !this.languageSelect || !this.openSettingsModalBtn || !this.closeSettingsModalBtn) {
                    console.error("Editor or Overlay element not found!");
                    return;
                }
                // Ensure the elements have the respective classes for styling
                this.textEditor.classList.add('text-editor'); 
                this.editorOverlay.classList.add('text-editor-overlay');
                this.systemThemeListener = null; 

                // Personal Dictionary Elements
                this.personalDictionaryWordInput = document.getElementById('personalDictionaryWordInput');
                this.addWordToDictionaryBtn = document.getElementById('addWordToDictionaryBtn');
                this.personalDictionaryList = document.getElementById('personalDictionaryList');
                this.personalDictionary = [];
                this.statsElements = {
                    errors: document.querySelector('[data-translate-key="sidebar.statistics.errors"] + p'),
                    corrections: document.querySelector('[data-translate-key="sidebar.statistics.corrections"] + p'),
                    words: document.createElement('p'), // Will be added to DOM
                    chars: document.createElement('p'), // Will be added to DOM
                    sentences: document.createElement('p') // Will be added to DOM
                };
                this.currentMode = 'standard'; // Default mode
                this.activeModeDisplay = null; // Will be set up in setupEventListeners or initializeApp
            }

            // Theme and Appearance Methods (applyTheme, updateManualDarkStyles, loadPreferredTheme, applyFontSize, loadPreferredFontSize)
            // ... (These methods remain unchanged from the previous step) ...
            applyTheme(theme) {
                const bodyClassList = document.body.classList;
                const htmlClassList = document.documentElement.classList;

                // Remove old theme button active states
                document.querySelectorAll('.theme-option-btn').forEach(btn => btn.classList.remove('active-theme-btn'));

                if (theme === 'system') {
                    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                        htmlClassList.add('dark');
                        // bodyClassList.add('dark'); // Tailwind uses class on <html> for dark mode
                    } else {
                        htmlClassList.remove('dark');
                        // bodyClassList.remove('dark');
                    }
                    localStorage.setItem('preferredTheme', 'system');
                    document.querySelector('button[data-theme-option="system"]').classList.add('active-theme-btn');
                } else if (theme === 'dark') {
                    htmlClassList.add('dark');
                    // bodyClassList.add('dark');
                    localStorage.setItem('preferredTheme', 'dark');
                     document.querySelector('button[data-theme-option="dark"]').classList.add('active-theme-btn');
                } else { // 'light'
                    htmlClassList.remove('dark');
                    // bodyClassList.remove('dark');
                    localStorage.setItem('preferredTheme', 'light');
                     document.querySelector('button[data-theme-option="light"]').classList.add('active-theme-btn');
                }
                // Manually update classes for elements not covered by Tailwind's html.dark selector if needed
                // This part might be redundant if all elements correctly use Tailwind's dark: variants
                this.updateManualDarkStyles(htmlClassList.contains('dark'));
                this.applyTranslations(this.languageSelect.value || 'en'); // Re-apply translations for potential theme-specific text changes
            }

            updateManualDarkStyles(isDarkMode) {
                // This function ensures elements not perfectly handled by Tailwind's html.dark
                // (like suggestion cards or specific text that had .dark class toggled) are updated.
                // Most of this should ideally be handled by Tailwind's `dark:` prefix.
                const elementsToToggle = [
                    ...document.querySelectorAll('header, aside, .mode-card, .stats-grid div, .editor-toolbar, textarea, .suggestion-card, #demoBtn, label[for="fileUpload"], #exportBtn, #openSettingsModalBtn, #clearBtn')
                ];
                 elementsToToggle.forEach(el => {
                    if(isDarkMode) el.classList.add('dark'); else el.classList.remove('dark');
                });
                 document.querySelectorAll('aside h2, aside .text-gray-800, .stats-grid .text-gray-600, .stats-grid .text-gray-800, .editor-toolbar button, main h2, #settingsModal, .theme-option-btn, .fontsize-option-btn, #addWordToDictionaryBtn').forEach(el => {
                     if(isDarkMode) el.classList.add('dark-text-override'); else el.classList.remove('dark-text-override');
                 });

                // Update caret color after theme change
                const currentThemeTextColor = isDarkMode ? '#f3f4f6' : '#1f2937';
                if (this.textEditor.style.color !== 'transparent') { 
                    this.textEditor.style.color = currentThemeTextColor; 
                }
                this.textEditor.style.caretColor = currentThemeTextColor;
            }


            loadPreferredTheme() {
                const storedTheme = localStorage.getItem('preferredTheme') || 'system';
                this.applyTheme(storedTheme);

                // Remove existing listener before adding a new one
                if (this.systemThemeListener) {
                    window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', this.systemThemeListener);
                }
                
                this.systemThemeListener = () => {
                    if (localStorage.getItem('preferredTheme') === 'system') {
                        this.applyTheme('system');
                    }
                };
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', this.systemThemeListener);
            }

            applyFontSize(size) {
                const bodyClassList = document.body.classList;
                // Remove existing font size classes
                bodyClassList.remove('font-size-small', 'font-size-normal', 'font-size-large');
                // Add the new font size class
                bodyClassList.add(`font-size-${size}`);
                localStorage.setItem('preferredFontSize', size);

                // Update active button
                document.querySelectorAll('.fontsize-option-btn').forEach(btn => btn.classList.remove('active-fontsize-btn'));
                document.querySelector(`button[data-fontsize-option="${size}"]`).classList.add('active-fontsize-btn');
            }

            loadPreferredFontSize() {
                const storedSize = localStorage.getItem('preferredFontSize') || 'normal';
                this.applyFontSize(storedSize);
            }

            applyTranslations(lang) {
                if (!this.translations) {
                    console.error("Translations not loaded.");
                    return;
                }
                document.querySelectorAll('[data-translate-key]').forEach(element => {
                    const key = element.dataset.translateKey;
                    const translationSet = this.translations[key];
                    if (translationSet && translationSet[lang]) {
                        if (key === 'editor.placeholder' && element.tagName === 'TEXTAREA') {
                            element.placeholder = translationSet[lang];
                        } else if (element.tagName === 'TITLE') {
                            document.title = translationSet[lang]; // Special handling for document title
                        } else if (element.hasAttribute('title') && key.endsWith('.tooltip')) { // For tooltips stored in title
                             element.title = translationSet[lang];
                        }
                        else {
                            // Handle cases where text is inside a child span, e.g. for buttons with icons
                            const textSpan = element.querySelector('span[data-translate-key]');
                            if (textSpan && textSpan.dataset.translateKey === key) {
                                 textSpan.innerText = translationSet[lang];
                            } else if (!element.querySelector('span[data-translate-key]')) { 
                                // Only update if it's not a container for a more specific translated span
                                element.innerText = translationSet[lang];
                            }
                        }
                    } else {
                        console.warn(`No translation found for key: ${key}, lang: ${lang}`);
                    }
                });

                if (lang === 'ar') {
                    document.documentElement.dir = 'rtl';
                    document.documentElement.lang = 'ar';
                } else {
                    document.documentElement.dir = 'ltr';
                    document.documentElement.lang = lang;
                }
            }

            loadPreferredLanguage() {
                const storedLang = localStorage.getItem('preferredLanguage');
                const initialLang = storedLang || 'en'; // Default to English
                if (this.languageSelect) {
                    this.languageSelect.value = initialLang;
                }
                this.applyTranslations(initialLang);
            }
            
            escapeHTML(str) {
                const div = document.createElement('div');
                div.appendChild(document.createTextNode(str));
                return div.innerHTML;
            }

            highlightErrors(text) {
                if (!this.textEditor || !this.editorOverlay) {
                    console.error("highlightErrors: editor or overlay not found");
                    return;
                }

                // 2. Clear Previous Highlighting
                this.editorOverlay.innerHTML = '';

                // 3. Prepare for Overlay
                this.textEditor.style.color = 'transparent'; // Make underlying text invisible
                // Set caret color based on current theme, ensuring it's visible
                this.textEditor.style.caretColor = document.body.classList.contains('dark') ? '#f3f4f6' : '#1f2937'; // text-gray-100 or text-gray-800
                
                this.editorOverlay.classList.remove('hidden');

                // Match font styles from textEditor to editorOverlay for alignment.
                // Padding and border are largely handled by existing Tailwind classes and specific CSS.
                const computedStyles = window.getComputedStyle(this.textEditor);
                this.editorOverlay.style.fontFamily = computedStyles.fontFamily;
                this.editorOverlay.style.fontSize = computedStyles.fontSize;
                this.editorOverlay.style.lineHeight = computedStyles.lineHeight;
                // this.editorOverlay.style.padding = computedStyles.padding; // Covered by p-4
                // this.editorOverlay.style.borderWidth = computedStyles.borderWidth; // Overlay border is transparent

                // 4. Construct Highlighted Content
                // Ensure text is not null or undefined
                const currentText = text === null || typeof text === 'undefined' ? this.textEditor.value : text;

                const sortedErrors = [...this.errors].sort((a, b) => a.start - b.start);
                let currentIndex = 0;
                let highlightedHTML = '';

                for (const error of sortedErrors) {
                    // Append non-error text
                    if (error.start > currentIndex) {
                        highlightedHTML += this.escapeHTML(currentText.substring(currentIndex, error.start));
                    }
                    // Append error span
                    const errorText = this.escapeHTML(currentText.substring(error.start, error.end));
                    // Add data-suggestion and data-error-index attributes
                    // Using actual index from forEach for data-error-index is safer
                    const errorIndex = this.errors.indexOf(error); // Keep this if errors array can change during iteration (it doesn't here)
                    highlightedHTML += `<span class="error-${error.type}" data-error-index="${errorIndex}" data-suggestion="${this.escapeHTML(error.suggestion || '')}">${errorText}</span>`;
                    currentIndex = error.end;
                }
                // Append remaining text
                if (currentIndex < currentText.length) {
                    highlightedHTML += this.escapeHTML(currentText.substring(currentIndex));
                }
                
                this.editorOverlay.innerHTML = highlightedHTML;

                // 5. Scroll Synchronization (initial sync after populating)
                this.synchronizeScroll();
            }
            
            synchronizeScroll() {
                if (!this.textEditor || !this.editorOverlay) return;
                this.editorOverlay.scrollTop = this.textEditor.scrollTop;
                this.editorOverlay.scrollLeft = this.textEditor.scrollLeft;
            }

            // Basic vowel harmony check (naive)
            hasVowelHarmonyViolation(word) {
                // Simplified: checks if common incorrect patterns exist.
                // Removed 'kitabım', 'evlerden', 'masaüzerinde' (problematic/correct examples)
                const violations = {
                    "gözlar": "gözler" // Example: "göz" (front vowel root) + "-lar" (back vowel suffix)
                    // Add other clear, simple violation patterns if needed
                };
                return violations[word.toLowerCase()] ? true : false;
            }

            suggestVowelHarmonyFix(word) {
                const fixes = {
                    "gözlar": "gözler"
                    // Add other fixes corresponding to violations
                };
                return fixes[word.toLowerCase()] || word; // Return original word if no fix found
            }

            findErrors(text) {
                const foundErrors = [];
                const words = text.split(/(\b|\s+)/); 
                let currentIndex = 0;
                for (const segment of words) {
                    const lowerSegment = segment.toLowerCase();
                    // Skip spaces and punctuation for harmony checks, but include in index
                    if (segment.trim() === '') {
                         currentIndex += segment.length;
                         continue;
                    }

                    if (lowerSegment === "yanlis") { 
                        foundErrors.push({ 
                            start: currentIndex, end: currentIndex + segment.length, 
                            type: 'spelling', original: segment, suggestion: 'yanlış', correct: 'yanlış'
                        });
                    } else if (lowerSegment === "hatali") { 
                        foundErrors.push({ 
                            start: currentIndex, end: currentIndex + segment.length, 
                            type: 'grammar', original: segment, suggestion: 'hatalı', correct: 'hatalı'
                        });
                    } else if (lowerSegment === "belki") { 
                         foundErrors.push({
                            start: currentIndex, end: currentIndex + segment.length,
                            type: 'style', original: segment, suggestion: 'olasılıkla', correct: 'olasılıkla'
                        });
                    } 
                    
                    // Vowel Harmony Check for the current segment (word)
                    if (this.hasVowelHarmonyViolation(segment)) {
                        const suggestion = this.suggestVowelHarmonyFix(segment);
                        foundErrors.push({
                            start: currentIndex,
                            end: currentIndex + segment.length,
                            type: 'harmony', // New error type
                            original: segment,
                            suggestion: suggestion,
                            correct: suggestion 
                        });
                    }
                    currentIndex += segment.length;
                }
                return foundErrors;
            }

            analyzeText() {
                if (!this.textEditor) return;
                const text = this.textEditor.value;
                // console.log("Analyzing text:", text.substring(0, 30) + "...");
                this.errors = this.findErrors(text); // Use findErrors method
                
                // console.log("Errors found:", this.errors.length);
                this.highlightErrors(text); // Call highlighting after analysis
                this.updateSuggestionCards(); // Simulate updating suggestion UI
                this.updateStatistics();      // Simulate updating stats UI
            }

            applyCorrection(errorIndex) {
                if (!this.textEditor || errorIndex < 0 || errorIndex >= this.errors.length) {
                    console.error("Invalid errorIndex or editor not found for applyCorrection.");
                    return;
                }
                const error = this.errors[errorIndex];
                if (!error.correct) {
                    console.warn("No correction available for this error.");
                    return;
                }

                const currentValue = this.textEditor.value;
                const beforeError = currentValue.substring(0, error.start);
                const afterError = currentValue.substring(error.end);
                
                this.textEditor.value = beforeError + error.correct + afterError;
                console.log(`Applied correction for "${error.original}" -> "${error.correct}"`);

                // Store cursor position
                const cursorPosition = error.start + error.correct.length;

                this.analyzeText(); // Re-analyze the text

                // Restore cursor position
                this.textEditor.focus();
                this.textEditor.setSelectionRange(cursorPosition, cursorPosition);
            }

            applySuggestion(from, to) {
                if (!this.textEditor || !from || !to) {
                    console.error("Invalid parameters for applySuggestion.");
                    return;
                }
                const currentValue = this.textEditor.value;
                // Case-insensitive replacement
                const regex = new RegExp(this.escapeRegExp(from), 'gi');
                const newValue = currentValue.replace(regex, to);

                if (currentValue !== newValue) {
                    this.textEditor.value = newValue;
                    console.log(`Applied suggestion: replaced all "${from}" with "${to}"`);
                    this.analyzeText(); // Re-analyze
                } else {
                    console.log(`Suggestion: "${from}" not found for replacement.`);
                }
            }

            escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
            }
            
            updateSuggestionCards() {
                // Simulate updating suggestion cards UI
                // In a real app, this would re-render the suggestion cards based on this.errors
                const suggestionsContainer = document.querySelector('.suggestions-container');
                if (suggestionsContainer) {
                    suggestionsContainer.innerHTML = ''; // Clear old suggestions
                    this.errors.forEach((error, index) => {
                        if(error.suggestion) {
                            const card = document.createElement('div');
                            card.className = 'suggestion-card p-3 rounded-lg shadow flex justify-between items-center';
                            // Add appropriate dark mode classes if body.dark
                            if (document.body.classList.contains('dark')) {
                                card.classList.add('dark'); // This will trigger .dark .suggestion-card styles
                            }
                            card.innerHTML = `
                                <div>
                                    <p class="text-sm ${document.body.classList.contains('dark') ? 'text-gray-300' : 'text-gray-600'}">Incorrect: <span class="${error.type === 'spelling' ? 'text-red-500' : 'text-yellow-500'}">${this.escapeHTML(error.original)}</span></p>
                                    <p class="text-sm ${document.body.classList.contains('dark') ? 'text-gray-300' : 'text-gray-600'}">Correct: <span class="text-green-500">${this.escapeHTML(error.suggestion)}</span></p>
                                </div>
                                <button class="text-sm" onclick="grammarChecker.applyCorrection(${index})">Apply</button>
                            `;
                            // CSS should handle text color for p tags inside .dark .suggestion-card
                            suggestionsContainer.appendChild(card);
                        }
                    });
                }
                // console.log("Simulating: Suggestion cards updated. Count:", this.errors.filter(e => e.suggestion).length);
            }

            updateStatistics() {
                const text = this.textEditor.value;
                const errorsCount = this.errors.length;
                // For "corrections", this is just a placeholder as we don't track applied corrections directly.
                // Let's assume for now it's related to number of suggestions or some other metric.
                // For this task, we'll keep it simple and not change it.
                const correctionsCount = document.querySelector('[data-translate-key="sidebar.statistics.corrections"] + p').textContent;


                const charsCount = text.length;
                const wordsCount = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
                const sentencesCount = text.trim() === '' ? 0 : (text.match(/[.!?]+/g) || []).length;
                 // If the text ends with a character that is not a sentence terminator,
                 // and the text is not empty, we count one more sentence.
                if (text.trim() !== '' && !/[.!?]$/.test(text.trim())) {
                    // sentencesCount++; // This logic might be too simple, but for now.
                    // A more robust sentence count might be complex. Let's use a simpler approach for now.
                }


                if (this.statsElements.errors) this.statsElements.errors.textContent = errorsCount;
                // this.statsElements.corrections.textContent = 0; // Example, actual logic might differ

                // Update or add word, char, sentence count elements
                this.updateOrCreateStatElement('words', 'sidebar.statistics.words', wordsCount, 0);
                this.updateOrCreateStatElement('chars', 'sidebar.statistics.chars', charsCount, 1);
                this.updateOrCreateStatElement('sentences', 'sidebar.statistics.sentences', sentencesCount, 2); // Example position
                
                // Ensure translations are applied to new stat labels
                this.applyTranslations(this.languageSelect.value || 'en');
            }
            
            updateOrCreateStatElement(key, translateKey, value, gridOrder) {
                const statsGrid = document.querySelector('.stats-grid');
                if (!statsGrid) return;

                let statDiv = this.statsElements[key + 'Div'];
                if (!statDiv) {
                    statDiv = document.createElement('div');
                    statDiv.className = 'p-3 rounded-lg shadow';
                    if (document.body.classList.contains('dark')) { // Ensure new elements respect dark mode
                        statDiv.classList.add('dark');
                    }
                    statDiv.style.order = gridOrder; // For visual ordering

                    const label = document.createElement('p');
                    label.className = 'text-sm text-gray-600';
                    label.dataset.translateKey = translateKey;
                    // label.textContent will be set by applyTranslations

                    const valueP = document.createElement('p');
                    valueP.className = 'text-2xl font-bold text-gray-800';
                     if (document.body.classList.contains('dark')) { // Ensure new elements respect dark mode
                        valueP.classList.add('dark-text-override'); // Match existing value styling
                    }

                    statDiv.appendChild(label);
                    statDiv.appendChild(valueP);
                    statsGrid.appendChild(statDiv);

                    this.statsElements[key + 'Div'] = statDiv;
                    this.statsElements[key + 'Label'] = label;
                    this.statsElements[key + 'ValueP'] = valueP;
                }
                this.statsElements[key + 'ValueP'].textContent = value;
            }


            clearText() {
                if (!this.textEditor) return;
                this.textEditor.value = '';
                this.analyzeText(); // This will update errors, suggestions, and stats
                this.textEditor.focus();
                console.log("Text editor cleared and re-analyzed.");
            }

            undoLastAction() {
                if (!this.textEditor) return;
                console.log("Attempting to undo last action...");
                // The effectiveness of execCommand('undo') can be limited, especially for programmatic changes.
                document.execCommand('undo', false, null); 
                this.analyzeText(); // Re-analyze text after undo attempt
                // Focus editor to see the result and ensure undo stack is on the editor
                this.textEditor.focus(); 
            }

            setupEventListeners() {
                if (!this.textEditor) return;
                // console.log("setupEventListeners: Adding input and scroll listeners");
                this.textEditor.addEventListener('input', () => this.analyzeText());
                this.textEditor.addEventListener('scroll', () => this.synchronizeScroll());

                if (this.languageSelect) {
                    this.languageSelect.addEventListener('change', (event) => {
                        const selectedLang = event.target.value;
                        this.applyTranslations(selectedLang);
                        localStorage.setItem('preferredLanguage', selectedLang);
                    });
                }

                if (this.openSettingsModalBtn && this.settingsModal) {
                    this.openSettingsModalBtn.addEventListener('click', () => {
                        this.settingsModal.classList.remove('hidden');
                    });
                }

                if (this.closeSettingsModalBtn && this.settingsModal) {
                    this.closeSettingsModalBtn.addEventListener('click', () => {
                        this.settingsModal.classList.add('hidden');
                    });
                }
                
                if (this.settingsModal) {
                    this.settingsModal.addEventListener('click', (event) => {
                        if (event.target === this.settingsModal) { 
                            this.settingsModal.classList.add('hidden');
                        }
                    });
                }

                // Theme button listeners
                document.querySelectorAll('.theme-option-btn').forEach(button => {
                    button.addEventListener('click', (event) => {
                        const theme = event.currentTarget.dataset.themeOption;
                        this.applyTheme(theme);
                    });
                });

                // Font size button listeners
                document.querySelectorAll('.fontsize-option-btn').forEach(button => {
                    button.addEventListener('click', (event) => {
                        const size = event.currentTarget.dataset.fontsizeOption;
                        this.applyFontSize(size);
                    });
                });

                // Personal Dictionary Event Listeners
                if (this.addWordToDictionaryBtn) {
                    this.addWordToDictionaryBtn.addEventListener('click', () => this.addWordToPersonalDictionary());
                }
                if (this.personalDictionaryWordInput) {
                    this.personalDictionaryWordInput.addEventListener('keypress', (event) => {
                        if (event.key === 'Enter') {
                            event.preventDefault(); // Prevent form submission if it were in a form
                            this.addWordToPersonalDictionary();
                        }
                    });
                }
            }

            loadPersonalDictionary() {
                const storedDictionary = localStorage.getItem('personalDictionary');
                if (storedDictionary) {
                    try {
                        this.personalDictionary = JSON.parse(storedDictionary);
                    } catch (e) {
                        console.error("Error parsing personal dictionary from localStorage:", e);
                        this.personalDictionary = [];
                    }
                }
                this.renderPersonalDictionary();
            }

            savePersonalDictionary() {
                localStorage.setItem('personalDictionary', JSON.stringify(this.personalDictionary));
            }

            renderPersonalDictionary() {
                if (!this.personalDictionaryList) return;
                this.personalDictionaryList.innerHTML = ''; // Clear current list

                if (this.personalDictionary.length === 0) {
                    const emptyMsgKey = this.personalDictionaryList.dataset.translateKey || 'settings.dictionary.empty';
                    const lang = this.languageSelect ? this.languageSelect.value : 'en';
                    this.personalDictionaryList.textContent = (this.translations[emptyMsgKey] && this.translations[emptyMsgKey][lang]) || 'Your personal dictionary is empty.';
                    return;
                }

                this.personalDictionary.forEach(word => {
                    const item = document.createElement('div');
                    item.className = 'flex justify-between items-center py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded';
                    
                    const wordSpan = document.createElement('span');
                    wordSpan.textContent = word;
                    wordSpan.className = 'text-sm text-gray-700 dark:text-gray-300';
                    
                    const removeBtn = document.createElement('button');
                    removeBtn.innerHTML = '<i class="fas fa-times text-red-500 hover:text-red-700"></i>';
                    removeBtn.className = 'p-1';
                    removeBtn.setAttribute('aria-label', `Remove ${word}`);
                    removeBtn.onclick = () => this.removeWordFromPersonalDictionary(word);
                    
                    item.appendChild(wordSpan);
                    item.appendChild(removeBtn);
                    this.personalDictionaryList.appendChild(item);
                });
            }

            addWordToPersonalDictionary() {
                if (!this.personalDictionaryWordInput) return;
                const word = this.personalDictionaryWordInput.value.trim();

                if (word === '') {
                    // Optionally provide feedback: alert("Word cannot be empty.");
                    console.warn("Attempted to add empty word to dictionary.");
                    return;
                }
                // Case-insensitive check for existence
                if (this.personalDictionary.some(entry => entry.toLowerCase() === word.toLowerCase())) {
                    // Optionally provide feedback: alert(`"${word}" is already in the dictionary.`);
                    console.warn(`Word "${word}" already in dictionary.`);
                    this.personalDictionaryWordInput.value = ''; // Clear input even if word exists
                    return;
                }

                this.personalDictionary.push(word);
                this.personalDictionary.sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase())); // Case-insensitive sort
                this.savePersonalDictionary();
                this.renderPersonalDictionary();
                this.personalDictionaryWordInput.value = '';
                this.analyzeText(); // Re-analyze text as new dictionary word might affect errors
            }

            removeWordFromPersonalDictionary(wordToRemove) {
                this.personalDictionary = this.personalDictionary.filter(word => word !== wordToRemove);
                this.savePersonalDictionary();
                this.renderPersonalDictionary();
                this.analyzeText(); // Re-analyze text as removing dictionary word might affect errors
            }


            findErrors(text) {
                const foundErrors = [];
                const words = text.split(/(\b|\s+)/); 
                let currentIndex = 0;
                for (const segment of words) {
                    const lowerSegment = segment.toLowerCase();
                    if (segment.trim() === '') {
                         currentIndex += segment.length;
                         continue;
                    }

                    // Check against personal dictionary first for spelling errors
                    if (this.personalDictionary.some(dictWord => dictWord.toLowerCase() === lowerSegment)) {
                        currentIndex += segment.length;
                        continue; // Skip if word is in personal dictionary
                    }

                    if (lowerSegment === "yanlis") { 
                        foundErrors.push({ 
                            start: currentIndex, end: currentIndex + segment.length, 
                            type: 'spelling', original: segment, suggestion: 'yanlış', correct: 'yanlış'
                        });
                    } else if (lowerSegment === "hatali") { 
                        foundErrors.push({ 
                            start: currentIndex, end: currentIndex + segment.length, 
                            type: 'grammar', original: segment, suggestion: 'hatalı', correct: 'hatalı'
                        });
                    } else if (lowerSegment === "belki") { 
                         foundErrors.push({
                            start: currentIndex, end: currentIndex + segment.length,
                            type: 'style', original: segment, suggestion: 'olasılıkla', correct: 'olasılıkla'
                        });
                    } 
                    
                    // Vowel Harmony Check for the current segment (word)
                    if (this.hasVowelHarmonyViolation(segment)) {
                        const suggestion = this.suggestVowelHarmonyFix(segment);
                        foundErrors.push({
                            start: currentIndex,
                            end: currentIndex + segment.length,
                            type: 'harmony', 
                            original: segment,
                            suggestion: suggestion,
                            correct: suggestion 
                        });
                    }
                    currentIndex += segment.length;
                }
                return foundErrors;
            }


            loadDemoText() {
                if (!this.textEditor) return;
                const demoText = "Bu bir örnek metindir. Bazi yanlis ifadeler, hatali gramer kullanimi ve gözlar gibi uyum sorunlari içerebilir. Belki de degistirmek istersin.";
                // console.log("Loading demo text:", demoText);
                this.textEditor.value = demoText;
            }

            initializeApp() {
                console.log("TurkishGrammarChecker Initializing...");
                this.loadPreferredLanguage(); 
                this.loadPreferredTheme();    
                this.loadPreferredFontSize(); 
                this.loadPersonalDictionary(); // Load personal dictionary
                this.setupEventListeners(); 
                this.loadDemoText();      
                this.analyzeText(); 
                
                // Initial caret/text color update after theme and text analysis
                const isCurrentlyDark = document.documentElement.classList.contains('dark');
                const currentThemeTextColor = isCurrentlyDark ? '#f3f4f6' : '#1f2937';
                if (this.textEditor.style.color !== 'transparent') { 
                    this.textEditor.style.color = currentThemeTextColor; 
                }
                this.textEditor.style.caretColor = currentThemeTextColor; 
            }
        }
        
        let grammarChecker; 
        document.addEventListener('DOMContentLoaded', () => {
            grammarChecker = new GrammarChecker('editor', 'editorOverlay');
            grammarChecker.initializeApp(); 

            // Setup Clear button listener (moved from direct manipulation to class method)
            const clearBtn = document.getElementById('clearBtn');
            if (clearBtn) {
                clearBtn.addEventListener('click', () => {
                    grammarChecker.clearText();
                });
                 // Ensure this new button also toggles dark mode style (if it's not a general .button-like.dark rule)
                if (darkModeToggle) { // Assuming darkModeToggle is still relevant for other buttons
                    darkModeToggle.addEventListener('click', () => {
                         setTimeout(() => {
                            if (document.body.classList.contains('dark')) {
                                clearBtn.classList.add('dark');
                            } else {
                                clearBtn.classList.remove('dark');
                            }
                        },0);
                    });
                     if (document.body.classList.contains('dark')) clearBtn.classList.add('dark');
                }
            }


            // Old darkModeToggle related code is removed from here as it's handled by settings modal for theme
            // However, the darkModeToggle variable might still be used by other buttons if they weren't updated
            // It's better to let the applyTheme method handle general dark mode class toggling for buttons if possible
            // or ensure each button's dark mode state is updated when the theme changes.
            // For now, the specific dark mode toggling for clearBtn is kept as per previous structure.
            const darkModeToggle = document.getElementById('darkModeToggle'); // This ID is for the old button, it was removed.
                                                                              // This should be removed or adapted if general button dark mode styling is handled by applyTheme.
                                                                              // For now, assuming it's a mistake and removing it for clearBtn specific logic.
                                                                              // The actual theme change will be handled by applyTheme.

        });
    </script>
</body>
</html>