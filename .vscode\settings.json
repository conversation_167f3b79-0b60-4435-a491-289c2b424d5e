{"python.analysis.typeCheckingMode": "basic", "cmake.configureArgs": [], "C_Cpp.errorSquiggles": "disabled", "cSpell.words": ["Aleykum", "anında", "Aracı", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Casualize", "commuincation", "De<PERSON>ch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Español", "Français", "grammarpro", "gweight", "hackathons", "<PERSON><PERSON><PERSON>", "Italiano", "iterature", "jsonify", "Kontrol", "kruskal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "mstidx", "Nederlands", "pakage", "pnode", "Poping", "Primsalgorithm", "Prototip", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Stil", "<PERSON><PERSON><PERSON><PERSON>", "tedge", "tnode", "tooltiptext", "Türkçe", "tweight", "veya", "yapıştırın", "yazın", "السعودية", "السورية", "العربية", "المصرية"], "cSpell.language": "en,en-US", "python.testing.unittestArgs": ["-v", "-s", "./Python Projects", "-p", "*test.py"], "python.testing.pytestEnabled": false, "python.testing.unittestEnabled": true}