import { useLanguageContext } from './LanguageProvider';
import { useAppStore } from '../lib/store';
import { useG<PERSON>marChe<PERSON> } from '../lib/features/grammar/useGrammarChecker';
import { Button } from './ui/button';

export function Sidebar() {
  const { t } = useLanguageContext();
  const { writingStats } = useAppStore();
  const { errors, analyzing } = useGrammarChecker();
  
  // Writing modes
  const writingModes = [
    { id: 'casual', icon: '📝', label: t('casual') },
    { id: 'formal', icon: '🏢', label: t('formal') },
    { id: 'academic', icon: '🎓', label: t('academic') },
    { id: 'creative', icon: '🎨', label: t('creative') },
    { id: 'business', icon: '💼', label: t('business') }
  ];
  
  // Recent prompts
  const { recentPrompts } = useAppStore();
  
  return (
    <div className="lg:col-span-1 space-y-6">
      {/* Writing Tools */}
      <div>
        <h3 className="text-xl font-semibold mb-3">{t('writingTools')}</h3>
        
        {/* Writing Mode */}
        <div className="mb-4">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-2">
            {t('writingMode')}
          </label>
          <div className="grid grid-cols-2 gap-2">
            {writingModes.map((mode) => (
              <button
                key={mode.id}
                className="flex items-center space-x-2 p-2 border rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <span>{mode.icon}</span>
                <span>{mode.label}</span>
              </button>
            ))}
          </div>
        </div>
        
        {/* Import Document */}
        <div className="mb-4">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-2">
            {t('importDocument')}
          </label>
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md p-4 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('dropFilesHere')} <span className="text-primary cursor-pointer">{t('browse')}</span>
            </p>
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="mb-4">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-2">
            {t('quickActions')}
          </label>
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" id="checkGrammarBtn">
              {t('checkAll')}
            </Button>
            <Button variant="outline" id="aiRewriteBtn">
              {t('aiRewrite')}
            </Button>
            <Button variant="outline" id="exportBtn">
              {t('export')}
            </Button>
            <Button variant="outline" id="clearBtn">
              {t('clear')}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Writing Stats */}
      <div className="writing-stats bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <h4 className="text-lg font-medium mb-3">{t('writingStatistics')}</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{t('words')}</div>
            <div className="text-2xl font-semibold">{writingStats.words}</div>
          </div>
          <div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{t('characters')}</div>
            <div className="text-2xl font-semibold">{writingStats.characters}</div>
          </div>
          <div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{t('errors')}</div>
            <div className="text-2xl font-semibold" id="errorCount">{errors.length}</div>
          </div>
          <div>
            <div className="text-sm text-gray-500 dark:text-gray-400">{t('score')}</div>
            <div className="text-2xl font-semibold">{writingStats.score}</div>
          </div>
        </div>
      </div>
      
      {/* Suggestions */}
      <div>
        <h4 className="text-lg font-medium mb-3">{t('suggestions')}</h4>
        <div id="suggestionsList" className="space-y-3">
          {analyzing ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm mt-2">{t('analyzing')}</p>
            </div>
          ) : errors.length === 0 ? (
            <div className="text-sm text-green-600 text-center py-8">
              <i className="fas fa-check-circle text-2xl mb-2"></i>
              <p>{t('noIssues')}</p>
            </div>
          ) : (
            errors.slice(0, 5).map((error, index) => (
              <div key={index} className="suggestion-card border border-gray-200 dark:border-gray-700 rounded p-3 hover:shadow-md transition-shadow cursor-pointer">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      <span className={`inline-block w-2 h-2 rounded-full ${
                        error.type === 'spelling' ? 'bg-red-500' :
                        error.type === 'grammar' ? 'bg-blue-500' :
                        error.type === 'style' ? 'bg-green-500' :
                        'bg-yellow-500'
                      } mr-2`}></span>
                      <span className="text-sm font-medium capitalize">{error.type}</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{error.explanation}</p>
                    <div className="text-xs text-gray-500 dark:text-gray-500">
                      Suggestions: {error.suggestions.slice(0, 2).join(', ')}
                    </div>
                  </div>
                  <button className="text-primary hover:text-primary-dark text-sm font-medium ml-2">
                    Fix
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
      
      {/* Recent AI Prompts */}
      <div>
        <h4 className="text-lg font-medium mb-3">{t('recentAiPrompts')}</h4>
        <div className="space-y-2">
          {recentPrompts.map((prompt, index) => (
            <div 
              key={index}
              className="text-sm p-2 bg-gray-50 dark:bg-gray-800 rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              {prompt}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
