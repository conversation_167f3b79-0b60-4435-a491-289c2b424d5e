/**
 * Integration module for GrammarPro application fixes
 * Combines all individual fixes and initializes them properly
 */

// Main initialization function to be called when the page loads
function initializeAllFixes() {
  console.log('Initializing GrammarPro application fixes...');
  
  // Initialize state if not already done
  initializeState();
  
  // Add enhanced CSS variables
  addEnhancedCSSVariables();
  
  // Initialize features in the correct order
  initializeLanguage();
  initializeTheme();
  initializeGrammarAndSpelling();
  initializeAppearanceSettings();
  initializeDictionary();
  
  // Set up global event listeners
  setupGlobalEventListeners();
  
  console.log('All fixes initialized successfully');
}

// Initialize application state with defaults
function initializeState() {
  if (typeof state === 'undefined') {
    window.state = {
      currentLanguage: 'en',
      theme: 'light',
      fontSize: 'normal',
      rtlEnabled: false,
      settings: {
        aiOnSelection: true,
        quickReplies: true,
        promptHistory: true,
        grammarCheck: true,
        spellCheck: true,
        styleCheck: true,
        toneDetection: true,
        plagiarismCheck: false,
        realTimeCheck: true,
        saveHistory: true,
        analytics: false,
        highContrast: false,
        rtlSupport: false
      },
      personalDictionary: ['grammarpro', 'multilingual'],
      recentPrompts: [
        "Make this more formal",
        "Improve clarity",
        "Add more details"
      ],
      writingStats: {
        words: 0,
        characters: 0,
        errors: 0,
        score: 100
      }
    };
  }
  
  // Load saved settings
  loadSettings();
}

// Add enhanced CSS variables for better theming
function addEnhancedCSSVariables() {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    :root {
      --primary-color: #059669;
      --secondary-color: #0f766e;
      --accent-color: #06b6d4;
      --error-color: #dc2626;
      --warning-color: #d97706;
      --success-color: #16a34a;
      --text-primary: #1f2937;
      --text-secondary: #6b7280;
      --bg-primary: #ffffff;
      --bg-secondary: #f9fafb;
      --border-color: #e5e7eb;
    }

    [data-theme="dark"] {
      --text-primary: #f9fafb;
      --text-secondary: #d1d5db;
      --bg-primary: #111827;
      --bg-secondary: #1f2937;
      --border-color: #374151;
      --primary-color: #10b981;
      --secondary-color: #14b8a6;
      --accent-color: #22d3ee;
      --error-color: #ef4444;
      --warning-color: #f59e0b;
      --success-color: #22c55e;
    }
  `;
  document.head.appendChild(styleElement);
}

// Set up global event listeners
function setupGlobalEventListeners() {
  // Global keyboard shortcuts
  document.addEventListener('keydown', handleGlobalKeyboard);
  
  // Settings button
  const settingsBtn = document.getElementById('settingsBtn');
  if (settingsBtn) {
    settingsBtn.addEventListener('click', openSettings);
  }
  
  // Help button
  const helpBtn = document.getElementById('helpBtn');
  if (helpBtn) {
    helpBtn.addEventListener('click', openHelp);
  }
  
  // Close buttons for modals
  const closeSettings = document.getElementById('closeSettings');
  if (closeSettings) {
    closeSettings.addEventListener('click', () => {
      document.getElementById('settingsModal').classList.add('hidden');
    });
  }
  
  const closeHelp = document.getElementById('closeHelp');
  if (closeHelp) {
    closeHelp.addEventListener('click', () => {
      document.getElementById('helpModal').classList.add('hidden');
    });
  }
}

// Initialize the application when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initializeAllFixes);
