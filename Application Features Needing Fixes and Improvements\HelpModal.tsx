import { useLanguageContext } from './LanguageProvider';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { But<PERSON> } from './ui/button';
import { useState } from 'react';

export function HelpModal() {
  const { t } = useLanguageContext();
  const [isOpen, setIsOpen] = useState(false);
  
  // This component needs to be triggered to open. We'll add a temporary button for testing.
  // In the final app, the Header component will trigger this.
  return (
    <>
      {/* Temporary button to open modal */}
      <Button onClick={() => setIsOpen(true)} className="fixed bottom-16 right-4 z-50">Help</Button>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{t('helpAndTutorial')}</DialogTitle>
          </DialogHeader>
          
          <Tabs defaultValue="getting-started">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="getting-started">{t('gettingStarted')}</TabsTrigger>
              <TabsTrigger value="error-types">{t('errorTypes')}</TabsTrigger>
              <TabsTrigger value="keyboard-shortcuts">{t('keyboardShortcuts')}</TabsTrigger>
            </TabsList>
            
            <TabsContent value="getting-started" className="space-y-4 pt-4">
              <h4 className="font-medium text-lg">Welcome to GrammarPro</h4>
              <p>GrammarPro is a powerful grammar and spelling checker that helps you write better content. Here's how to get started:</p>
              
              <div className="space-y-2">
                <h5 className="font-medium">1. Start Writing</h5>
                <p className="text-sm">Simply start typing in the editor area. GrammarPro will automatically check your text as you write.</p>
                
                <h5 className="font-medium">2. Review Suggestions</h5>
                <p className="text-sm">Errors and suggestions will be highlighted in the text. Click on any highlighted word to see suggestions.</p>
                
                <h5 className="font-medium">3. Apply Corrections</h5>
                <p className="text-sm">Click on a suggestion to apply it to your text. You can also ignore suggestions if you prefer.</p>
                
                <h5 className="font-medium">4. Customize Settings</h5>
                <p className="text-sm">Use the settings menu to customize the application to your preferences, including language, theme, and more.</p>
              </div>
            </TabsContent>
            
            <TabsContent value="error-types" className="space-y-4 pt-4">
              <h4 className="font-medium text-lg">Understanding Error Types</h4>
              <p>GrammarPro identifies different types of writing issues, each highlighted with a different color:</p>
              
              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <div className="w-3 h-3 rounded-full bg-red-500 mt-1"></div>
                  <div>
                    <h5 className="font-medium">Spelling Errors</h5>
                    <p className="text-sm">Words that may be misspelled or not recognized in the current language.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-2">
                  <div className="w-3 h-3 rounded-full bg-blue-500 mt-1"></div>
                  <div>
                    <h5 className="font-medium">Grammar Issues</h5>
                    <p className="text-sm">Problems with sentence structure, verb tense, punctuation, and other grammar rules.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-2">
                  <div className="w-3 h-3 rounded-full bg-green-500 mt-1"></div>
                  <div>
                    <h5 className="font-medium">Style Suggestions</h5>
                    <p className="text-sm">Recommendations for improving clarity, conciseness, and overall writing style.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500 mt-1"></div>
                  <div>
                    <h5 className="font-medium">Clarity Improvements</h5>
                    <p className="text-sm">Suggestions to make your writing more clear and easier to understand.</p>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="keyboard-shortcuts" className="space-y-4 pt-4">
              <h4 className="font-medium text-lg">Keyboard Shortcuts</h4>
              <p>Use these keyboard shortcuts to work more efficiently:</p>
              
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm font-medium">Ctrl+Z</div>
                <div className="text-sm">{t('undo')}</div>
                
                <div className="text-sm font-medium">Ctrl+Y</div>
                <div className="text-sm">{t('redo')}</div>
                
                <div className="text-sm font-medium">Ctrl+G</div>
                <div className="text-sm">{t('checkGrammar')}</div>
                
                <div className="text-sm font-medium">Ctrl+A</div>
                <div className="text-sm">{t('aiAssistant')}</div>
                
                <div className="text-sm font-medium">Ctrl+,</div>
                <div className="text-sm">{t('openSettings')}</div>
                
                <div className="text-sm font-medium">Ctrl+E</div>
                <div className="text-sm">{t('exportDocument')}</div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
}
