import React, { useState, useEffect, createContext, useContext, useCallback, useRef } from 'react';

// --- Contexts ---

// Language Context
const LanguageContext = createContext();

const languages = {
  en: {
    name: 'English',
    direction: 'ltr',
    dialects: {
      'en-US': 'American English',
      'en-GB': 'British English',
    },
    translations: {
      appTitle: 'Advanced Writing Assistant',
      settings: 'Settings',
      editor: 'Editor',
      multilingualSupport: 'Multilingual Language Support',
      uiLanguage: 'UI Language',
      writingLanguage: 'Primary Writing Language',
      regionalDialect: 'Regional Dialect',
      languageProficiency: 'Language Proficiency/Background',
      nativeSpeaker: 'Native Speaker',
      advancedLearner: 'Advanced Learner',
      intermediateLearner: 'Intermediate Learner',
      beginner: 'Beginner',
      generativeAIFeatures: 'Generative AI Feature Customization',
      showAISuggestions: 'Show AI suggestions when selecting text',
      displayQuickReply: 'Display AI-based quick reply suggestions',
      viewRecentPromptHistory: 'View and reuse recent prompt history',
      personalDictionary: 'Personal Dictionary',
      addCustomWord: 'Add Custom Word',
      importDictionary: 'Import Dictionary',
      exportDictionary: 'Export Dictionary',
      appearanceAccessibility: 'Appearance & Accessibility Settings',
      theme: 'Theme',
      lightMode: 'Light Mode',
      darkMode: 'Dark Mode',
      systemDefault: 'System Default',
      fontSize: 'Font Size',
      small: 'Small',
      normal: 'Normal',
      large: 'Large',
      highContrastMode: 'High Contrast Mode',
      advancedWritingAssistance: 'Advanced Writing Assistance',
      realtimeSuggestions: 'Real-time spelling, grammar, and style suggestions',
      toneDetection: 'Tone Detection',
      adjustTone: 'Adjust Tone',
      formal: 'Formal',
      casual: 'Casual',
      professional: 'Professional',
      sentenceRewriting: 'Full sentence rewriting for clarity and fluency',
      consistencyTools: 'Consistency tools (e.g., brand style)',
      nonNativeAssistance: 'Assistance for non-native speakers',
      plagiarismDetection: 'Plagiarism detection (conceptual)',
      aiTextGeneration: 'AI Text Generation via Custom Prompts',
      enterPrompt: 'Enter your prompt here...',
      generate: 'Generate',
      saveSettings: 'Save Settings',
      textEditor: 'Text Editor',
      typeHere: 'Start typing here...',
      brainstorm: 'Brainstorm',
      rewriteSentence: 'Rewrite Sentence',
      quickReplies: 'Quick Replies',
      promptHistory: 'Prompt History',
      customWordAdded: 'Custom word added!',
      wordExists: 'Word already exists in dictionary.',
      dictionaryCleared: 'Dictionary cleared!',
      dictionaryImported: 'Dictionary imported!',
      copyToClipboard: 'Copy to Clipboard',
      copied: 'Copied!',
      aiSuggestions: 'AI Suggestions',
      noSuggestions: 'No AI suggestions available.',
      loading: 'Loading...',
      error: 'Error:',
      clearDictionary: 'Clear Dictionary',
      deleteWord: 'Delete',
      tone: 'Tone',
      style: 'Style',
      grammar: 'Grammar',
      spelling: 'Spelling',
      clarity: 'Clarity',
      fluency: 'Fluency',
      consistency: 'Consistency',
      nonNative: 'Non-Native Speaker Assistance',
      plagiarism: 'Plagiarism Detection',
      aiGeneration: 'AI Generation',
      aiPrompt: 'AI Prompt',
      aiResponse: 'AI Response',
      close: 'Close',
      promptHistoryTitle: 'Recent Prompt History',
      noPromptHistory: 'No recent prompt history.',
      usePrompt: 'Use',
      aiPoweredTools: 'AI-Powered Tools',
      selectTextForSuggestions: 'Select text in the editor to get AI suggestions.',
      messageBoxTitle: 'Message',
      ok: 'OK',
      cancel: 'Cancel',
      noCustomWords: 'No custom words added yet.',
      confirmClearDictionary: 'Are you sure you want to clear your personal dictionary? This action cannot be undone.',
    },
  },
  ar: {
    name: 'العربية',
    direction: 'rtl',
    dialects: {
      'ar-SA': 'العربية السعودية',
      'ar-EG': 'العربية المصرية',
      'ar-SY': 'العربية السورية',
    },
    translations: {
      appTitle: 'مساعد الكتابة المتقدم',
      settings: 'الإعدادات',
      editor: 'المحرر',
      multilingualSupport: 'دعم اللغات المتعددة',
      uiLanguage: 'لغة واجهة المستخدم',
      writingLanguage: 'لغة الكتابة الأساسية',
      regionalDialect: 'اللهجة الإقليمية',
      languageProficiency: 'مستوى إتقان اللغة/الخلفية',
      nativeSpeaker: 'متحدث أصلي',
      advancedLearner: 'متعلم متقدم',
      intermediateLearner: 'متعلم متوسط',
      beginner: 'مبتدئ',
      generativeAIFeatures: 'تخصيص ميزات الذكاء الاصطناعي التوليدي',
      showAISuggestions: 'إظهار اقتراحات الذكاء الاصطناعي عند تحديد النص',
      displayQuickReply: 'عرض اقتراحات الرد السريع المستندة إلى الذكاء الاصطناعي',
      viewRecentPromptHistory: 'عرض وإعادة استخدام سجل المطالبات الأخير',
      personalDictionary: 'القاموس الشخصي',
      addCustomWord: 'إضافة كلمة مخصصة',
      importDictionary: 'استيراد القاموس',
      exportDictionary: 'تصدير القاموس',
      appearanceAccessibility: 'إعدادات المظهر وإمكانية الوصول',
      theme: 'السمة',
      lightMode: 'الوضع الفاتح',
      darkMode: 'الوضع الداكن',
      systemDefault: 'افتراضي النظام',
      fontSize: 'حجم الخط',
      small: 'صغير',
      normal: 'عادي',
      large: 'كبير',
      highContrastMode: 'وضع التباين العالي',
      advancedWritingAssistance: 'مساعدة الكتابة المتقدمة',
      realtimeSuggestions: 'اقتراحات التدقيق الإملائي والنحوي والأسلوب في الوقت الفعلي',
      toneDetection: 'اكتشاف النبرة',
      adjustTone: 'ضبط النبرة',
      formal: 'رسمي',
      casual: 'غير رسمي',
      professional: 'احترافي',
      sentenceRewriting: 'إعادة صياغة الجملة بالكامل للوضوح والطلاقة',
      consistencyTools: 'أدوات الاتساق (مثل أسلوب العلامة التجارية)',
      nonNativeAssistance: 'مساعدة للمتحدثين غير الأصليين',
      plagiarismDetection: 'كشف الانتحال (مفهوم)',
      aiTextGeneration: 'توليد النص بالذكاء الاصطناعي عبر المطالبات المخصصة',
      enterPrompt: 'أدخل المطالبة هنا...',
      generate: 'توليد',
      saveSettings: 'حفظ الإعدادات',
      textEditor: 'محرر النصوص',
      typeHere: 'ابدأ الكتابة هنا...',
      brainstorm: 'عصف ذهني',
      rewriteSentence: 'إعادة صياغة الجملة',
      quickReplies: 'الردود السريعة',
      promptHistory: 'سجل المطالبات',
      customWordAdded: 'تمت إضافة الكلمة المخصصة!',
      wordExists: 'الكلمة موجودة بالفعل في القاموس.',
      dictionaryCleared: 'تم مسح القاموس!',
      dictionaryImported: 'تم استيراد القاموس!',
      copyToClipboard: 'نسخ إلى الحافظة',
      copied: 'تم النسخ!',
      aiSuggestions: 'اقتراحات الذكاء الاصطناعي',
      noSuggestions: 'لا توجد اقتراحات للذكاء الاصطناعي.',
      loading: 'جاري التحميل...',
      error: 'خطأ:',
      clearDictionary: 'مسح القاموس',
      deleteWord: 'حذف',
      tone: 'النبرة',
      style: 'الأسلوب',
      grammar: 'القواعد',
      spelling: 'الإملاء',
      clarity: 'الوضوح',
      fluency: 'الطلاقة',
      consistency: 'الاتساق',
      nonNative: 'مساعدة لغير المتحدثين الأصليين',
      plagiarism: 'كشف الانتحال',
      aiGeneration: 'توليد الذكاء الاصطناعي',
      aiPrompt: 'مطالبة الذكاء الاصطناعي',
      aiResponse: 'استجابة الذكاء الاصطناعي',
      close: 'إغلاق',
      promptHistoryTitle: 'سجل المطالبات الأخيرة',
      noPromptHistory: 'لا يوجد سجل مطالبات حديث.',
      usePrompt: 'استخدام',
      aiPoweredTools: 'أدوات مدعومة بالذكاء الاصطناعي',
      selectTextForSuggestions: 'حدد نصًا في المحرر للحصول على اقتراحات الذكاء الاصطناعي.',
      messageBoxTitle: 'رسالة',
      ok: 'موافق',
      cancel: 'إلغاء',
      noCustomWords: 'لم تتم إضافة كلمات مخصصة بعد.',
      confirmClearDictionary: 'هل أنت متأكد أنك تريد مسح قاموسك الشخصي؟ لا يمكن التراجع عن هذا الإجراء.',
    },
  },
  tr: {
    name: 'Türkçe',
    direction: 'ltr',
    dialects: {
      'tr-TR': 'Türkiye Türkçesi',
    },
    translations: {
      appTitle: 'Gelişmiş Yazma Asistanı',
      settings: 'Ayarlar',
      editor: 'Düzenleyici',
      multilingualSupport: 'Çok Dilli Dil Desteği',
      uiLanguage: 'Kullanıcı Arayüzü Dili',
      writingLanguage: 'Birincil Yazma Dili',
      regionalDialect: 'Bölgesel Lehçe',
      languageProficiency: 'Dil Yeterliliği/Geçmişi',
      nativeSpeaker: 'Ana Dil Konuşmacısı',
      advancedLearner: 'İleri Seviye Öğrenci',
      intermediateLearner: 'Orta Seviye Öğrenci',
      beginner: 'Başlangıç Seviyesi',
      generativeAIFeatures: 'Üretken Yapay Zeka Özelliği Özelleştirmesi',
      showAISuggestions: 'Metin seçildiğinde yapay zeka önerilerini göster',
      displayQuickReply: 'Yapay zeka tabanlı hızlı yanıt önerilerini göster',
      viewRecentPromptHistory: 'Son istem geçmişini görüntüle ve yeniden kullan',
      personalDictionary: 'Kişisel Sözlük',
      addCustomWord: 'Özel Kelime Ekle',
      importDictionary: 'Sözlük İçe Aktar',
      exportDictionary: 'Sözlük Dışa Aktar',
      appearanceAccessibility: 'Görünüm ve Erişilebilirlik Ayarları',
      theme: 'Tema',
      lightMode: 'Açık Mod',
      darkMode: 'Koyu Mod',
      systemDefault: 'Sistem Varsayılanı',
      fontSize: 'Yazı Tipi Boyutu',
      small: 'Küçük',
      normal: 'Normal',
      large: 'Büyük',
      highContrastMode: 'Yüksek Kontrast Modu',
      advancedWritingAssistance: 'Gelişmiş Yazma Yardımı',
      realtimeSuggestions: 'Gerçek zamanlı yazım, dil bilgisi ve stil önerileri',
      toneDetection: 'Ton Algılama',
      adjustTone: 'Tonu Ayarla',
      formal: 'Resmi',
      casual: 'Günlük',
      professional: 'Profesyonel',
      sentenceRewriting: 'Netlik ve akıcılık için tam cümle yeniden yazma',
      consistencyTools: 'Tutarlılık araçları (örn. marka stili)',
      nonNativeAssistance: 'Ana dili olmayan konuşmacılar için yardım',
      plagiarismDetection: 'İntihal tespiti (kavramsal)',
      aiTextGeneration: 'Özel İstemlerle Yapay Zeka Metin Üretimi',
      enterPrompt: 'İsteminizi buraya girin...',
      generate: 'Üret',
      saveSettings: 'Ayarları Kaydet',
      textEditor: 'Metin Düzenleyici',
      typeHere: 'Buraya yazmaya başlayın...',
      brainstorm: 'Beyin Fırtınası',
      rewriteSentence: 'Cümleyi Yeniden Yaz',
      quickReplies: 'Hızlı Yanıtlar',
      promptHistory: 'İstem Geçmişi',
      customWordAdded: 'Özel kelime eklendi!',
      wordExists: 'Kelime zaten sözlükte mevcut.',
      dictionaryCleared: 'Sözlük temizlendi!',
      dictionaryImported: 'Sözlük içe aktarıldı!',
      copyToClipboard: 'Panoya Kopyala',
      copied: 'Kopyalandı!',
      aiSuggestions: 'Yapay Zeka Önerileri',
      noSuggestions: 'Yapay zeka önerisi yok.',
      loading: 'Yükleniyor...',
      error: 'Hata:',
      clearDictionary: 'Sözlüğü Temizle',
      deleteWord: 'Sil',
      tone: 'Ton',
      style: 'Stil',
      grammar: 'Dil Bilgisi',
      spelling: 'Yazım',
      clarity: 'Açıklık',
      fluency: 'Akıcılık',
      consistency: 'Tutarlılık',
      nonNative: 'Ana Dili Olmayanlara Yardım',
      plagiarism: 'İntihal Tespiti',
      aiGeneration: 'Yapay Zeka Üretimi',
      aiPrompt: 'Yapay Zeka İstemi',
      aiResponse: 'Yapay Zeka Yanıtı',
      close: 'Kapat',
      promptHistoryTitle: 'Son İstem Geçmişi',
      noPromptHistory: 'Yakın zamanda istem geçmişi yok.',
      usePrompt: 'Kullan',
      aiPoweredTools: 'Yapay Zeka Destekli Araçlar',
      selectTextForSuggestions: 'Yapay zeka önerileri almak için düzenleyicide metin seçin.',
      messageBoxTitle: 'Mesaj',
      ok: 'Tamam',
      cancel: 'İptal',
      noCustomWords: 'Henüz özel kelime eklenmedi.',
      confirmClearDictionary: 'Kişisel sözlüğünüzü temizlemek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
    },
  },
  es: {
    name: 'Español',
    direction: 'ltr',
    dialects: {
      'es-ES': 'Español de España',
      'es-MX': 'Español de México',
    },
    translations: {
      appTitle: 'Asistente de Escritura Avanzado',
      settings: 'Configuración',
      editor: 'Editor',
      multilingualSupport: 'Soporte Multilingüe',
      uiLanguage: 'Idioma de la Interfaz',
      writingLanguage: 'Idioma Principal de Escritura',
      regionalDialect: 'Dialecto Regional',
      languageProficiency: 'Nivel de Dominio del Idioma/Antecedentes',
      nativeSpeaker: 'Hablante Nativo',
      advancedLearner: 'Estudiante Avanzado',
      intermediateLearner: 'Estudiante Intermedio',
      beginner: 'Principiante',
      generativeAIFeatures: 'Personalización de Funciones de IA Generativa',
      showAISuggestions: 'Mostrar sugerencias de IA al seleccionar texto',
      displayQuickReply: 'Mostrar sugerencias de respuesta rápida basadas en IA',
      viewRecentPromptHistory: 'Ver y reutilizar el historial de prompts recientes',
      personalDictionary: 'Diccionario Personal',
      addCustomWord: 'Añadir Palabra Personalizada',
      importDictionary: 'Importar Diccionario',
      exportDictionary: 'Exportar Diccionario',
      appearanceAccessibility: 'Configuración de Apariencia y Accesibilidad',
      theme: 'Tema',
      lightMode: 'Modo Claro',
      darkMode: 'Modo Oscuro',
      systemDefault: 'Predeterminado del Sistema',
      fontSize: 'Tamaño de Fuente',
      small: 'Pequeño',
      normal: 'Normal',
      large: 'Grande',
      highContrastMode: 'Modo de Alto Contraste',
      advancedWritingAssistance: 'Asistencia de Escritura Avanzada',
      realtimeSuggestions: 'Sugerencias de ortografía, gramática y estilo en tiempo real',
      toneDetection: 'Detección de Tono',
      adjustTone: 'Ajustar Tono',
      formal: 'Formal',
      casual: 'Informal',
      professional: 'Profesional',
      sentenceRewriting: 'Reescritura completa de oraciones para mayor claridad y fluidez',
      consistencyTools: 'Herramientas de consistencia (ej. estilo de marca)',
      nonNativeAssistance: 'Asistencia para hablantes no nativos',
      plagiarismDetection: 'Detección de plagio (conceptual)',
      aiTextGeneration: 'Generación de Texto con IA mediante Prompts Personalizados',
      enterPrompt: 'Introduce tu prompt aquí...',
      generate: 'Generar',
      saveSettings: 'Guardar Configuración',
      textEditor: 'Editor de Texto',
      typeHere: 'Empieza a escribir aquí...',
      brainstorm: 'Lluvia de Ideas',
      rewriteSentence: 'Reescribir Oración',
      quickReplies: 'Respuestas Rápidas',
      promptHistory: 'Historial de Prompts',
      customWordAdded: '¡Palabra personalizada añadida!',
      wordExists: 'La palabra ya existe en el diccionario.',
      dictionaryCleared: '¡Diccionario limpiado!',
      dictionaryImported: '¡Diccionario importado!',
      copyToClipboard: 'Copiar al Portapapeles',
      copied: '¡Copiado!',
      aiSuggestions: 'Sugerencias de IA',
      noSuggestions: 'No hay sugerencias de IA disponibles.',
      loading: 'Cargando...',
      error: 'Error:',
      clearDictionary: 'Limpiar Diccionario',
      deleteWord: 'Eliminar',
      tone: 'Tono',
      style: 'Estilo',
      grammar: 'Gramática',
      spelling: 'Ortografía',
      clarity: 'Claridad',
      fluency: 'Fluidez',
      consistency: 'Consistencia',
      nonNative: 'Asistencia para no Nativos',
      plagiarism: 'Detección de Plagio',
      aiGeneration: 'Generación de IA',
      aiPrompt: 'Prompt de IA',
      aiResponse: 'Respuesta de IA',
      close: 'Cerrar',
      promptHistoryTitle: 'Historial de Prompts Recientes',
      noPromptHistory: 'No hay historial de prompts recientes.',
      usePrompt: 'Usar',
      aiPoweredTools: 'Herramientas con IA',
      selectTextForSuggestions: 'Selecciona texto en el editor para obtener sugerencias de IA.',
      messageBoxTitle: 'Mensaje',
      ok: 'Aceptar',
      cancel: 'Cancelar',
      noCustomWords: 'No se han añadido palabras personalizadas aún.',
      confirmClearDictionary: '¿Estás seguro de que quieres borrar tu diccionario personal? Esta acción no se puede deshacer.',
    },
  },
  de: {
    name: 'Deutsch',
    direction: 'ltr',
    dialects: {
      'de-DE': 'Standarddeutsch',
    },
    translations: {
      appTitle: 'Erweiterter Schreibassistent',
      settings: 'Einstellungen',
      editor: 'Editor',
      multilingualSupport: 'Mehrsprachige Unterstützung',
      uiLanguage: 'Benutzeroberflächensprache',
      writingLanguage: 'Primäre Schreibsprache',
      regionalDialect: 'Regionale Mundart',
      languageProficiency: 'Sprachkenntnisse/Hintergrund',
      nativeSpeaker: 'Muttersprachler',
      advancedLearner: 'Fortgeschrittener Lerner',
      intermediateLearner: 'Fortgeschrittener Lerner',
      beginner: 'Anfänger',
      generativeAIFeatures: 'Anpassung der generativen KI-Funktionen',
      showAISuggestions: 'KI-Vorschläge bei Textauswahl anzeigen',
      displayQuickReply: 'KI-basierte Schnellantwortvorschläge anzeigen',
      viewRecentPromptHistory: 'Aktuellen Prompt-Verlauf anzeigen und wiederverwenden',
      personalDictionary: 'Persönliches Wörterbuch',
      addCustomWord: 'Benutzerdefiniertes Wort hinzufügen',
      importDictionary: 'Wörterbuch importieren',
      exportDictionary: 'Wörterbuch exportieren',
      appearanceAccessibility: 'Erscheinungsbild- und Barrierefreiheitseinstellungen',
      theme: 'Thema',
      lightMode: 'Heller Modus',
      darkMode: 'Dunkler Modus',
      systemDefault: 'Systemstandard',
      fontSize: 'Schriftgröße',
      small: 'Klein',
      normal: 'Normal',
      large: 'Groß',
      highContrastMode: 'Hoher Kontrastmodus',
      advancedWritingAssistance: 'Erweiterte Schreibhilfe',
      realtimeSuggestions: 'Echtzeit-Rechtschreib-, Grammatik- und Stilvorschläge',
      toneDetection: 'Tonerkennung',
      adjustTone: 'Ton anpassen',
      formal: 'Formell',
      casual: 'Zwanglos',
      professional: 'Professionell',
      sentenceRewriting: 'Vollständige Satzumschreibung für Klarheit und Flüssigkeit',
      consistencyTools: 'Konsistenzwerkzeuge (z.B. Markenstil)',
      nonNativeAssistance: 'Hilfe für Nicht-Muttersprachler',
      plagiarismDetection: 'Plagiatsprüfung (konzeptuell)',
      aiTextGeneration: 'KI-Textgenerierung über benutzerdefinierte Prompts',
      enterPrompt: 'Geben Sie hier Ihren Prompt ein...',
      generate: 'Generieren',
      saveSettings: 'Einstellungen speichern',
      textEditor: 'Texteditor',
      typeHere: 'Beginnen Sie hier zu tippen...',
      brainstorm: 'Brainstorming',
      rewriteSentence: 'Satz umschreiben',
      quickReplies: 'Schnellantworten',
      promptHistory: 'Prompt-Verlauf',
      customWordAdded: 'Benutzerdefiniertes Wort hinzugefügt!',
      wordExists: 'Wort existiert bereits im Wörterbuch.',
      dictionaryCleared: 'Wörterbuch geleert!',
      dictionaryImported: 'Wörterbuch importiert!',
      copyToClipboard: 'In Zwischenablage kopieren',
      copied: 'Kopiert!',
      aiSuggestions: 'KI-Vorschläge',
      noSuggestions: 'Keine KI-Vorschläge verfügbar.',
      loading: 'Wird geladen...',
      error: 'Fehler:',
      clearDictionary: 'Wörterbuch leeren',
      deleteWord: 'Löschen',
      tone: 'Ton',
      style: 'Stil',
      grammar: 'Grammatik',
      spelling: 'Rechtschreibung',
      clarity: 'Klarheit',
      fluency: 'Flüssigkeit',
      consistency: 'Konsistenz',
      nonNative: 'Hilfe für Nicht-Muttersprachler',
      plagiarism: 'Plagiatsprüfung',
      aiGeneration: 'KI-Generierung',
      aiPrompt: 'KI-Prompt',
      aiResponse: 'KI-Antwort',
      close: 'Schließen',
      promptHistoryTitle: 'Aktueller Prompt-Verlauf',
      noPromptHistory: 'Kein aktueller Prompt-Verlauf.',
      usePrompt: 'Verwenden',
      aiPoweredTools: 'KI-gestützte Werkzeuge',
      selectTextForSuggestions: 'Wählen Sie Text im Editor aus, um KI-Vorschläge zu erhalten.',
      messageBoxTitle: 'Nachricht',
      ok: 'OK',
      cancel: 'Abbrechen',
      noCustomWords: 'Noch keine benutzerdefinierten Wörter hinzugefügt.',
      confirmClearDictionary: 'Sind Sie sicher, dass Sie Ihr persönliches Wörterbuch leeren möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
    },
  },
  fr: {
    name: 'Français',
    direction: 'ltr',
    dialects: {
      'fr-FR': 'Français de France',
      'fr-CA': 'Français Canadien',
    },
    translations: {
      appTitle: 'Assistant d\'Écriture Avancé',
      settings: 'Paramètres',
      editor: 'Éditeur',
      multilingualSupport: 'Support Multilingue',
      uiLanguage: 'Langue de l\'Interface Utilisateur',
      writingLanguage: 'Langue d\'Écriture Principale',
      regionalDialect: 'Dialecte Régional',
      languageProficiency: 'Niveau de Compétence Linguistique/Contexte',
      nativeSpeaker: 'Locuteur Natif',
      advancedLearner: 'Apprenant Avancé',
      intermediateLearner: 'Apprenant Intermédiaire',
      beginner: 'Débutant',
      generativeAIFeatures: 'Personnalisation des Fonctionnalités d\'IA Générative',
      showAISuggestions: 'Afficher les suggestions d\'IA lors de la sélection de texte',
      displayQuickReply: 'Afficher les suggestions de réponse rapide basées sur l\'IA',
      viewRecentPromptHistory: 'Afficher et réutiliser l\'historique des invites récentes',
      personalDictionary: 'Dictionnaire Personnel',
      addCustomWord: 'Ajouter un Mot Personnalisé',
      importDictionary: 'Importer le Dictionnaire',
      exportDictionary: 'Exporter le Dictionnaire',
      appearanceAccessibility: 'Paramètres d\'Apparence et d\'Accessibilité',
      theme: 'Thème',
      lightMode: 'Mode Clair',
      darkMode: 'Mode Sombre',
      systemDefault: 'Par Défaut du Système',
      fontSize: 'Taille de Police',
      small: 'Petit',
      normal: 'Normal',
      large: 'Grand',
      highContrastMode: 'Mode Contraste Élevé',
      advancedWritingAssistance: 'Assistance à l\'Écriture Avancée',
      realtimeSuggestions: 'Suggestions d\'orthographe, de grammaire et de style en temps réel',
      toneDetection: 'Détection de la Tonalité',
      adjustTone: 'Ajuster la Tonalité',
      formal: 'Formel',
      casual: 'Informel',
      professional: 'Professionnel',
      sentenceRewriting: 'Réécriture complète de phrases pour la clarté et la fluidité',
      consistencyTools: 'Outils de cohérence (ex. style de marque)',
      nonNativeAssistance: 'Assistance pour les locuteurs non natifs',
      plagiarismDetection: 'Détection de plagiat (conceptuel)',
      aiTextGeneration: 'Génération de Texte par IA via des Invites Personnalisées',
      enterPrompt: 'Entrez votre invite ici...',
      generate: 'Générer',
      saveSettings: 'Enregistrer les Paramètres',
      textEditor: 'Éditeur de Texte',
      typeHere: 'Commencez à taper ici...',
      brainstorm: 'Brainstorming',
      rewriteSentence: 'Réécrire la Phrase',
      quickReplies: 'Réponses Rapides',
      promptHistory: 'Historique des Invites',
      customWordAdded: 'Mot personnalisé ajouté !',
      wordExists: 'Le mot existe déjà dans le dictionnaire.',
      dictionaryCleared: 'Dictionnaire vidé !',
      dictionaryImported: 'Dictionnaire importé !',
      copyToClipboard: 'Copier dans le Presse-papiers',
      copied: 'Copié !',
      aiSuggestions: 'Suggestions IA',
      noSuggestions: 'Aucune suggestion IA disponible.',
      loading: 'Chargement...',
      error: 'Erreur :',
      clearDictionary: 'Vider le Dictionnaire',
      deleteWord: 'Supprimer',
      tone: 'Tonalité',
      style: 'Style',
      grammar: 'Grammaire',
      spelling: 'Orthographe',
      clarity: 'Clarté',
      fluency: 'Fluidité',
      consistency: 'Cohérence',
      nonNative: 'Assistance Non-Natif',
      plagiarism: 'Détection de Plagiat',
      aiGeneration: 'Génération IA',
      aiPrompt: 'Invite IA',
      aiResponse: 'Réponse IA',
      close: 'Fermer',
      promptHistoryTitle: 'Historique des Invites Récentes',
      noPromptHistory: 'Aucun historique d\'invites récent.',
      usePrompt: 'Utiliser',
      aiPoweredTools: 'Outils Alimentés par l\'IA',
      selectTextForSuggestions: 'Sélectionnez du texte dans l\'éditeur pour obtenir des suggestions d\'IA.',
      messageBoxTitle: 'Message',
      ok: 'OK',
      cancel: 'Annuler',
      noCustomWords: 'Aucun mot personnalisé ajouté pour l\'instant.',
      confirmClearDictionary: 'Êtes-vous sûr de vouloir vider votre dictionnaire personnel ? Cette action est irréversible.',
    },
  },
  nl: {
    name: 'Nederlands',
    direction: 'ltr',
    dialects: {
      'nl-NL': 'Nederlands',
    },
    translations: {
      appTitle: 'Geavanceerde Schrijfassistent',
      settings: 'Instellingen',
      editor: 'Editor',
      multilingualSupport: 'Meertalige Ondersteuning',
      uiLanguage: 'UI Taal',
      writingLanguage: 'Primaire Schrijftaal',
      regionalDialect: 'Regionaal Dialect',
      languageProficiency: 'Taalvaardigheid/Achtergrond',
      nativeSpeaker: 'Moedertaalspreker',
      advancedLearner: 'Gevorderde Leerling',
      intermediateLearner: 'Gemiddelde Leerling',
      beginner: 'Beginner',
      generativeAIFeatures: 'Generatieve AI Functie Aanpassing',
      showAISuggestions: 'AI-suggesties tonen bij tekstselectie',
      displayQuickReply: 'AI-gebaseerde snelle antwoordsuggesties tonen',
      viewRecentPromptHistory: 'Recente promptgeschiedenis bekijken en hergebruiken',
      personalDictionary: 'Persoonlijk Woordenboek',
      addCustomWord: 'Aangepast Woord Toevoegen',
      importDictionary: 'Woordenboek Importeren',
      exportDictionary: 'Woordenboek Exporteren',
      appearanceAccessibility: 'Uiterlijk & Toegankelijkheidsinstellingen',
      theme: 'Thema',
      lightMode: 'Lichte Modus',
      darkMode: 'Donkere Modus',
      systemDefault: 'Systeemstandaard',
      fontSize: 'Lettergrootte',
      small: 'Klein',
      normal: 'Normaal',
      large: 'Groot',
      highContrastMode: 'Hoog Contrast Modus',
      advancedWritingAssistance: 'Geavanceerde Schrijfhulp',
      realtimeSuggestions: 'Realtime spelling-, grammatica- en stijlsuggesties',
      toneDetection: 'Toondetectie',
      adjustTone: 'Toon aanpassen',
      formal: 'Formeel',
      casual: 'Informeel',
      professional: 'Professioneel',
      sentenceRewriting: 'Volledige zinsherformulering voor duidelijkheid en vloeiendheid',
      consistencyTools: 'Consistentietools (bijv. merkstijl)',
      nonNativeAssistance: 'Hulp voor niet-moedertaalsprekers',
      plagiarismDetection: 'Plagiaatdetectie (conceptueel)',
      aiTextGeneration: 'AI Tekstgeneratie via Aangepaste Prompts',
      enterPrompt: 'Voer hier uw prompt in...',
      generate: 'Genereren',
      saveSettings: 'Instellingen Opslaan',
      textEditor: 'Teksteditor',
      typeHere: 'Begin hier te typen...',
      brainstorm: 'Brainstormen',
      rewriteSentence: 'Zin Herschrijven',
      quickReplies: 'Snelle Antwoorden',
      promptHistory: 'Promptgeschiedenis',
      customWordAdded: 'Aangepast woord toegevoegd!',
      wordExists: 'Woord bestaat al in woordenboek.',
      dictionaryCleared: 'Woordenboek geleegd!',
      dictionaryImported: 'Woordenboek geïmporteerd!',
      copyToClipboard: 'Kopiëren naar klembord',
      copied: 'Gekopieerd!',
      aiSuggestions: 'AI Suggesties',
      noSuggestions: 'Geen AI-suggesties beschikbaar.',
      loading: 'Laden...',
      error: 'Fout:',
      clearDictionary: 'Woordenboek Leegmaken',
      deleteWord: 'Verwijderen',
      tone: 'Toon',
      style: 'Stijl',
      grammar: 'Grammatica',
      spelling: 'Spelling',
      clarity: 'Duidelijkheid',
      fluency: 'Vloeiendheid',
      consistency: 'Consistentie',
      nonNative: 'Hulp voor niet-moedertaalsprekers',
      plagiarism: 'Plagiaatdetectie',
      aiGeneration: 'AI Generatie',
      aiPrompt: 'AI Prompt',
      aiResponse: 'AI Antwoord',
      close: 'Sluiten',
      promptHistoryTitle: 'Recente Promptgeschiedenis',
      noPromptHistory: 'Geen recente promptgeschiedenis.',
      usePrompt: 'Gebruiken',
      aiPoweredTools: 'AI-aangedreven tools',
      selectTextForSuggestions: 'Selecteer tekst in de editor om AI-suggesties te krijgen.',
      messageBoxTitle: 'Bericht',
      ok: 'OK',
      cancel: 'Annuleren',
      noCustomWords: 'Nog geen aangepaste woorden toegevoegd.',
      confirmClearDictionary: 'Weet u zeker dat u uw persoonlijke woordenboek wilt wissen? Deze actie kan niet ongedaan worden gemaakt.',
    },
  },
  it: {
    name: 'Italiano',
    direction: 'ltr',
    dialects: {
      'it-IT': 'Italiano',
    },
    translations: {
      appTitle: 'Assistente di Scrittura Avanzato',
      settings: 'Impostazioni',
      editor: 'Editor',
      multilingualSupport: 'Supporto Multilingue',
      uiLanguage: 'Lingua dell\'Interfaccia Utente',
      writingLanguage: 'Lingua di Scrittura Primaria',
      regionalDialect: 'Dialetto Regionale',
      languageProficiency: 'Livello di Competenza Linguistica/Contesto',
      nativeSpeaker: 'Madrelingua',
      advancedLearner: 'Studente Avanzato',
      intermediateLearner: 'Studente Intermedio',
      beginner: 'Principiante',
      generativeAIFeatures: 'Personalizzazione delle Funzionalità AI Generative',
      showAISuggestions: 'Mostra suggerimenti AI quando si seleziona il testo',
      displayQuickReply: 'Visualizza suggerimenti di risposta rapida basati su AI',
      viewRecentPromptHistory: 'Visualizza e riutilizza la cronologia dei prompt recenti',
      personalDictionary: 'Dizionario Personale',
      addCustomWord: 'Aggiungi Parola Personalizzata',
      importDictionary: 'Importa Dizionario',
      exportDictionary: 'Esporta Dizionario',
      appearanceAccessibility: 'Impostazioni Aspetto e Accessibilità',
      theme: 'Tema',
      lightMode: 'Modalità Chiara',
      darkMode: 'Modalità Scura',
      systemDefault: 'Predefinito di Sistema',
      fontSize: 'Dimensione Carattere',
      small: 'Piccolo',
      normal: 'Normale',
      large: 'Grande',
      highContrastMode: 'Modalità Alto Contrasto',
      advancedWritingAssistance: 'Assistenza alla Scrittura Avanzata',
      realtimeSuggestions: 'Suggerimenti di ortografia, grammatica e stile in tempo reale',
      toneDetection: 'Rilevamento del Tono',
      adjustTone: 'Regola il Tono',
      formal: 'Formale',
      casual: 'Informale',
      professional: 'Professionale',
      sentenceRewriting: 'Riscrizione completa della frase per chiarezza e fluidità',
      consistencyTools: 'Strumenti di coerenza (es. stile del marchio)',
      nonNativeAssistance: 'Assistenza per i parlanti non nativi',
      plagiarismDetection: 'Rilevamento del plagio (concettuale)',
      aiTextGeneration: 'Generazione di Testo AI tramite Prompt Personalizzati',
      enterPrompt: 'Inserisci il tuo prompt qui...',
      generate: 'Genera',
      saveSettings: 'Salva Impostazioni',
      textEditor: 'Editor di Testo',
      typeHere: 'Inizia a digitare qui...',
      brainstorm: 'Brainstorming',
      rewriteSentence: 'Riscrivi Frase',
      quickReplies: 'Risposte Rapide',
      promptHistory: 'Cronologia Prompt',
      customWordAdded: 'Parola personalizzata aggiunta!',
      wordExists: 'La parola esiste già nel dizionario.',
      dictionaryCleared: 'Dizionario cancellato!',
      dictionaryImported: 'Dizionario importato!',
      copyToClipboard: 'Copia negli Appunti',
      copied: 'Copiato!',
      aiSuggestions: 'Suggerimenti AI',
      noSuggestions: 'Nessun suggerimento AI disponibile.',
      loading: 'Caricamento...',
      error: 'Errore:',
      clearDictionary: 'Cancella Dizionario',
      deleteWord: 'Elimina',
      tone: 'Tono',
      style: 'Stile',
      grammar: 'Grammatica',
      spelling: 'Ortografia',
      clarity: 'Chiarezza',
      fluency: 'Fluidità',
      consistency: 'Coerenza',
      nonNative: 'Assistenza Non Madrelingua',
      plagiarism: 'Rilevamento Plagio',
      aiGeneration: 'Generazione AI',
      aiPrompt: 'Prompt AI',
      aiResponse: 'Risposta AI',
      close: 'Chiudi',
      promptHistoryTitle: 'Cronologia Prompt Recenti',
      noPromptHistory: 'Nessuna cronologia prompt recente.',
      usePrompt: 'Usa',
      aiPoweredTools: 'Strumenti Alimentati dall\'AI',
      selectTextForSuggestions: 'Seleziona il testo nell\'editor per ottenere suggerimenti AI.',
      messageBoxTitle: 'Messaggio',
      ok: 'OK',
      cancel: 'Annulla',
      noCustomWords: 'Nessuna parola personalizzata aggiunta ancora.',
      confirmClearDictionary: 'Sei sicuro di voler cancellare il tuo dizionario personale? Questa azione non può essere annullata.',
    },
  },
};

const getBrowserLanguage = () => {
  const browserLang = navigator.language || navigator.userLanguage;
  const langCode = browserLang.split('-')[0];
  if (languages[langCode]) {
    return langCode;
  }
  return 'en'; // Default to English if browser language is not supported
};

const LanguageProvider = ({ children }) => {
  const [uiLanguage, setUiLanguage] = useState(() => {
    return localStorage.getItem('uiLanguage') || getBrowserLanguage();
  });
  const [writingLanguage, setWritingLanguage] = useState(() => {
    return localStorage.getItem('writingLanguage') || 'en';
  });
  const [regionalDialect, setRegionalDialect] = useState(() => {
    return localStorage.getItem('regionalDialect') || 'en-US';
  });
  const [languageProficiency, setLanguageProficiency] = useState(() => {
    return localStorage.getItem('languageProficiency') || 'nativeSpeaker';
  });

  useEffect(() => {
    localStorage.setItem('uiLanguage', uiLanguage);
    localStorage.setItem('writingLanguage', writingLanguage);
    localStorage.setItem('regionalDialect', regionalDialect);
    localStorage.setItem('languageProficiency', languageProficiency);
    document.documentElement.setAttribute('dir', languages[uiLanguage]?.direction || 'ltr');
  }, [uiLanguage, writingLanguage, regionalDialect, languageProficiency]);

  const t = (key) => languages[uiLanguage]?.translations[key] || key;

  return (
    <LanguageContext.Provider value={{
      uiLanguage, setUiLanguage,
      writingLanguage, setWritingLanguage,
      regionalDialect, setRegionalDialect,
      languageProficiency, setLanguageProficiency,
      t, languages
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Theme Context
const ThemeContext = createContext();

const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) return savedTheme;
    // System default detection
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  });
  const [fontSize, setFontSize] = useState(() => localStorage.getItem('fontSize') || 'normal');
  const [highContrast, setHighContrast] = useState(() => localStorage.getItem('highContrast') === 'true');

  useEffect(() => {
    localStorage.setItem('theme', theme);
    localStorage.setItem('fontSize', fontSize);
    localStorage.setItem('highContrast', highContrast);

    const root = document.documentElement;
    root.classList.remove('light', 'dark', 'high-contrast', 'text-sm', 'text-base', 'text-lg');

    if (theme === 'systemDefault') {
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        root.classList.add('dark');
      } else {
        root.classList.add('light');
      }
    } else {
      root.classList.add(theme);
    }

    if (highContrast) {
      root.classList.add('high-contrast');
    }

    switch (fontSize) {
      case 'small':
        root.classList.add('text-sm');
        break;
      case 'normal':
        root.classList.add('text-base');
        break;
      case 'large':
        root.classList.add('text-lg');
        break;
      default:
        root.classList.add('text-base');
    }
  }, [theme, fontSize, highContrast]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme, fontSize, setFontSize, highContrast, setHighContrast }}>
      {children}
    </ThemeContext.Provider>
  );
};

// --- Components ---

const MessageBox = ({ title, message, onClose, onConfirm, showCancel = false }) => {
  const { t } = useContext(LanguageContext);
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-sm w-full border border-gray-200 dark:border-gray-700">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">{title || t('messageBoxTitle')}</h3>
        <p className="text-gray-700 dark:text-gray-300 mb-6">{message}</p>
        <div className="flex justify-end space-x-3">
          {showCancel && (
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
            >
              {t('cancel')}
            </button>
          )}
          <button
            onClick={onConfirm || onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
          >
            {onConfirm ? t('ok') : t('ok')}
          </button>
        </div>
      </div>
    </div>
  );
};

const SettingsPanel = ({ onSave }) => {
  const { t, uiLanguage, setUiLanguage, writingLanguage, setWritingLanguage, regionalDialect, setRegionalDialect, languageProficiency, setLanguageProficiency, languages } = useContext(LanguageContext);
  const { theme, setTheme, fontSize, setFontSize, highContrast, setHighContrast } = useContext(ThemeContext);

  // AI Feature Customization
  const [showAISuggestions, setShowAISuggestions] = useState(() => localStorage.getItem('showAISuggestions') === 'true');
  const [displayQuickReply, setDisplayQuickReply] = useState(() => localStorage.getItem('displayQuickReply') === 'true');
  const [viewRecentPromptHistory, setViewRecentPromptHistory] = useState(() => localStorage.getItem('viewRecentPromptHistory') === 'true');

  // Personal Dictionary
  const [personalDictionary, setPersonalDictionary] = useState(() => {
    try {
      const savedDict = localStorage.getItem('personalDictionary');
      return savedDict ? JSON.parse(savedDict) : [];
    } catch (e) {
      console.error("Failed to parse personal dictionary from localStorage", e);
      return [];
    }
  });
  const [newCustomWord, setNewCustomWord] = useState('');
  const [message, setMessage] = useState(null);
  const [showConfirmClearDict, setShowConfirmClearDict] = useState(false); // State for confirmation modal

  useEffect(() => {
    localStorage.setItem('showAISuggestions', showAISuggestions);
    localStorage.setItem('displayQuickReply', displayQuickReply);
    localStorage.setItem('viewRecentPromptHistory', viewRecentPromptHistory);
    localStorage.setItem('personalDictionary', JSON.stringify(personalDictionary));
  }, [showAISuggestions, displayQuickReply, viewRecentPromptHistory, personalDictionary]);

  const handleAddCustomWord = () => {
    const word = newCustomWord.trim().toLowerCase();
    if (word && !personalDictionary.includes(word)) {
      setPersonalDictionary([...personalDictionary, word]);
      setNewCustomWord('');
      setMessage({ title: t('messageBoxTitle'), message: t('customWordAdded') });
    } else if (word) {
      setMessage({ title: t('messageBoxTitle'), message: t('wordExists') });
    }
  };

  const handleDeleteCustomWord = (wordToDelete) => {
    setPersonalDictionary(personalDictionary.filter(word => word !== wordToDelete));
  };

  const handleImportDictionary = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedWords = JSON.parse(e.target.result);
          if (Array.isArray(importedWords)) {
            setPersonalDictionary(Array.from(new Set([...personalDictionary, ...importedWords.map(w => w.toLowerCase())])));
            setMessage({ title: t('messageBoxTitle'), message: t('dictionaryImported') });
          } else {
            throw new Error("Invalid dictionary format.");
          }
        } catch (error) {
          setMessage({ title: t('messageBoxTitle'), message: `${t('error')} ${error.message}` });
        }
      };
      reader.readAsText(file);
    }
  };

  const handleExportDictionary = () => {
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(personalDictionary, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "personal_dictionary.json");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  const handleClearDictionary = () => {
    setShowConfirmClearDict(true);
  };

  const confirmClearDictionary = () => {
    setPersonalDictionary([]);
    setMessage({ title: t('messageBoxTitle'), message: t('dictionaryCleared') });
    setShowConfirmClearDict(false);
  };

  const cancelClearDictionary = () => {
    setShowConfirmClearDict(false);
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-y-auto max-h-[calc(100vh-80px)]">
      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('settings')}</h2>

      {/* Multilingual Support */}
      <section className="mb-8 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">{t('multilingualSupport')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="ui-language" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('uiLanguage')}</label>
            <select
              id="ui-language"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
              value={uiLanguage}
              onChange={(e) => setUiLanguage(e.target.value)}
            >
              {Object.keys(languages).map((langCode) => (
                <option key={langCode} value={langCode}>{languages[langCode].name}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="writing-language" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('writingLanguage')}</label>
            <select
              id="writing-language"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
              value={writingLanguage}
              onChange={(e) => setWritingLanguage(e.target.value)}
            >
              {Object.keys(languages).map((langCode) => (
                <option key={langCode} value={langCode}>{languages[langCode].name}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="regional-dialect" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('regionalDialect')}</label>
            <select
              id="regional-dialect"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
              value={regionalDialect}
              onChange={(e) => setRegionalDialect(e.target.value)}
            >
              {languages[writingLanguage]?.dialects && Object.keys(languages[writingLanguage].dialects).map((dialectCode) => (
                <option key={dialectCode} value={dialectCode}>{languages[writingLanguage].dialects[dialectCode]}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="language-proficiency" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('languageProficiency')}</label>
            <select
              id="language-proficiency"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
              value={languageProficiency}
              onChange={(e) => setLanguageProficiency(e.target.value)}
            >
              <option value="nativeSpeaker">{t('nativeSpeaker')}</option>
              <option value="advancedLearner">{t('advancedLearner')}</option>
              <option value="intermediateLearner">{t('intermediateLearner')}</option>
              <option value="beginner">{t('beginner')}</option>
            </select>
          </div>
        </div>
      </section>

      {/* Generative AI Feature Customization */}
      <section className="mb-8 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">{t('generativeAIFeatures')}</h3>
        <div className="space-y-3">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="toggle-switch"
              checked={showAISuggestions}
              onChange={(e) => setShowAISuggestions(e.target.checked)}
            />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('showAISuggestions')}</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="toggle-switch"
              checked={displayQuickReply}
              onChange={(e) => setDisplayQuickReply(e.target.checked)}
            />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('displayQuickReply')}</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="toggle-switch"
              checked={viewRecentPromptHistory}
              onChange={(e) => setViewRecentPromptHistory(e.target.checked)}
            />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('viewRecentPromptHistory')}</span>
          </label>
        </div>
      </section>

      {/* Personal Dictionary */}
      <section className="mb-8 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">{t('personalDictionary')}</h3>
        <div className="flex mb-4">
          <input
            type="text"
            className="flex-grow p-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
            placeholder={t('addCustomWord')}
            value={newCustomWord}
            onChange={(e) => setNewCustomWord(e.target.value)}
            onKeyPress={(e) => { if (e.key === 'Enter') handleAddCustomWord(); }}
          />
          <button
            onClick={handleAddCustomWord}
            className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
          >
            {t('addCustomWord')}
          </button>
        </div>
        <div className="mb-4 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-2 bg-gray-50 dark:bg-gray-700">
          {personalDictionary.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">{t('noCustomWords')}</p>
          ) : (
            <ul className="list-disc list-inside text-gray-900 dark:text-gray-100">
              {personalDictionary.map((word, index) => (
                <li key={index} className="flex justify-between items-center py-1">
                  <span>{word}</span>
                  <button
                    onClick={() => handleDeleteCustomWord(word)}
                    className="text-red-500 hover:text-red-700 text-sm ml-2"
                    aria-label={`${t('deleteWord')} ${word}`}
                  >
                    {t('deleteWord')}
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
        <div className="flex flex-wrap gap-2">
          <input
            type="file"
            id="import-dictionary-file"
            className="hidden"
            accept=".json"
            onChange={handleImportDictionary}
          />
          <button
            onClick={() => document.getElementById('import-dictionary-file').click()}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            {t('importDictionary')}
          </button>
          <button
            onClick={handleExportDictionary}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            {t('exportDictionary')}
          </button>
          <button
            onClick={handleClearDictionary}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors duration-200"
          >
            {t('clearDictionary')}
          </button>
        </div>
      </section>

      {/* Appearance & Accessibility Settings */}
      <section className="mb-8 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">{t('appearanceAccessibility')}</h3>
        <div className="mb-4">
          <label htmlFor="theme" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('theme')}</label>
          <select
            id="theme"
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
            value={theme}
            onChange={(e) => setTheme(e.target.value)}
          >
            <option value="light">{t('lightMode')}</option>
            <option value="dark">{t('darkMode')}</option>
            <option value="systemDefault">{t('systemDefault')}</option>
          </select>
        </div>
        <div className="mb-4">
          <label htmlFor="font-size" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('fontSize')}</label>
          <select
            id="font-size"
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
            value={fontSize}
            onChange={(e) => setFontSize(e.target.value)}
          >
            <option value="small">{t('small')}</option>
            <option value="normal">{t('normal')}</option>
            <option value="large">{t('large')}</option>
          </select>
        </div>
        <label className="flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="toggle-switch"
            checked={highContrast}
            onChange={(e) => setHighContrast(e.target.checked)}
          />
          <span className="ml-3 text-gray-700 dark:text-gray-300">{t('highContrastMode')}</span>
        </label>
      </section>

      {/* Advanced Writing Assistance (Toggles for conceptual features) */}
      <section className="mb-8 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">{t('advancedWritingAssistance')}</h3>
        <div className="space-y-3">
          <label className="flex items-center cursor-pointer">
            <input type="checkbox" className="toggle-switch" defaultChecked disabled />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('realtimeSuggestions')}</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input type="checkbox" className="toggle-switch" defaultChecked disabled />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('toneDetection')}</span>
          </label>
          <div className="ml-8">
            <label htmlFor="adjust-tone" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('adjustTone')}</label>
            <select
              id="adjust-tone"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
              defaultValue="professional"
            >
              <option value="formal">{t('formal')}</option>
              <option value="casual">{t('casual')}</option>
              <option value="professional">{t('professional')}</option>
            </select>
          </div>
          <label className="flex items-center cursor-pointer">
            <input type="checkbox" className="toggle-switch" defaultChecked disabled />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('sentenceRewriting')}</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input type="checkbox" className="toggle-switch" defaultChecked disabled />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('consistencyTools')}</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input type="checkbox" className="toggle-switch" defaultChecked disabled />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('nonNativeAssistance')}</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input type="checkbox" className="toggle-switch" defaultChecked disabled />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('plagiarismDetection')}</span>
          </label>
          <label className="flex items-center cursor-pointer">
            <input type="checkbox" className="toggle-switch" defaultChecked disabled />
            <span className="ml-3 text-gray-700 dark:text-gray-300">{t('aiTextGeneration')}</span>
          </label>
        </div>
      </section>

      <div className="flex justify-end mt-6">
        <button
          onClick={onSave}
          className="px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 font-semibold"
        >
          {t('saveSettings')}
        </button>
      </div>

      {message && <MessageBox title={message.title} message={message.message} onClose={() => setMessage(null)} />}
      {showConfirmClearDict && (
        <MessageBox
          title={t('clearDictionary')}
          message={t('confirmClearDictionary')}
          onClose={cancelClearDictionary}
          onConfirm={confirmClearDictionary}
          showCancel={true}
        />
      )}
    </div>
  );
};

const TextEditor = () => {
  const { t } = useContext(LanguageContext);
  const { showAISuggestions, displayQuickReply, viewRecentPromptHistory } = useContext(SettingsContext);
  const [text, setText] = useState(() => localStorage.getItem('editorText') || '');
  const [aiPrompt, setAiPrompt] = useState('');
  const [aiResponse, setAiResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [promptHistory, setPromptHistory] = useState(() => {
    try {
      const savedHistory = localStorage.getItem('promptHistory');
      return savedHistory ? JSON.parse(savedHistory) : [];
    } catch (e) {
      console.error("Failed to parse prompt history from localStorage", e);
      return [];
    }
  });
  const [showPromptHistory, setShowPromptHistory] = useState(false);
  const [showAiToolsModal, setShowAiToolsModal] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const editorRef = useRef(null);

  useEffect(() => {
    localStorage.setItem('editorText', text);
  }, [text]);

  useEffect(() => {
    localStorage.setItem('promptHistory', JSON.stringify(promptHistory));
  }, [promptHistory]);

  const handleTextChange = (e) => {
    setText(e.target.value);
  };

  const handleSelectionChange = useCallback(() => {
    const editor = editorRef.current;
    if (editor && showAISuggestions) {
      const selection = window.getSelection();
      if (selection && selection.toString().length > 0) {
        setSelectedText(selection.toString());
        // Show AI suggestions modal or context menu
        // For this example, we'll just log it and potentially open a modal
        // console.log("Selected text:", selection.toString());
        // setShowAiToolsModal(true); // Can open modal on selection
      } else {
        setSelectedText('');
      }
    }
  }, [showAISuggestions]);

  useEffect(() => {
    const editor = editorRef.current;
    if (editor) {
      editor.addEventListener('mouseup', handleSelectionChange);
      editor.addEventListener('keyup', handleSelectionChange); // For keyboard selection
      return () => {
        editor.removeEventListener('mouseup', handleSelectionChange);
        editor.removeEventListener('keyup', handleSelectionChange);
      };
    }
  }, [handleSelectionChange]);


  const callGeminiAPI = async (prompt) => {
    setIsLoading(true);
    setError(null);
    setAiResponse('');

    const chatHistory = [];
    chatHistory.push({ role: "user", parts: [{ text: prompt }] });
    const payload = { contents: chatHistory };
    const apiKey = ""; // If you want to use models other than gemini-2.0-flash or imagen-3.0-generate-002, provide an API key here. Otherwise, leave this as-is.
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`API error: ${response.status} ${response.statusText} - ${errorData?.error?.message || 'Unknown error'}`);
      }

      const result = await response.json();

      if (result.candidates && result.candidates.length > 0 &&
        result.candidates[0].content && result.candidates[0].content.parts &&
        result.candidates[0].content.parts.length > 0) {
        const generatedText = result.candidates[0].content.parts[0].text;
        setAiResponse(generatedText);
        setPromptHistory(prev => [{ prompt, response: generatedText, timestamp: new Date().toISOString() }, ...prev].slice(0, 10)); // Keep last 10
      } else {
        setAiResponse(t('noSuggestions'));
      }
    } catch (err) {
      console.error("Error calling Gemini API:", err);
      setError(`${t('error')} ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBrainstorm = () => {
    const prompt = `Brainstorm ideas related to the following text: "${text.substring(0, 200)}..."`;
    setAiPrompt(prompt);
    callGeminiAPI(prompt);
    setShowAiToolsModal(true);
  };

  const handleRewriteSentence = () => {
    const textToRewrite = selectedText || text.split('. ')[0] || text; // Use selected, first sentence, or whole text
    const prompt = `Rewrite the following sentence for clarity and fluency: "${textToRewrite}"`;
    setAiPrompt(prompt);
    callGeminiAPI(prompt);
    setShowAiToolsModal(true);
  };

  const handleGenerateCustomPrompt = () => {
    callGeminiAPI(aiPrompt);
  };

  const handleCopy = (textToCopy) => {
    // Fallback for document.execCommand('copy') due to iFrame restrictions
    const textArea = document.createElement('textarea');
    textArea.value = textToCopy;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      // console.log('Text copied to clipboard');
      // Show a temporary message
      const originalText = document.getElementById('copy-button').textContent;
      document.getElementById('copy-button').textContent = t('copied');
      setTimeout(() => {
        document.getElementById('copy-button').textContent = originalText;
      }, 1500);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
    document.body.removeChild(textArea);
  };

  const handleUsePromptFromHistory = (prompt) => {
    setAiPrompt(prompt);
    setShowPromptHistory(false);
  };

  const AiToolsModal = ({ onClose }) => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-2xl w-full border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('aiPoweredTools')}</h3>

          {selectedText && showAISuggestions && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-md border border-blue-200 dark:border-blue-700">
              <p className="text-blue-800 dark:text-blue-200 font-medium">{t('selectedTextForSuggestions')}:</p>
              <p className="text-gray-700 dark:text-gray-300 italic">"{selectedText}"</p>
              <button
                onClick={handleRewriteSentence}
                className="mt-2 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                {t('rewriteSentence')}
              </button>
            </div>
          )}

          <div className="mb-4">
            <label htmlFor="ai-prompt-input" className="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">{t('aiPrompt')}</label>
            <textarea
              id="ai-prompt-input"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500 min-h-[80px]"
              placeholder={t('enterPrompt')}
              value={aiPrompt}
              onChange={(e) => setAiPrompt(e.target.value)}
            ></textarea>
            <button
              onClick={handleGenerateCustomPrompt}
              disabled={isLoading || !aiPrompt.trim()}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? t('loading') : t('generate')}
            </button>
          </div>

          {isLoading && (
            <div className="text-center text-blue-600 dark:text-blue-400 my-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400 mx-auto"></div>
              <p className="mt-2">{t('loading')}</p>
            </div>
          )}

          {error && (
            <div className="text-red-500 dark:text-red-400 my-4 p-3 bg-red-50 dark:bg-red-900 rounded-md border border-red-200 dark:border-red-700">
              {error}
            </div>
          )}

          {aiResponse && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">{t('aiResponse')}</h4>
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{aiResponse}</p>
              <button
                id="copy-button"
                onClick={() => handleCopy(aiResponse)}
                className="mt-2 px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
              >
                {t('copyToClipboard')}
              </button>
            </div>
          )}

          <div className="flex justify-end mt-6">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
            >
              {t('close')}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const PromptHistoryModal = ({ onClose }) => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-2xl w-full border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('promptHistoryTitle')}</h3>
          {promptHistory.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">{t('noPromptHistory')}</p>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {promptHistory.map((entry, index) => (
                <div key={index} className="p-3 border border-gray-200 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-700">
                  <p className="font-semibold text-gray-800 dark:text-gray-200 mb-1">{t('aiPrompt')}: <span className="font-normal">{entry.prompt}</span></p>
                  <p className="text-gray-700 dark:text-gray-300 text-sm mb-2">{t('aiResponse')}: <span className="italic">{entry.response.substring(0, 100)}...</span></p>
                  <p className="text-gray-500 dark:text-gray-400 text-xs">{(new Date(entry.timestamp)).toLocaleString()}</p>
                  <button
                    onClick={() => { handleUsePromptFromHistory(entry.prompt); onClose(); }}
                    className="mt-2 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    {t('usePrompt')}
                  </button>
                </div>
              ))}
            </div>
          )}
          <div className="flex justify-end mt-6">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
            >
              {t('close')}
            </button>
          </div>
        </div>
      </div>
    );
  };


  return (
    <div className="flex flex-col h-full p-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl">
      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">{t('textEditor')}</h2>

      <div className="flex-grow flex flex-col mb-4">
        <textarea
          ref={editorRef}
          className="flex-grow w-full p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500 resize-none leading-relaxed"
          placeholder={t('typeHere')}
          value={text}
          onChange={handleTextChange}
          aria-label={t('textEditor')}
        ></textarea>
      </div>

      {/* Quick Access Toolbar */}
      <div className="flex flex-wrap gap-3 justify-center mb-6">
        <button
          onClick={handleBrainstorm}
          className="px-5 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200 font-medium"
        >
          {t('brainstorm')}
        </button>
        <button
          onClick={handleRewriteSentence}
          disabled={!selectedText && !text.trim()}
          className="px-5 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {t('rewriteSentence')}
        </button>
        {displayQuickReply && (
          <button
            onClick={() => { /* Implement quick reply logic */ }}
            className="px-5 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 transition-colors duration-200 font-medium"
          >
            {t('quickReplies')}
          </button>
        )}
        {viewRecentPromptHistory && (
          <button
            onClick={() => setShowPromptHistory(true)}
            className="px-5 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors duration-200 font-medium"
          >
            {t('promptHistory')}
          </button>
        )}
      </div>

      {showAiToolsModal && <AiToolsModal onClose={() => setShowAiToolsModal(false)} />}
      {showPromptHistory && <PromptHistoryModal onClose={() => setShowPromptHistory(false)} />}
    </div>
  );
};

// Global Settings Context to pass AI feature toggles
const SettingsContext = createContext();

const SettingsProvider = ({ children }) => {
  const [showAISuggestions, setShowAISuggestions] = useState(() => localStorage.getItem('showAISuggestions') === 'true');
  const [displayQuickReply, setDisplayQuickReply] = useState(() => localStorage.getItem('displayQuickReply') === 'true');
  const [viewRecentPromptHistory, setViewRecentPromptHistory] = useState(() => localStorage.getItem('viewRecentPromptHistory') === 'true');

  const updateSettings = (newSettings) => {
    if (newSettings.hasOwnProperty('showAISuggestions')) {
      setShowAISuggestions(newSettings.showAISuggestions);
      localStorage.setItem('showAISuggestions', newSettings.showAISuggestions);
    }
    if (newSettings.hasOwnProperty('displayQuickReply')) {
      setDisplayQuickReply(newSettings.displayQuickReply);
      localStorage.setItem('displayQuickReply', newSettings.displayQuickReply);
    }
    if (newSettings.hasOwnProperty('viewRecentPromptHistory')) {
      setViewRecentPromptHistory(newSettings.viewRecentPromptHistory);
      localStorage.setItem('viewRecentPromptHistory', newSettings.viewRecentPromptHistory);
    }
  };

  return (
    <SettingsContext.Provider value={{
      showAISuggestions,
      displayQuickReply,
      viewRecentPromptHistory,
      updateSettings
    }}>
      {children}
    </SettingsContext.Provider>
  );
};


// --- Main App Component ---
function App() {
  const [currentPage, setCurrentPage] = useState('editor'); // 'editor' or 'settings'
  const { t } = useContext(LanguageContext);
  const { theme, fontSize, highContrast } = useContext(ThemeContext);
  const { updateSettings } = useContext(SettingsContext);

  const handleSaveSettings = () => {
    // SettingsPanel already updates localStorage directly.
    // This function can be used to trigger a re-render or confirm save if needed.
    // For now, it just switches back to the editor.
    // We need to re-read settings from localStorage to ensure context is updated.
    updateSettings({
      showAISuggestions: localStorage.getItem('showAISuggestions') === 'true',
      displayQuickReply: localStorage.getItem('displayQuickReply') === 'true',
      viewRecentPromptHistory: localStorage.getItem('viewRecentPromptHistory') === 'true',
    });
    setCurrentPage('editor');
  };

  const getThemeClasses = () => {
    let classes = '';
    if (theme === 'dark' || (theme === 'systemDefault' && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      classes += 'bg-gray-900 text-gray-100';
    } else {
      classes += 'bg-gray-100 text-gray-900';
    }
    if (highContrast) {
      classes += ' high-contrast-mode'; // Custom class for high contrast
    }
    return classes;
  };

  const getFontSizeClass = () => {
    switch (fontSize) {
      case 'small': return 'text-sm';
      case 'normal': return 'text-base';
      case 'large': return 'text-lg';
      default: return 'text-base';
    }
  };

  return (
    <div className={`min-h-screen flex flex-col font-inter ${getThemeClasses()} ${getFontSizeClass()}`}>
      {/* Header */}
      <header className="bg-blue-700 dark:bg-blue-900 text-white p-4 shadow-md flex justify-between items-center rounded-b-lg">
        <h1 className="text-2xl font-bold">{t('appTitle')}</h1>
        <nav>
          <button
            onClick={() => setCurrentPage('editor')}
            className={`px-4 py-2 rounded-md transition-colors duration-200 ${currentPage === 'editor' ? 'bg-blue-800 dark:bg-blue-950' : 'hover:bg-blue-600 dark:hover:bg-blue-800'}`}
          >
            {t('editor')}
          </button>
          <button
            onClick={() => setCurrentPage('settings')}
            className={`ml-3 px-4 py-2 rounded-md transition-colors duration-200 ${currentPage === 'settings' ? 'bg-blue-800 dark:bg-blue-950' : 'hover:bg-blue-600 dark:hover:bg-blue-800'}`}
          >
            {t('settings')}
          </button>
        </nav>
      </header>

      {/* Main Content Area */}
      <main className="flex-grow p-6 flex justify-center items-start">
        <div className="w-full max-w-4xl">
          {currentPage === 'editor' && <TextEditor />}
          {currentPage === 'settings' && <SettingsPanel onSave={handleSaveSettings} />}
        </div>
      </main>

      {/* Tailwind CSS for custom toggle switch and high contrast */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
          font-family: 'Inter', sans-serif;
          margin: 0;
          padding: 0;
          overflow: hidden; /* Prevent body scroll, let inner components scroll */
        }

        /* Custom Toggle Switch */
        .toggle-switch {
          appearance: none;
          width: 48px;
          height: 24px;
          border-radius: 9999px;
          background-color: #d1d5db; /* light gray */
          position: relative;
          cursor: pointer;
          transition: background-color 0.2s ease-in-out;
          outline: none;
        }

        .toggle-switch:checked {
          background-color: #2563eb; /* blue-600 */
        }

        .toggle-switch::before {
          content: '';
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: #ffffff;
          top: 2px;
          left: 2px;
          transition: transform 0.2s ease-in-out;
        }

        .toggle-switch:checked::before {
          transform: translateX(24px);
        }

        .toggle-switch:focus-visible {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); /* blue-500 with opacity */
        }

        /* High Contrast Mode */
        .high-contrast-mode {
          background-color: #000 !important;
          color: #fff !important;
        }
        .high-contrast-mode .bg-white {
          background-color: #000 !important;
          border-color: #fff !important;
        }
        .high-contrast-mode .dark\\:bg-gray-800 {
          background-color: #000 !important;
          border-color: #fff !important;
        }
        .high-contrast-mode .text-gray-900 {
          color: #fff !important;
        }
        .high-contrast-mode .dark\\:text-gray-100 {
          color: #fff !important;
        }
        .high-contrast-mode .text-gray-700 {
          color: #fff !important;
        }
        .high-contrast-mode .dark\\:text-gray-300 {
          color: #fff !important;
        }
        .high-contrast-mode .border-gray-200 {
          border-color: #fff !important;
        }
        .high-contrast-mode .dark\\:border-gray-700 {
          border-color: #fff !important;
        }
        .high-contrast-mode .bg-gray-50 {
          background-color: #333 !important;
        }
        .high-contrast-mode .dark\\:bg-gray-700 {
          background-color: #333 !important;
        }
        .high-contrast-mode .border-gray-300 {
          border-color: #fff !important;
        }
        .high-contrast-mode .dark\\:border-gray-600 {
          border-color: #fff !important;
        }
        .high-contrast-mode select,
        .high-contrast-mode textarea,
        .high-contrast-mode input[type="text"] {
          background-color: #333 !important;
          color: #fff !important;
          border-color: #fff !important;
        }
        .high-contrast-mode button {
          border: 1px solid #fff !important;
          color: #fff !important;
          background-color: #000 !important;
        }
        .high-contrast-mode button.bg-blue-600,
        .high-contrast-mode button.bg-green-600,
        .high-contrast-mode button.bg-purple-600,
        .high-contrast-mode button.bg-indigo-600,
        .high-contrast-mode button.bg-cyan-600,
        .high-contrast-mode button.bg-teal-600,
        .high-contrast-mode button.bg-red-500 {
          background-color: #000 !important;
          border-color: #fff !important;
          color: #fff !important;
        }
        .high-contrast-mode button:hover {
          background-color: #333 !important;
        }
        .high-contrast-mode .toggle-switch {
          background-color: #666 !important;
        }
        .high-contrast-mode .toggle-switch:checked {
          background-color: #fff !important;
        }
        .high-contrast-mode .toggle-switch::before {
          background-color: #000 !important;
        }
        .high-contrast-mode .toggle-switch:checked::before {
          background-color: #000 !important;
        }
        .high-contrast-mode .bg-blue-50 {
            background-color: #000 !important;
            border-color: #fff !important;
        }
        .high-contrast-mode .dark\\:bg-blue-900 {
            background-color: #000 !important;
            border-color: #fff !important;
        }
        .high-contrast-mode .text-blue-800 {
            color: #fff !important;
        }
        .high-contrast-mode .dark\\:text-blue-200 {
            color: #fff !important;
        }
        .high-contrast-mode .bg-red-50 {
            background-color: #000 !important;
            border-color: #fff !important;
        }
        .high-contrast-mode .dark\\:bg-red-900 {
            background-color: #000 !important;
            border-color: #fff !important;
        }
        .high-contrast-mode .text-red-500 {
            color: #fff !important;
        }
        .high-contrast-mode .dark\\:text-red-400 {
            color: #fff !important;
        }
      `}</style>
    </div>
  );
}

// Wrap App with all contexts
const AppWithProviders = () => (
  <LanguageProvider>
    <ThemeProvider>
      <SettingsProvider>
        <App />
      </SettingsProvider>
    </ThemeProvider>
  </LanguageProvider>
);

export default AppWithProviders;