/**
 * Enhanced appearance settings functionality for GrammarPro application
 * Fixes issues with font size selection and theme switching
 */

/**
 * Update font size in the editor
 * This enhanced function properly handles class changes
 */
function updateFontSize() {
  const editor = document.getElementById('textEditor');
  if (!editor) return;
  
  // Remove all font size classes first
  editor.classList.remove('font-small', 'font-normal', 'font-large');
  
  // Add the current font size class
  editor.classList.add(`font-${state.fontSize}`);
}

/**
 * Load settings into the UI
 * This enhanced function includes proper error handling
 */
function loadSettingsUI() {
  // Load current settings into UI
  const primaryLanguage = document.getElementById('primaryLanguage');
  if (primaryLanguage) {
    primaryLanguage.value = state.currentLanguage || 'en';
  }
  
  // Load toggles with error handling
  Object.keys(state.settings).forEach(key => {
    const toggle = document.getElementById(key);
    if (toggle) {
      toggle.checked = state.settings[key];
    }
  });
  
  // Load theme with error handling
  const themeInputs = document.querySelectorAll('input[name="theme"]');
  let themeFound = false;
  themeInputs.forEach(input => {
    if (input.value === state.theme) {
      input.checked = true;
      themeFound = true;
    }
  });
  
  // If theme not found, default to light
  if (!themeFound && themeInputs.length > 0) {
    themeInputs[0].checked = true;
  }
  
  // Load font size with error handling
  const fontSizeInputs = document.querySelectorAll('input[name="fontSize"]');
  let fontSizeFound = false;
  fontSizeInputs.forEach(input => {
    if (input.value === state.fontSize) {
      input.checked = true;
      fontSizeFound = true;
    }
  });
  
  // If font size not found, default to normal
  if (!fontSizeFound && fontSizeInputs.length > 0) {
    // Find the "normal" option
    for (const input of fontSizeInputs) {
      if (input.value === 'normal') {
        input.checked = true;
        break;
      }
    }
  }
}

/**
 * Save all settings from UI
 * This enhanced function includes proper error handling
 */
function saveAllSettings() {
  // Save language setting
  const langSelector = document.getElementById('primaryLanguage');
  if (langSelector) {
    state.currentLanguage = langSelector.value;
  }
  
  // Save toggles with error handling
  Object.keys(state.settings).forEach(key => {
    const toggle = document.getElementById(key);
    if (toggle) {
      state.settings[key] = toggle.checked;
    }
  });
  
  // Save theme with error handling
  const selectedTheme = document.querySelector('input[name="theme"]:checked');
  if (selectedTheme) {
    state.theme = selectedTheme.value;
  }
  
  // Save font size with error handling
  const selectedFontSize = document.querySelector('input[name="fontSize"]:checked');
  if (selectedFontSize) {
    state.fontSize = selectedFontSize.value;
  }
  
  // Apply settings
  try {
    saveSettings();
    updateLanguageUI();
    updateTheme();
    updateFontSize();
    closeSettings();
    showSettingsSavedFeedback();
  } catch (e) {
    console.error('Error applying settings:', e);
    showSettingsErrorFeedback();
  }
}

/**
 * Save settings to localStorage with error handling
 */
function saveSettings() {
  try {
    localStorage.setItem('grammarCheckerSettings', JSON.stringify(state));
    return true;
  } catch (e) {
    console.error('Failed to save settings:', e);
    return false;
  }
}

/**
 * Load settings from localStorage with error handling
 */
function loadSettings() {
  try {
    const saved = localStorage.getItem('grammarCheckerSettings');
    if (saved) {
      const savedState = JSON.parse(saved);
      // Merge saved state with default state to handle missing properties
      Object.assign(state, savedState);
    }
    return true;
  } catch (e) {
    console.error('Failed to load settings:', e);
    return false;
  }
}

/**
 * Show visual feedback when settings are saved
 */
function showSettingsSavedFeedback() {
  updateStatus(getTranslation('settingsSaved', state.currentLanguage));
  
  // Create visual feedback element
  const feedbackEl = document.createElement('div');
  feedbackEl.className = 'fixed bottom-4 right-4 bg-emerald-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 fade-in';
  feedbackEl.textContent = getTranslation('settingsSaved', state.currentLanguage);
  document.body.appendChild(feedbackEl);
  
  // Remove after delay
  setTimeout(() => {
    feedbackEl.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(feedbackEl)) {
        document.body.removeChild(feedbackEl);
      }
    }, 300);
  }, 2000);
}

/**
 * Show visual feedback when settings save fails
 */
function showSettingsErrorFeedback() {
  // Create visual feedback element
  const feedbackEl = document.createElement('div');
  feedbackEl.className = 'fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 fade-in';
  feedbackEl.textContent = 'Error saving settings';
  document.body.appendChild(feedbackEl);
  
  // Remove after delay
  setTimeout(() => {
    feedbackEl.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(feedbackEl)) {
        document.body.removeChild(feedbackEl);
      }
    }, 300);
  }, 2000);
}

/**
 * Apply a setting change with visual feedback
 * @param {string} setting - The setting name
 * @param {any} value - The new setting value
 */
function applySettingChange(setting, value) {
  // Update state
  if (typeof state.settings[setting] !== 'undefined') {
    state.settings[setting] = value;
  } else if (setting === 'theme') {
    state.theme = value;
    updateTheme();
  } else if (setting === 'fontSize') {
    state.fontSize = value;
    updateFontSize();
  }
  
  // Save the change
  saveSettings();
  
  // Show visual feedback
  const feedbackEl = document.createElement('div');
  feedbackEl.className = 'fixed bottom-4 right-4 bg-emerald-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 fade-in';
  feedbackEl.textContent = 'Setting updated';
  document.body.appendChild(feedbackEl);
  
  // Remove after delay
  setTimeout(() => {
    feedbackEl.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(feedbackEl)) {
        document.body.removeChild(feedbackEl);
      }
    }, 300);
  }, 2000);
}

/**
 * Initialize appearance settings functionality
 * Sets up event listeners and loads initial settings
 */
function initializeAppearanceSettings() {
  // Set up font size radio buttons
  document.querySelectorAll('input[name="fontSize"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
      state.fontSize = e.target.value;
      updateFontSize();
      applySettingChange('fontSize', e.target.value);
    });
  });
  
  // Set up theme radio buttons
  document.querySelectorAll('input[name="theme"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
      state.theme = e.target.value;
      updateTheme();
      applySettingChange('theme', e.target.value);
    });
  });
  
  // Set up settings save button
  const saveBtn = document.getElementById('saveSettings');
  if (saveBtn) {
    saveBtn.addEventListener('click', saveAllSettings);
  }
  
  // Load initial font size
  updateFontSize();
}
