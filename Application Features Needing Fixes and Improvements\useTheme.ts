import { useEffect, useState } from 'react';

export type Theme = 'light' | 'dark' | 'auto';

/**
 * Hook to manage theme functionality
 * @returns Object with theme state and functions
 */
export const useTheme = () => {
  const [theme, setTheme] = useState<Theme>('auto');
  const [isDark, setIsDark] = useState(false);
  
  // Apply theme when it changes
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark');
    
    // Determine if dark mode should be applied
    let shouldApplyDark = theme === 'dark';
    
    // For auto theme, check system preference
    if (theme === 'auto') {
      shouldApplyDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      // Listen for system theme changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        setIsDark(e.matches);
        root.classList.toggle('dark', e.matches);
      };
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
    
    // Apply theme class
    root.classList.add(shouldApplyDark ? 'dark' : 'light');
    setIsDark(shouldApplyDark);
    
    // Update CSS variables
    if (shouldApplyDark) {
      root.style.setProperty('--background', '#1a1a1a');
      root.style.setProperty('--foreground', '#ffffff');
      root.style.setProperty('--primary', '#3b82f6');
      root.style.setProperty('--primary-dark', '#2563eb');
      root.style.setProperty('--error-color', '#ef4444');
      root.style.setProperty('--success-color', '#22c55e');
      root.style.setProperty('--warning-color', '#f59e0b');
    } else {
      root.style.setProperty('--background', '#ffffff');
      root.style.setProperty('--foreground', '#1a1a1a');
      root.style.setProperty('--primary', '#2563eb');
      root.style.setProperty('--primary-dark', '#1d4ed8');
      root.style.setProperty('--error-color', '#dc2626');
      root.style.setProperty('--success-color', '#16a34a');
      root.style.setProperty('--warning-color', '#d97706');
    }
  }, [theme]);
  
  /**
   * Toggle between light, dark, and auto themes
   */
  const toggleTheme = () => {
    setTheme(current => {
      if (current === 'light') return 'dark';
      if (current === 'dark') return 'auto';
      return 'light';
    });
  };
  
  return {
    theme,
    setTheme,
    toggleTheme,
    isDark
  };
};

/**
 * Add CSS for theme transitions
 */
export const addThemeTransitionStyles = () => {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    :root {
      --background: #ffffff;
      --foreground: #1a1a1a;
      --primary: #2563eb;
      --primary-dark: #1d4ed8;
      --primary-foreground: #ffffff;
      --secondary: #f3f4f6;
      --secondary-foreground: #1f2937;
      --accent: #f3f4f6;
      --accent-foreground: #1f2937;
      --destructive: #ef4444;
      --destructive-foreground: #ffffff;
      --ring: #e5e7eb;
      --radius: 0.5rem;
      --error-color: #dc2626;
      --success-color: #16a34a;
      --warning-color: #d97706;
    }
    
    .dark {
      --background: #1a1a1a;
      --foreground: #ffffff;
      --primary: #3b82f6;
      --primary-dark: #2563eb;
      --primary-foreground: #ffffff;
      --secondary: #1f2937;
      --secondary-foreground: #f3f4f6;
      --accent: #1f2937;
      --accent-foreground: #f3f4f6;
      --destructive: #ef4444;
      --destructive-foreground: #ffffff;
      --ring: #1f2937;
      --error-color: #ef4444;
      --success-color: #22c55e;
      --warning-color: #f59e0b;
    }
    
    * {
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
    
    body {
      background-color: var(--background);
      color: var(--foreground);
    }
  `;
  document.head.appendChild(styleElement);
};
