import { useEffect } from 'react';
import { useLanguageContext } from './components/LanguageProvider';
import { Sidebar } from './components/Sidebar';
import { Header } from './components/Header';
import { SettingsModal } from './components/SettingsModal';
import { DictionaryModal } from './components/DictionaryModal';
import { HelpModal } from './components/HelpModal';

function App() {
  const { currentLanguage, updateLanguage, detectSystemLanguage } = useLanguageContext();
  
  // Initialize app settings on first load
  useEffect(() => {
    // Load saved language or detect from browser
    const savedLanguage = localStorage.getItem('preferredLanguage');
    if (savedLanguage && savedLanguage !== currentLanguage) {
      updateLanguage(savedLanguage as any);
    } else {
      const detectedLanguage = detectSystemLanguage();
      if (detectedLanguage !== currentLanguage) {
        updateLanguage(detectedLanguage);
      }
    }
    
    // Theme is initialized in ThemeProvider
  }, [currentLanguage, updateLanguage, detectSystemLanguage]);

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
        <Sidebar />
        
        <div className="lg:col-span-3">
          <div className="border rounded-lg p-4 min-h-[500px] relative">
            <textarea 
              className="w-full h-full min-h-[500px] resize-none focus:outline-none bg-transparent"
              placeholder="Start writing here..."
            />
          </div>
        </div>
      </main>
      
      {/* Modals */}
      <SettingsModal />
      <DictionaryModal />
      <HelpModal />
    </div>
  );
}

export default App;
