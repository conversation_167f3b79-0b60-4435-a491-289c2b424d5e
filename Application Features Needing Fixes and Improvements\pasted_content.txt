<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Multilingual Grammar Checker - Enhanced Writing Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #059669;
            --secondary-color: #0f766e;
            --accent-color: #06b6d4;
            --error-color: #dc2626;
            --warning-color: #d97706;
            --success-color: #16a34a;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --border-color: #e5e7eb;
        }

        [data-theme="dark"] {
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --border-color: #374151;
        }

        body {
            color: var(--text-primary);
            background-color: var(--bg-primary);
            transition: all 0.3s ease;
        }

        .rtl {
            direction: rtl;
            text-align: right;
        }

        .grammar-error {
            border-bottom: 2px wavy var(--error-color);
            cursor: pointer;
        }

        .spelling-error {
            border-bottom: 2px wavy #dc2626;
            cursor: pointer;
        }

        .style-suggestion {
            border-bottom: 2px wavy var(--success-color);
            cursor: pointer;
        }

        .clarity-improvement {
            border-bottom: 2px wavy var(--warning-color);
            cursor: pointer;
        }

        .editor-container {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
        }

        .settings-panel {
            max-height: 800px;
            overflow-y: auto;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            text-align: center;
            border-radius: 6px;
            padding: 5px 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .font-small { font-size: 0.875rem; }
        .font-normal { font-size: 1rem; }
        .font-large { font-size: 1.125rem; }

        .writing-stats {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .ai-suggestion {
            background: linear-gradient(135deg, #8b5cf6, #a855f7);
            color: white;
        }

        .suggestion-card {
            transition: all 0.2s ease;
        }

        .suggestion-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .error-highlight {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            height: 4px;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .language-flag {
            width: 20px;
            height: 15px;
            display: inline-block;
            margin-right: 8px;
            background-size: cover;
            border-radius: 2px;
        }

        .flag-en { background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHZpZXdCb3g9IjAgMCAyMCAxNSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE1IiBmaWxsPSIjMDA1MkI0Ii8+CjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'); }
        .flag-tr { background-color: #e30a17; }
        .flag-ar { background-color: #007a3d; }
        .flag-es { background-color: #aa151b; }
        .flag-de { background-color: #000000; }
        .flag-fr { background-color: #0055a4; }
        .flag-nl { background-color: #21468b; }
        .flag-it { background-color: #009246; }

        @media (max-width: 768px) {
            .mobile-hidden { display: none; }
            .editor-container { min-height: 300px; }
        }

        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        .settings-section {
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 1rem;
            padding-bottom: 1rem;
        }

        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div id="app" class="min-h-screen bg-gray-50 transition-all duration-300">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-spell-check text-2xl text-emerald-600"></i>
                            <h1 class="text-xl font-bold text-gray-900">GrammarPro</h1>
                        </div>
                        <div class="hidden md:flex items-center space-x-2">
                            <span class="text-sm text-gray-500">Current Language:</span>
                            <select id="languageSelector" class="text-sm border border-gray-300 rounded px-2 py-1 bg-white">
                                <option value="en">🇺🇸 English</option>
                                <option value="tr">🇹🇷 Turkish</option>
                                <option value="ar">🇸🇦 Arabic</option>
                                <option value="es">🇪🇸 Spanish</option>
                                <option value="de">🇩🇪 German</option>
                                <option value="fr">🇫🇷 French</option>
                                <option value="nl">🇳🇱 Dutch</option>
                                <option value="it">🇮🇹 Italian</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button id="settingsBtn" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-cog text-lg"></i>
                        </button>
                        <button id="themeToggle" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-moon text-lg"></i>
                        </button>
                        <button id="helpBtn" class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-question-circle text-lg"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Left Sidebar - Tools -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 space-y-4">
                        <h3 class="font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-tools mr-2 text-emerald-600"></i>
                            Writing Tools
                        </h3>
                        
                        <!-- Writing Mode -->
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700">Writing Mode</label>
                            <select id="writingMode" class="w-full border border-gray-300 rounded px-3 py-2 text-sm bg-white">
                                <option value="casual">📝 Casual</option>
                                <option value="formal">🏢 Formal</option>
                                <option value="academic">🎓 Academic</option>
                                <option value="creative">🎨 Creative</option>
                                <option value="business">💼 Business</option>
                            </select>
                        </div>

                        <!-- File Upload -->
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700">Import Document</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-emerald-400 transition-colors cursor-pointer" id="fileDropZone">
                                <i class="fas fa-cloud-upload-alt text-2xl text-gray-400 mb-2"></i>
                                <p class="text-sm text-gray-500">Drop files here or <span class="text-emerald-600">browse</span></p>
                                <input type="file" id="fileInput" class="hidden" accept=".txt,.docx,.pdf">
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700">Quick Actions</label>
                            <div class="grid grid-cols-2 gap-2">
                                <button id="checkGrammarBtn" class="btn-secondary text-xs p-2">
                                    <i class="fas fa-check mr-1"></i>Check All
                                </button>
                                <button id="aiRewriteBtn" class="btn-secondary text-xs p-2">
                                    <i class="fas fa-robot mr-1"></i>AI Rewrite
                                </button>
                                <button id="exportBtn" class="btn-secondary text-xs p-2">
                                    <i class="fas fa-download mr-1"></i>Export
                                </button>
                                <button id="clearBtn" class="btn-secondary text-xs p-2">
                                    <i class="fas fa-trash mr-1"></i>Clear
                                </button>
                            </div>
                        </div>

                        <!-- Writing Stats -->
                        <div class="writing-stats rounded-lg p-4 space-y-2">
                            <h4 class="font-medium">Writing Statistics</h4>
                            <div class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <span>Words:</span>
                                    <span id="wordCount">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Characters:</span>
                                    <span id="charCount">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Errors:</span>
                                    <span id="errorCount">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Score:</span>
                                    <span id="qualityScore">100%</span>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="bg-white bg-opacity-20 rounded-full h-2">
                                    <div id="qualityBar" class="progress-bar w-full h-full rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Editor -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <!-- Editor Toolbar -->
                        <div class="border-b border-gray-200 px-4 py-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <h3 class="font-semibold text-gray-900">Document Editor</h3>
                                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                                        <i class="fas fa-circle text-green-500 text-xs"></i>
                                        <span id="statusText">Ready</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button id="undoBtn" class="p-1 text-gray-400 hover:text-gray-600 tooltip">
                                        <i class="fas fa-undo"></i>
                                        <span class="tooltiptext">Undo (Ctrl+Z)</span>
                                    </button>
                                    <button id="redoBtn" class="p-1 text-gray-400 hover:text-gray-600 tooltip">
                                        <i class="fas fa-redo"></i>
                                        <span class="tooltiptext">Redo (Ctrl+Y)</span>
                                    </button>
                                    <button id="aiAssistBtn" class="p-1 text-purple-500 hover:text-purple-600 tooltip">
                                        <i class="fas fa-magic"></i>
                                        <span class="tooltiptext">AI Assistant</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Editor Content -->
                        <div class="editor-container custom-scrollbar">
                            <div contenteditable="true" id="textEditor" class="p-6 min-h-96 focus:outline-none text-gray-900 leading-relaxed" 
                                 placeholder="Start writing your text here... The grammar checker will provide real-time suggestions as you type.">
                            </div>
                        </div>

                        <!-- Editor Footer -->
                        <div class="border-t border-gray-200 px-4 py-2 bg-gray-50 rounded-b-lg">
                            <div class="flex items-center justify-between text-xs text-gray-500">
                                <div class="flex items-center space-x-4">
                                    <span>Last saved: <span id="lastSaved">Never</span></span>
                                    <span>Language: <span id="currentLang">English</span></span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span id="selectionInfo"></span>
                                    <span>|</span>
                                    <span>Line <span id="lineNumber">1</span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Suggestion Panel -->
                    <div id="aiPanel" class="mt-4 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-sm p-4 text-white hidden">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium flex items-center">
                                <i class="fas fa-robot mr-2"></i>
                                AI Writing Assistant
                            </h4>
                            <button id="closeAiPanel" class="text-white hover:text-gray-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div id="aiSuggestions" class="space-y-2">
                            <div class="bg-white bg-opacity-20 rounded p-3">
                                <p class="text-sm mb-2">💡 Try rewriting this sentence for better clarity:</p>
                                <p class="text-sm italic">"The quick brown fox jumps over the lazy dog with remarkable agility and grace."</p>
                                <button class="mt-2 bg-white text-purple-600 px-3 py-1 rounded text-xs font-medium hover:bg-gray-100 transition-colors">
                                    Apply Suggestion
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Sidebar - Suggestions -->
                <div class="lg:col-span-1">
                    <div class="space-y-4">
                        <!-- Current Suggestions -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                                Suggestions
                            </h3>
                            <div id="suggestionsList" class="space-y-3">
                                <div class="text-sm text-gray-500 text-center py-8">
                                    <i class="fas fa-search text-2xl mb-2 text-gray-300"></i>
                                    <p>Start typing to see suggestions</p>
                                </div>
                            </div>
                        </div>

                        <!-- Tone Analysis -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                                Tone Analysis
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Confidence</span>
                                    <span class="text-sm font-medium text-emerald-600">95%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Formality</span>
                                    <span class="text-sm font-medium text-blue-600">Professional</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Emotion</span>
                                    <span class="text-sm font-medium text-purple-600">Neutral</span>
                                </div>
                                <button id="adjustToneBtn" class="w-full bg-blue-50 text-blue-600 px-3 py-2 rounded text-sm font-medium hover:bg-blue-100 transition-colors">
                                    <i class="fas fa-adjust mr-1"></i>
                                    Adjust Tone
                                </button>
                            </div>
                        </div>

                        <!-- Recent Prompts -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                            <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-history mr-2 text-green-500"></i>
                                Recent AI Prompts
                            </h3>
                            <div id="recentPrompts" class="space-y-2">
                                <div class="text-xs text-gray-500 bg-gray-50 rounded p-2 cursor-pointer hover:bg-gray-100 transition-colors">
                                    "Make this more formal"
                                </div>
                                <div class="text-xs text-gray-500 bg-gray-50 rounded p-2 cursor-pointer hover:bg-gray-100 transition-colors">
                                    "Improve clarity"
                                </div>
                                <div class="text-xs text-gray-500 bg-gray-50 rounded p-2 cursor-pointer hover:bg-gray-100 transition-colors">
                                    "Add more details"
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settingsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-hidden">
                    <div class="flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900">Settings</h2>
                        <button id="closeSettings" class="text-gray-400 hover:text-gray-500">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div class="flex">
                        <!-- Settings Navigation -->
                        <div class="w-1/4 bg-gray-50 p-4 border-r border-gray-200">
                            <nav class="space-y-2">
                                <button class="settings-nav-item w-full text-left px-3 py-2 rounded hover:bg-gray-200 transition-colors" data-section="language">
                                    <i class="fas fa-globe mr-2"></i>Language
                                </button>
                                <button class="settings-nav-item w-full text-left px-3 py-2 rounded hover:bg-gray-200 transition-colors" data-section="features">
                                    <i class="fas fa-cogs mr-2"></i>Features
                                </button>
                                <button class="settings-nav-item w-full text-left px-3 py-2 rounded hover:bg-gray-200 transition-colors" data-section="dictionary">
                                    <i class="fas fa-book mr-2"></i>Dictionary
                                </button>
                                <button class="settings-nav-item w-full text-left px-3 py-2 rounded hover:bg-gray-200 transition-colors" data-section="appearance">
                                    <i class="fas fa-palette mr-2"></i>Appearance
                                </button>
                                <button class="settings-nav-item w-full text-left px-3 py-2 rounded hover:bg-gray-200 transition-colors" data-section="advanced">
                                    <i class="fas fa-tools mr-2"></i>Advanced
                                </button>
                            </nav>
                        </div>

                        <!-- Settings Content -->
                        <div class="flex-1 p-6 settings-panel custom-scrollbar">
                            <!-- Language Settings -->
                            <div id="languageSettings" class="settings-section">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Language Settings</h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Primary Language</label>
                                        <select id="primaryLanguage" class="w-full border border-gray-300 rounded px-3 py-2">
                                            <option value="auto">🌐 Auto-detect (System Default)</option>
                                            <option value="en">🇺🇸 English</option>
                                            <option value="tr">🇹🇷 Turkish</option>
                                            <option value="ar">🇸🇦 Arabic</option>
                                            <option value="es">🇪🇸 Spanish</option>
                                            <option value="de">🇩🇪 German</option>
                                            <option value="fr">🇫🇷 French</option>
                                            <option value="nl">🇳🇱 Dutch</option>
                                            <option value="it">🇮🇹 Italian</option>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">This will be used as your default writing language</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Dialect/Region</label>
                                        <select id="dialectSelect" class="w-full border border-gray-300 rounded px-3 py-2">
                                            <option value="us">🇺🇸 American English</option>
                                            <option value="uk">🇬🇧 British English</option>
                                            <option value="au">🇦🇺 Australian English</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Language Proficiency</label>
                                        <div class="flex items-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" name="proficiency" value="native" class="mr-2">
                                                <span class="text-sm">Native Speaker</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="proficiency" value="advanced" class="mr-2" checked>
                                                <span class="text-sm">Advanced</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="proficiency" value="intermediate" class="mr-2">
                                                <span class="text-sm">Intermediate</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="proficiency" value="beginner" class="mr-2">
                                                <span class="text-sm">Beginner</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div>
                                        <div class="flex items-center justify-between">
                                            <label class="text-sm font-medium text-gray-700">RTL Support (Right-to-Left)</label>
                                            <label class="toggle-switch">
                                                <input type="checkbox" id="rtlSupport">
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Automatically enable for Arabic and other RTL languages</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Feature Settings -->
                            <div id="featureSettings" class="settings-section">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Feature Customization</h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Generative AI</h4>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-sm font-medium text-gray-700">Show AI on text selection</label>
                                                    <p class="text-xs text-gray-500">Display AI suggestions when text is selected</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="aiOnSelection" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-sm font-medium text-gray-700">Quick AI replies</label>
                                                    <p class="text-xs text-gray-500">Generate quick response suggestions</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="quickReplies" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-sm font-medium text-gray-700">Show recent prompt history</label>
                                                    <p class="text-xs text-gray-500">Keep track of recent AI prompts</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="promptHistory" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Writing Assistance</h4>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm font-medium text-gray-700">Grammar checking</label>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="grammarCheck" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm font-medium text-gray-700">Spell checking</label>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="spellCheck" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm font-medium text-gray-700">Style suggestions</label>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="styleCheck" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm font-medium text-gray-700">Tone detection</label>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="toneDetection" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm font-medium text-gray-700">Plagiarism checking</label>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="plagiarismCheck">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Dictionary Settings -->
                            <div id="dictionarySettings" class="settings-section">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Dictionary</h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Add words to your dictionary</label>
                                        <div class="flex space-x-2">
                                            <input type="text" id="newWord" placeholder="Enter word..." class="flex-1 border border-gray-300 rounded px-3 py-2">
                                            <button id="addWordBtn" class="bg-emerald-600 text-white px-4 py-2 rounded hover:bg-emerald-700 transition-colors">
                                                Add
                                            </button>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Added words will not be flagged as misspellings</p>
                                    </div>

                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <label class="text-sm font-medium text-gray-700">Your Dictionary</label>
                                            <div class="space-x-2">
                                                <button id="importDict" class="text-xs text-emerald-600 hover:text-emerald-700">Import</button>
                                                <button id="exportDict" class="text-xs text-emerald-600 hover:text-emerald-700">Export</button>
                                            </div>
                                        </div>
                                        <div id="dictionaryList" class="max-h-40 overflow-y-auto border border-gray-200 rounded p-2 bg-gray-50">
                                            <div class="space-y-1">
                                                <div class="flex items-center justify-between text-sm">
                                                    <span>grammarpro</span>
                                                    <button class="text-red-500 hover:text-red-700 text-xs">Remove</button>
                                                </div>
                                                <div class="flex items-center justify-between text-sm">
                                                    <span>multilingual</span>
                                                    <button class="text-red-500 hover:text-red-700 text-xs">Remove</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Language-specific dictionaries</label>
                                        <select class="w-full border border-gray-300 rounded px-3 py-2">
                                            <option>English Dictionary (25 words)</option>
                                            <option>Turkish Dictionary (12 words)</option>
                                            <option>Arabic Dictionary (8 words)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Appearance Settings -->
                            <div id="appearanceSettings" class="settings-section">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Appearance Settings</h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                                        <div class="grid grid-cols-3 gap-3">
                                            <label class="flex items-center p-3 border border-gray-300 rounded cursor-pointer hover:border-emerald-500 transition-colors">
                                                <input type="radio" name="theme" value="light" class="mr-2" checked>
                                                <div class="flex flex-col items-center">
                                                    <i class="fas fa-sun text-yellow-500 text-lg mb-1"></i>
                                                    <span class="text-xs">Light</span>
                                                </div>
                                            </label>
                                            <label class="flex items-center p-3 border border-gray-300 rounded cursor-pointer hover:border-emerald-500 transition-colors">
                                                <input type="radio" name="theme" value="dark" class="mr-2">
                                                <div class="flex flex-col items-center">
                                                    <i class="fas fa-moon text-blue-500 text-lg mb-1"></i>
                                                    <span class="text-xs">Dark</span>
                                                </div>
                                            </label>
                                            <label class="flex items-center p-3 border border-gray-300 rounded cursor-pointer hover:border-emerald-500 transition-colors">
                                                <input type="radio" name="theme" value="auto" class="mr-2">
                                                <div class="flex flex-col items-center">
                                                    <i class="fas fa-adjust text-gray-500 text-lg mb-1"></i>
                                                    <span class="text-xs">Auto</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                                        <div class="flex items-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" name="fontSize" value="small" class="mr-2">
                                                <span class="text-sm">Small</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="fontSize" value="normal" class="mr-2" checked>
                                                <span class="text-sm">Normal</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="fontSize" value="large" class="mr-2">
                                                <span class="text-sm">Large</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Error Highlighting Colors</h4>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm text-gray-700">Grammar errors</label>
                                                <input type="color" value="#dc2626" class="w-8 h-8 border border-gray-300 rounded">
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm text-gray-700">Spelling errors</label>
                                                <input type="color" value="#dc2626" class="w-8 h-8 border border-gray-300 rounded">
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm text-gray-700">Style suggestions</label>
                                                <input type="color" value="#16a34a" class="w-8 h-8 border border-gray-300 rounded">
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <label class="text-sm text-gray-700">Clarity improvements</label>
                                                <input type="color" value="#d97706" class="w-8 h-8 border border-gray-300 rounded">
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <div class="flex items-center justify-between">
                                            <label class="text-sm font-medium text-gray-700">High contrast mode</label>
                                            <label class="toggle-switch">
                                                <input type="checkbox" id="highContrast">
                                                <span class="slider"></span>
                                            </label>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">Improves accessibility for visually impaired users</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Advanced Settings -->
                            <div id="advancedSettings" class="settings-section">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Advanced Settings</h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Performance</h4>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-sm font-medium text-gray-700">Real-time checking</label>
                                                    <p class="text-xs text-gray-500">Check grammar as you type</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="realTimeCheck" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Check delay (ms)</label>
                                                <input type="range" min="100" max="2000" value="500" class="w-full" id="checkDelay">
                                                <div class="flex justify-between text-xs text-gray-500">
                                                    <span>Fast (100ms)</span>
                                                    <span>Slow (2000ms)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Privacy & Data</h4>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-sm font-medium text-gray-700">Save writing history</label>
                                                    <p class="text-xs text-gray-500">Store documents locally for quick access</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="saveHistory" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-sm font-medium text-gray-700">Analytics</label>
                                                    <p class="text-xs text-gray-500">Help improve the service with usage data</p>
                                                </div>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="analytics">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Keyboard Shortcuts</h4>
                                        <div class="space-y-2 text-sm">
                                            <div class="flex justify-between">
                                                <span>Check grammar</span>
                                                <code class="bg-gray-100 px-2 py-1 rounded">Ctrl+G</code>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>AI assistant</span>
                                                <code class="bg-gray-100 px-2 py-1 rounded">Ctrl+Shift+A</code>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>Settings</span>
                                                <code class="bg-gray-100 px-2 py-1 rounded">Ctrl+,</code>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>Export document</span>
                                                <code class="bg-gray-100 px-2 py-1 rounded">Ctrl+E</code>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 class="text-md font-medium text-gray-800 mb-3">Reset & Backup</h4>
                                        <div class="space-y-3">
                                            <button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 transition-colors">
                                                <i class="fas fa-download mr-2"></i>
                                                Export Settings
                                            </button>
                                            <button class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 transition-colors">
                                                <i class="fas fa-upload mr-2"></i>
                                                Import Settings
                                            </button>
                                            <button class="w-full bg-red-100 text-red-700 px-4 py-2 rounded hover:bg-red-200 transition-colors">
                                                <i class="fas fa-undo mr-2"></i>
                                                Reset to Defaults
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 px-6 py-4 bg-gray-50">
                        <div class="flex justify-end space-x-3">
                            <button id="cancelSettings" class="px-4 py-2 text-gray-700 border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                                Cancel
                            </button>
                            <button id="saveSettings" class="px-4 py-2 bg-emerald-600 text-white rounded hover:bg-emerald-700 transition-colors">
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Modal -->
        <div id="helpModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                    <div class="flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900">Help & Tutorial</h2>
                        <button id="closeHelp" class="text-gray-400 hover:text-gray-500">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="p-6">
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Getting Started</h3>
                                <ol class="list-decimal list-inside space-y-2 text-gray-700">
                                    <li>Select your primary language from the header dropdown</li>
                                    <li>Choose your writing mode (casual, formal, academic, creative)</li>
                                    <li>Start typing in the editor or upload a document</li>
                                    <li>Review suggestions in the right sidebar</li>
                                    <li>Click on highlighted errors to see corrections</li>
                                </ol>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Error Types</h3>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <div class="w-4 h-1 bg-red-500 mr-3"></div>
                                        <span class="text-gray-700">Grammar and spelling errors</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-1 bg-green-500 mr-3"></div>
                                        <span class="text-gray-700">Style and fluency suggestions</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-4 h-1 bg-yellow-500 mr-3"></div>
                                        <span class="text-gray-700">Clarity improvements</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-3">Keyboard Shortcuts</h3>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>Ctrl+G - Check grammar</div>
                                    <div>Ctrl+Shift+A - AI assistant</div>
                                    <div>Ctrl+, - Open settings</div>
                                    <div>Ctrl+E - Export document</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-30 z-40 hidden">
            <div class="flex items-center justify-center min-h-screen">
                <div class="bg-white rounded-lg p-6 shadow-xl">
                    <div class="flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-600"></div>
                        <span class="text-gray-900">Processing...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-secondary:hover {
            background-color: var(--border-color);
        }

        .settings-nav-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        .settings-nav-item.active:hover {
            background-color: var(--secondary-color);
        }
    </style>

    <script>
        // Application State
        const state = {
            currentLanguage: 'en',
            theme: 'light',
            fontSize: 'normal',
            rtlEnabled: false,
            settings: {
                aiOnSelection: true,
                quickReplies: true,
                promptHistory: true,
                grammarCheck: true,
                spellCheck: true,
                styleCheck: true,
                toneDetection: true,
                plagiarismCheck: false,
                realTimeCheck: true,
                saveHistory: true,
                analytics: false,
                highContrast: false,
                rtlSupport: false
            },
            personalDictionary: ['grammarpro', 'multilingual'],
            recentPrompts: [
                "Make this more formal",
                "Improve clarity",
                "Add more details"
            ],
            writingStats: {
                words: 0,
                characters: 0,
                errors: 0,
                score: 100
            }
        };

        // Language Configuration
        const languages = {
            en: { name: 'English', rtl: false, flag: '🇺🇸' },
            tr: { name: 'Turkish', rtl: false, flag: '🇹🇷' },
            ar: { name: 'Arabic', rtl: true, flag: '🇸🇦' },
            es: { name: 'Spanish', rtl: false, flag: '🇪🇸' },
            de: { name: 'German', rtl: false, flag: '🇩🇪' },
            fr: { name: 'French', rtl: false, flag: '🇫🇷' },
            nl: { name: 'Dutch', rtl: false, flag: '🇳🇱' },
            it: { name: 'Italian', rtl: false, flag: '🇮🇹' }
        };

        // Grammar Rules Database (Mock)
        const grammarRules = {
            en: [
                { pattern: /\b(there|their|they're)\b/gi, suggestions: ['Check if you mean "there", "their", or "they\'re"'] },
                { pattern: /\b(your|you're)\b/gi, suggestions: ['Check if you mean "your" or "you\'re"'] },
                { pattern: /\b(its|it's)\b/gi, suggestions: ['Check if you mean "its" or "it\'s"'] }
            ],
            tr: [
                { pattern: /\b(de|da)\b/g, suggestions: ['Check vowel harmony for "de/da"'] },
                { pattern: /\b\w+lık\b/g, suggestions: ['Check suffix harmony'] },
                { pattern: /\b\w+den\b/g, suggestions: ['Verify ablative case usage'] }
            ],
            ar: [
                { pattern: /ال\w+/g, suggestions: ['Check definite article usage'] },
                { pattern: /\w+ة/g, suggestions: ['Verify feminine ending'] }
            ],
            es: [
                { pattern: /\b(ser|estar)\b/gi, suggestions: ['Check if you need "ser" or "estar"'] },
                { pattern: /\b(por|para)\b/gi, suggestions: ['Check if you need "por" or "para"'] }
            ],
            de: [
                { pattern: /\b(der|die|das)\b/gi, suggestions: ['Check article gender'] },
                { pattern: /\b\w+ss\b/g, suggestions: ['Consider if "ß" is needed'] }
            ],
            fr: [
                { pattern: /\b(du|de la|des)\b/gi, suggestions: ['Check partitive article'] },
                { pattern: /\b\w+é\w*\b/g, suggestions: ['Check accent marks'] }
            ],
            nl: [
                { pattern: /\b(de|het)\b/gi, suggestions: ['Check article gender'] },
                { pattern: /\bij\b/g, suggestions: ['Check if you mean "bij" or "bijj"'] }
            ],
            it: [
                { pattern: /\b(il|la|lo|gli|le)\b/gi, suggestions: ['Check article agreement'] },
                { pattern: /\b\w+zione\b/g, suggestions: ['Check feminine ending for -zione words'] }
            ]
        };

        // Initialize Application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupEventListeners();
            loadSettings();
            detectSystemLanguage();
        });

        function initializeApp() {
            // Set initial theme
            updateTheme();
            updateFontSize();
            
            // Initialize editor
            setupEditor();
            
            // Load saved settings
            loadSettings();
            
            // Initialize sample text
            const editor = document.getElementById('textEditor');
            editor.innerHTML = getSampleText(state.currentLanguage);
            
            // Start real-time checking
            setTimeout(() => {
                analyzeText();
            }, 1000);
        }

        function setupEventListeners() {
            // Header controls
            document.getElementById('languageSelector').addEventListener('change', changeLanguage);
            document.getElementById('settingsBtn').addEventListener('click', openSettings);
            document.getElementById('themeToggle').addEventListener('click', toggleTheme);
            document.getElementById('helpBtn').addEventListener('click', openHelp);

            // Editor
            const editor = document.getElementById('textEditor');
            editor.addEventListener('input', handleTextInput);
            editor.addEventListener('keydown', handleKeydown);
            editor.addEventListener('mouseup', handleTextSelection);

            // Writing tools
            document.getElementById('writingMode').addEventListener('change', changeWritingMode);
            document.getElementById('checkGrammarBtn').addEventListener('click', checkGrammar);
            document.getElementById('aiRewriteBtn').addEventListener('click', showAiSuggestions);
            document.getElementById('exportBtn').addEventListener('click', exportDocument);
            document.getElementById('clearBtn').addEventListener('click', clearEditor);

            // File upload
            setupFileUpload();

            // Settings modal
            setupSettingsModal();

            // Help modal
            document.getElementById('closeHelp').addEventListener('click', closeHelp);

            // AI panel
            document.getElementById('closeAiPanel').addEventListener('click', closeAiPanel);

            // Tone adjustment
            document.getElementById('adjustToneBtn').addEventListener('click', adjustTone);

            // Keyboard shortcuts
            document.addEventListener('keydown', handleGlobalKeyboard);
        }

        function detectSystemLanguage() {
            const browserLang = navigator.language || navigator.userLanguage;
            const langCode = browserLang.split('-')[0];
            
            if (languages[langCode]) {
                state.currentLanguage = langCode;
                document.getElementById('languageSelector').value = langCode;
                updateLanguageUI();
            }
        }

        function changeLanguage(event) {
            const newLang = event.target.value;
            state.currentLanguage = newLang;
            updateLanguageUI();
            analyzeText();
            saveSettings();
        }

        function updateLanguageUI() {
            const lang = languages[state.currentLanguage];
            const editor = document.getElementById('textEditor');
            
            // Update RTL support
            if (lang.rtl) {
                editor.classList.add('rtl');
                document.getElementById('app').classList.add('rtl');
            } else {
                editor.classList.remove('rtl');
                document.getElementById('app').classList.remove('rtl');
            }
            
            // Update current language display
            document.getElementById('currentLang').textContent = lang.name;
            
            // Update status
            updateStatus(`Switched to ${lang.name}`);
        }

        function toggleTheme() {
            const themes = ['light', 'dark', 'auto'];
            const currentIndex = themes.indexOf(state.theme);
            state.theme = themes[(currentIndex + 1) % themes.length];
            updateTheme();
            saveSettings();
        }

        function updateTheme() {
            const html = document.documentElement;
            const themeToggle = document.getElementById('themeToggle');
            
            if (state.theme === 'auto') {
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                html.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
                themeToggle.innerHTML = '<i class="fas fa-adjust text-lg"></i>';
            } else {
                html.setAttribute('data-theme', state.theme);
                themeToggle.innerHTML = state.theme === 'dark' ? 
                    '<i class="fas fa-sun text-lg"></i>' : 
                    '<i class="fas fa-moon text-lg"></i>';
            }
        }

        function updateFontSize() {
            const editor = document.getElementById('textEditor');
            editor.className = editor.className.replace(/font-(small|normal|large)/, '');
            editor.classList.add(`font-${state.fontSize}`);
        }

        function setupEditor() {
            const editor = document.getElementById('textEditor');
            
            // Set placeholder behavior
            editor.addEventListener('focus', function() {
                if (this.textContent.trim() === '') {
                    this.innerHTML = '';
                }
            });
            
            editor.addEventListener('blur', function() {
                if (this.textContent.trim() === '') {
                    this.innerHTML = '<div class="text-gray-400">Start writing your text here... The grammar checker will provide real-time suggestions as you type.</div>';
                }
            });
        }

        function handleTextInput(event) {
            updateWritingStats();
            
            if (state.settings.realTimeCheck) {
                clearTimeout(window.checkTimeout);
                window.checkTimeout = setTimeout(analyzeText, 500);
            }
            
            updateLastSaved();
        }

        function handleKeydown(event) {
            // Handle tab key for indentation
            if (event.key === 'Tab') {
                event.preventDefault();
                document.execCommand('insertText', false, '    ');
            }
        }

        function handleTextSelection() {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            
            if (selectedText && state.settings.aiOnSelection) {
                showSelectionSuggestions(selectedText);
            }
            
            updateSelectionInfo(selectedText);
        }

        function handleGlobalKeyboard(event) {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case 'g':
                        event.preventDefault();
                        checkGrammar();
                        break;
                    case ',':
                        event.preventDefault();
                        openSettings();
                        break;
                    case 'e':
                        event.preventDefault();
                        exportDocument();
                        break;
                }
                
                if (event.shiftKey && event.key === 'A') {
                    event.preventDefault();
                    showAiSuggestions();
                }
            }
        }

        function analyzeText() {
            const editor = document.getElementById('textEditor');
            const text = editor.textContent;
            
            if (!text.trim()) {
                clearSuggestions();
                return;
            }
            
            updateStatus('Analyzing...');
            
            // Simulate analysis delay
            setTimeout(() => {
                const errors = findErrors(text);
                highlightErrors(editor, errors);
                updateSuggestions(errors);
                updateWritingStats();
                updateToneAnalysis(text);
                updateStatus('Analysis complete');
            }, 300);
        }

        function findErrors(text) {
            const errors = [];
            const rules = grammarRules[state.currentLanguage] || [];
            
            // Simulate finding errors
            const words = text.split(/\s+/);
            
            words.forEach((word, index) => {
                // Simulate various error types
                if (Math.random() < 0.1 && word.length > 3) {
                    errors.push({
                        type: 'spelling',
                        word: word,
                        position: index,
                        suggestions: [word + 's', word.slice(0, -1), word + 'ed'],
                        explanation: `"${word}" may be misspelled.`
                    });
                }
                
                if (Math.random() < 0.05) {
                    errors.push({
                        type: 'grammar',
                        word: word,
                        position: index,
                        suggestions: ['Consider rephrasing this sentence'],
                        explanation: 'Grammar issue detected'
                    });
                }
                
                if (Math.random() < 0.03) {
                    errors.push({
                        type: 'style',
                        word: word,
                        position: index,
                        suggestions: ['More concise alternative', 'Formal alternative'],
                        explanation: 'Style improvement suggested'
                    });
                }
            });
            
            return errors;
        }

        function highlightErrors(editor, errors) {
            let html = editor.innerHTML;
            
            // Remove existing highlights
            html = html.replace(/<span class="[^"]*error[^"]*"[^>]*>(.*?)<\/span>/g, '$1');
            
            errors.forEach(error => {
                const className = `${error.type}-error`;
                html = html.replace(
                    new RegExp(`\\b${error.word}\\b`, 'g'),
                    `<span class="${className}" data-error='${JSON.stringify(error)}'>${error.word}</span>`
                );
            });
            
            editor.innerHTML = html;
            
            // Add click handlers for error highlights
            editor.querySelectorAll('[data-error]').forEach(span => {
                span.addEventListener('click', function() {
                    const error = JSON.parse(this.dataset.error);
                    showErrorSuggestion(error, this);
                });
            });
        }

        function updateSuggestions(errors) {
            const suggestionsList = document.getElementById('suggestionsList');
            
            if (errors.length === 0) {
                suggestionsList.innerHTML = `
                    <div class="text-sm text-green-600 text-center py-8">
                        <i class="fas fa-check-circle text-2xl mb-2"></i>
                        <p>No issues found!</p>
                    </div>
                `;
                return;
            }
            
            const suggestionsHTML = errors.slice(0, 5).map(error => `
                <div class="suggestion-card border border-gray-200 rounded p-3 hover:shadow-md transition-shadow cursor-pointer">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center mb-1">
                                <span class="inline-block w-2 h-2 rounded-full ${getErrorColor(error.type)} mr-2"></span>
                                <span class="text-sm font-medium capitalize">${error.type}</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">${error.explanation}</p>
                            <div class="text-xs text-gray-500">
                                Suggestions: ${error.suggestions.slice(0, 2).join(', ')}
                            </div>
                        </div>
                        <button class="text-emerald-600 hover:text-emerald-700 text-sm font-medium ml-2">
                            Fix
                        </button>
                    </div>
                </div>
            `).join('');
            
            suggestionsList.innerHTML = suggestionsHTML;
            
            // Update error count
            state.writingStats.errors = errors.length;
            document.getElementById('errorCount').textContent = errors.length;
        }

        function getErrorColor(type) {
            const colors = {
                spelling: 'bg-red-500',
                grammar: 'bg-blue-500',
                style: 'bg-green-500',
                clarity: 'bg-yellow-500'
            };
            return colors[type] || 'bg-gray-500';
        }

        function updateWritingStats() {
            const editor = document.getElementById('textEditor');
            const text = editor.textContent;
            
            const words = text.trim() ? text.trim().split(/\s+/).length : 0;
            const characters = text.length;
            
            state.writingStats.words = words;
            state.writingStats.characters = characters;
            
            // Calculate quality score
            const errorRatio = state.writingStats.errors / Math.max(words, 1);
            const score = Math.max(0, Math.round((1 - errorRatio) * 100));
            state.writingStats.score = score;
            
            // Update UI
            document.getElementById('wordCount').textContent = words;
            document.getElementById('charCount').textContent = characters;
            document.getElementById('qualityScore').textContent = `${score}%`;
            
            // Update quality bar
            const qualityBar = document.getElementById('qualityBar');
            qualityBar.style.width = `${score}%`;
        }

        function updateToneAnalysis(text) {
            // Simulate tone analysis
            const tones = ['Professional', 'Casual', 'Friendly', 'Formal', 'Academic'];
            const emotions = ['Neutral', 'Positive', 'Confident', 'Analytical'];
            
            // Simple analysis based on text characteristics
            let formality = 'Professional';
            let emotion = 'Neutral';
            let confidence = 95;
            
            if (text.includes('!') || text.includes('awesome') || text.includes('great')) {
                emotion = 'Positive';
                formality = 'Casual';
            }
            
            if (text.includes('however') || text.includes('furthermore') || text.includes('therefore')) {
                formality = 'Academic';
                emotion = 'Analytical';
            }
            
            // Update tone display (implement tone display elements)
            // This would update actual tone analysis UI elements
        }

        function showSelectionSuggestions(selectedText) {
            if (!state.settings.aiOnSelection) return;
            
            const aiPanel = document.getElementById('aiPanel');
            const suggestions = document.getElementById('aiSuggestions');
            
            suggestions.innerHTML = `
                <div class="bg-white bg-opacity-20 rounded p-3">
                    <p class="text-sm mb-2">💡 AI suggestions for: "${selectedText}"</p>
                    <div class="space-y-2">
                        <button class="w-full text-left bg-white bg-opacity-10 rounded p-2 text-sm hover:bg-opacity-20 transition-colors">
                            Make it more formal
                        </button>
                        <button class="w-full text-left bg-white bg-opacity-10 rounded p-2 text-sm hover:bg-opacity-20 transition-colors">
                            Improve clarity
                        </button>
                        <button class="w-full text-left bg-white bg-opacity-10 rounded p-2 text-sm hover:bg-opacity-20 transition-colors">
                            Rewrite for brevity
                        </button>
                    </div>
                </div>
            `;
            
            aiPanel.classList.remove('hidden');
        }

        function updateSelectionInfo(selectedText) {
            const selectionInfo = document.getElementById('selectionInfo');
            if (selectedText) {
                const wordCount = selectedText.split(/\s+/).length;
                selectionInfo.textContent = `${wordCount} word(s) selected`;
            } else {
                selectionInfo.textContent = '';
            }
        }

        function updateStatus(message) {
            document.getElementById('statusText').textContent = message;
            setTimeout(() => {
                document.getElementById('statusText').textContent = 'Ready';
            }, 3000);
        }

        function updateLastSaved() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('lastSaved').textContent = timeString;
        }

        function checkGrammar() {
            updateStatus('Checking grammar...');
            analyzeText();
        }

        function showAiSuggestions() {
            const aiPanel = document.getElementById('aiPanel');
            aiPanel.classList.remove('hidden');
        }

        function closeAiPanel() {
            document.getElementById('aiPanel').classList.add('hidden');
        }

        function adjustTone() {
            // Implement tone adjustment functionality
            updateStatus('Adjusting tone...');
            
            setTimeout(() => {
                updateStatus('Tone adjusted');
            }, 1000);
        }

        function exportDocument() {
            const editor = document.getElementById('textEditor');
            const text = editor.textContent;
            
            const blob = new Blob([text], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'document.txt';
            a.click();
            URL.revokeObjectURL(url);
            
            updateStatus('Document exported');
        }

        function clearEditor() {
            if (confirm('Are you sure you want to clear the editor?')) {
                document.getElementById('textEditor').innerHTML = '';
                clearSuggestions();
                updateWritingStats();
                updateStatus('Editor cleared');
            }
        }

        function clearSuggestions() {
            document.getElementById('suggestionsList').innerHTML = `
                <div class="text-sm text-gray-500 text-center py-8">
                    <i class="fas fa-search text-2xl mb-2 text-gray-300"></i>
                    <p>Start typing to see suggestions</p>
                </div>
            `;
        }

        function changeWritingMode(event) {
            const mode = event.target.value;
            updateStatus(`Switched to ${mode} mode`);
            analyzeText(); // Re-analyze with new mode
        }

        function setupFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const dropZone = document.getElementById('fileDropZone');
            
            dropZone.addEventListener('click', () => fileInput.click());
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('border-emerald-400', 'bg-emerald-50');
            });
            
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('border-emerald-400', 'bg-emerald-50');
            });
            
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('border-emerald-400', 'bg-emerald-50');
                handleFiles(e.dataTransfer.files);
            });
            
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
        }

        function handleFiles(files) {
            const file = files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const text = e.target.result;
                document.getElementById('textEditor').textContent = text;
                analyzeText();
                updateStatus(`Loaded ${file.name}`);
            };
            reader.readAsText(file);
        }

        function setupSettingsModal() {
            const modal = document.getElementById('settingsModal');
            const closeBtn = document.getElementById('closeSettings');
            const cancelBtn = document.getElementById('cancelSettings');
            const saveBtn = document.getElementById('saveSettings');
            
            closeBtn.addEventListener('click', closeSettings);
            cancelBtn.addEventListener('click', closeSettings);
            saveBtn.addEventListener('click', saveAllSettings);
            
            // Settings navigation
            document.querySelectorAll('.settings-nav-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    const section = e.target.dataset.section;
                    switchSettingsSection(section);
                    
                    // Update active nav item
                    document.querySelectorAll('.settings-nav-item').forEach(nav => nav.classList.remove('active'));
                    e.target.classList.add('active');
                });
            });
            
            // Add word to dictionary
            document.getElementById('addWordBtn').addEventListener('click', addWordToDictionary);
            document.getElementById('newWord').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') addWordToDictionary();
            });
            
            // Theme selection
            document.querySelectorAll('input[name="theme"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    state.theme = e.target.value;
                    updateTheme();
                });
            });
            
            // Font size selection
            document.querySelectorAll('input[name="fontSize"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    state.fontSize = e.target.value;
                    updateFontSize();
                });
            });
        }

        function openSettings() {
            document.getElementById('settingsModal').classList.remove('hidden');
            loadSettingsUI();
            
            // Set first nav item as active
            document.querySelector('.settings-nav-item').classList.add('active');
            switchSettingsSection('language');
        }

        function closeSettings() {
            document.getElementById('settingsModal').classList.add('hidden');
        }

        function switchSettingsSection(section) {
            // Hide all sections
            const sections = ['language', 'features', 'dictionary', 'appearance', 'advanced'];
            sections.forEach(s => {
                const element = document.getElementById(`${s}Settings`);
                if (element) {
                    element.style.display = s === section ? 'block' : 'none';
                }
            });
        }

        function loadSettingsUI() {
            // Load current settings into UI
            document.getElementById('primaryLanguage').value = state.currentLanguage;
            
            // Load toggles
            Object.keys(state.settings).forEach(key => {
                const toggle = document.getElementById(key);
                if (toggle) {
                    toggle.checked = state.settings[key];
                }
            });
            
            // Load theme
            document.querySelector(`input[name="theme"][value="${state.theme}"]`).checked = true;
            
            // Load font size
            document.querySelector(`input[name="fontSize"][value="${state.fontSize}"]`).checked = true;
        }

        function saveAllSettings() {
            // Save all settings from UI
            state.currentLanguage = document.getElementById('primaryLanguage').value;
            
            // Save toggles
            Object.keys(state.settings).forEach(key => {
                const toggle = document.getElementById(key);
                if (toggle) {
                    state.settings[key] = toggle.checked;
                }
            });
            
            // Save theme
            const selectedTheme = document.querySelector('input[name="theme"]:checked');
            if (selectedTheme) {
                state.theme = selectedTheme.value;
            }
            
            // Save font size
            const selectedFontSize = document.querySelector('input[name="fontSize"]:checked');
            if (selectedFontSize) {
                state.fontSize = selectedFontSize.value;
            }
            
            saveSettings();
            updateLanguageUI();
            closeSettings();
            updateStatus('Settings saved');
        }

        function addWordToDictionary() {
            const input = document.getElementById('newWord');
            const word = input.value.trim();
            
            if (word && !state.personalDictionary.includes(word)) {
                state.personalDictionary.push(word);
                input.value = '';
                updateDictionaryList();
                saveSettings();
                updateStatus(`Added "${word}" to dictionary`);
            }
        }

        function updateDictionaryList() {
            const list = document.getElementById('dictionaryList');
            const html = state.personalDictionary.map(word => `
                <div class="flex items-center justify-between text-sm">
                    <span>${word}</span>
                    <button class="text-red-500 hover:text-red-700 text-xs" onclick="removeFromDictionary('${word}')">Remove</button>
                </div>
            `).join('');
            list.innerHTML = `<div class="space-y-1">${html}</div>`;
        }

        function removeFromDictionary(word) {
            const index = state.personalDictionary.indexOf(word);
            if (index > -1) {
                state.personalDictionary.splice(index, 1);
                updateDictionaryList();
                saveSettings();
                updateStatus(`Removed "${word}" from dictionary`);
            }
        }

        function openHelp() {
            document.getElementById('helpModal').classList.remove('hidden');
        }

        function closeHelp() {
            document.getElementById('helpModal').classList.add('hidden');
        }

        function saveSettings() {
            localStorage.setItem('grammarCheckerSettings', JSON.stringify(state));
        }

        function loadSettings() {
            const saved = localStorage.getItem('grammarCheckerSettings');
            if (saved) {
                const savedState = JSON.parse(saved);
                Object.assign(state, savedState);
            }
        }

        function getSampleText(language) {
            const samples = {
                en: "Welcome to the advanced multilingual grammar checker! This powerful tool helps you write with confidence in multiple languages. It provides real-time suggestions for grammar, spelling, style, and tone improvements.",
                tr: "Gelişmiş çok dilli dilbilgisi denetleyicisine hoş geldiniz! Bu güçlü araç, birden fazla dilde güvenle yazmanıza yardımcı olur. Dilbilgisi, yazım, stil ve ton iyileştirmeleri için gerçek zamanlı öneriler sağlar.",
                ar: "مرحباً بك في مدقق القواعد متعدد اللغات المتقدم! تساعدك هذه الأداة القوية على الكتابة بثقة بلغات متعددة. توفر اقتراحات فورية لتحسين القواعد والإملاء والأسلوب والنبرة.",
                es: "¡Bienvenido al corrector gramatical multilingüe avanzado! Esta poderosa herramienta te ayuda a escribir con confianza en múltiples idiomas. Proporciona sugerencias en tiempo real para mejoras de gramática, ortografía, estilo y tono.",
                de: "Willkommen beim fortgeschrittenen mehrsprachigen Grammatikprüfer! Dieses mächtige Tool hilft Ihnen, selbstbewusst in mehreren Sprachen zu schreiben. Es bietet Echtzeitvorschläge für Grammatik-, Rechtschreib-, Stil- und Tonverbesserungen.",
                fr: "Bienvenue dans le vérificateur grammatical multilingue avancé ! Cet outil puissant vous aide à écrire avec confiance dans plusieurs langues. Il fournit des suggestions en temps réel pour les améliorations de grammaire, d'orthographe, de style et de ton.",
                nl: "Welkom bij de geavanceerde meertalige grammaticacontrole! Deze krachtige tool helpt je met vertrouwen te schrijven in meerdere talen. Het biedt real-time suggesties voor grammatica-, spelling-, stijl- en toonverbeteringen.",
                it: "Benvenuto nel correttore grammaticale multilingue avanzato! Questo potente strumento ti aiuta a scrivere con fiducia in più lingue. Fornisce suggerimenti in tempo reale per miglioramenti di grammatica, ortografia, stile e tono."
            };
            
            return samples[language] || samples.en;
        }

        // Make removeFromDictionary globally accessible
        window.removeFromDictionary = removeFromDictionary;
    </script>
</body>
</html>
