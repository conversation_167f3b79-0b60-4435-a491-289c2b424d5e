import { useState } from 'react';
import { useLanguageContext } from './LanguageProvider';
import { useDictionary, DictionaryDefinition } from '../lib/features/dictionary/useDictionary';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';

export function DictionaryModal() {
  const { t } = useLanguageContext();
  const { lookupWord, lookupLoading } = useDictionary();
  
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [definition, setDefinition] = useState<DictionaryDefinition | null>(null);
  const [error, setError] = useState('');
  
  const handleSearch = async () => {
    if (!searchTerm.trim()) return;
    
    try {
      const result = await lookupWord(searchTerm.trim());
      setDefinition(result);
      setError('');
    } catch (err) {
      setDefinition(null);
      setError(t('definitionNotFound'));
    }
  };
  
  // This component needs to be triggered to open. We'll add a temporary button for testing.
  // In the final app, the Editor component will trigger this on double-click.
  return (
    <>
      {/* Temporary button to open modal */}
      <Button onClick={() => setIsOpen(true)} className="fixed bottom-4 right-4 z-50">Open Dictionary</Button>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t('dictionarySettingsTitle')}</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="flex space-x-2">
              <Input
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                placeholder={t('enterWordToLookup')}
                onKeyDown={(e: React.KeyboardEvent) => e.key === 'Enter' && handleSearch()}
              />
              <Button onClick={handleSearch} disabled={lookupLoading}>
                {lookupLoading ? t('lookingUpDefinition') : t('search')}
              </Button>
            </div>
            
            {error && (
              <div className="text-center py-4">
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}
            
            {definition && !definition.error && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">{definition.word}</h3>
                  {definition.phonetic && (
                    <span className="text-muted-foreground">{definition.phonetic}</span>
                  )}
                </div>
                
                {definition.meanings.map((meaning, index) => (
                  <div key={index} className="space-y-2">
                    <div className="text-sm font-medium text-primary">{meaning.partOfSpeech}</div>
                    <ol className="list-decimal list-inside space-y-1">
                      {meaning.definitions.map((def, defIndex) => (
                        <li key={defIndex} className="text-sm">
                          {def.definition}
                          {def.example && (
                            <div className="text-xs text-muted-foreground ml-5 mt-1">"{def.example}"</div>
                          )}
                        </li>
                      ))}
                    </ol>
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
