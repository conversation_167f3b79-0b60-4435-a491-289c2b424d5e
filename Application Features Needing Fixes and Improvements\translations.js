/**
 * Translation system for GrammarPro application
 * Contains UI text translations for all supported languages
 */

const translations = {
  'en': {
    // Header
    'appName': 'GrammarPro',
    'currentLanguage': 'Current Language',
    'settings': 'Settings',
    'help': 'Help',
    
    // Writing Tools
    'writingTools': 'Writing Tools',
    'writingMode': 'Writing Mode',
    'casual': '📝 Casual',
    'formal': '🏢 Formal',
    'academic': '🎓 Academic',
    'creative': '🎨 Creative',
    'business': '💼 Business',
    
    // Import Document
    'importDocument': 'Import Document',
    'dropFilesHere': 'Drop files here or',
    'browse': 'browse',
    
    // Quick Actions
    'quickActions': 'Quick Actions',
    'checkAll': 'Check All',
    'aiRewrite': 'AI Rewrite',
    'export': 'Export',
    'clear': 'Clear',
    
    // Writing Statistics
    'writingStatistics': 'Writing Statistics',
    'words': 'Words',
    'characters': 'Characters',
    'errors': 'Errors',
    'score': 'Score',
    
    // Editor
    'documentEditor': 'Document Editor',
    'ready': 'Ready',
    'undo': 'Undo',
    'redo': 'Redo',
    'editorPlaceholder': 'Start writing your text here... The grammar checker will provide real-time suggestions as you type.',
    
    // Editor Footer
    'lastSaved': 'Last saved',
    'never': 'Never',
    'language': 'Language',
    'line': 'Line',
    
    // AI Assistant
    'aiWritingAssistant': 'AI Writing Assistant',
    'trySuggestion': 'Try rewriting this sentence for better clarity:',
    'applySuggestion': 'Apply Suggestion',
    
    // Suggestions
    'suggestions': 'Suggestions',
    'startTyping': 'Start typing to see suggestions',
    'noIssues': 'No issues found!',
    
    // Tone Analysis
    'toneAnalysis': 'Tone Analysis',
    'confidence': 'Confidence',
    'formality': 'Formality',
    'emotion': 'Emotion',
    'adjustTone': 'Adjust Tone',
    
    // Recent Prompts
    'recentAiPrompts': 'Recent AI Prompts',
    
    // Settings Modal
    'settingsTitle': 'Settings',
    'cancel': 'Cancel',
    'saveChanges': 'Save Changes',
    
    // Settings Navigation
    'languageSettings': 'Language',
    'featuresSettings': 'Features',
    'dictionarySettings': 'Dictionary',
    'appearanceSettings': 'Appearance',
    'advancedSettings': 'Advanced',
    
    // Language Settings
    'languageSettingsTitle': 'Language Settings',
    'primaryLanguage': 'Primary Language',
    'autoDetect': '🌐 Auto-detect (System Default)',
    'dialectRegion': 'Dialect/Region',
    'americanEnglish': '🇺🇸 American English',
    'britishEnglish': '🇬🇧 British English',
    'australianEnglish': '🇦🇺 Australian English',
    'languageProficiency': 'Language Proficiency',
    'nativeSpeaker': 'Native Speaker',
    'advanced': 'Advanced',
    'intermediate': 'Intermediate',
    'beginner': 'Beginner',
    'rtlSupport': 'RTL Support (Right-to-Left)',
    'rtlSupportDesc': 'Automatically enable for Arabic and other RTL languages',
    
    // Feature Settings
    'featureCustomization': 'Feature Customization',
    'generativeAi': 'Generative AI',
    'showAiOnSelection': 'Show AI on text selection',
    'showAiOnSelectionDesc': 'Display AI suggestions when text is selected',
    'quickAiReplies': 'Quick AI replies',
    'quickAiRepliesDesc': 'Show quick response options for common tasks',
    'savePromptHistory': 'Save prompt history',
    'savePromptHistoryDesc': 'Remember your recent AI prompts',
    
    // Error Highlighting
    'errorHighlighting': 'Error Highlighting',
    'grammarErrors': 'Grammar errors',
    'spellingErrors': 'Spelling errors',
    'styleSuggestions': 'Style suggestions',
    'clarityImprovements': 'Clarity improvements',
    'highContrastMode': 'High contrast mode',
    'highContrastModeDesc': 'Improves accessibility for visually impaired users',
    
    // Advanced Settings
    'advancedSettingsTitle': 'Advanced Settings',
    'performance': 'Performance',
    'realTimeChecking': 'Real-time checking',
    'realTimeCheckingDesc': 'Check grammar as you type',
    'checkDelay': 'Check delay (ms)',
    'fast': 'Fast (100ms)',
    'slow': 'Slow (2000ms)',
    
    // Privacy & Data
    'privacyAndData': 'Privacy & Data',
    'saveWritingHistory': 'Save writing history',
    'saveWritingHistoryDesc': 'Store documents locally for quick access',
    'analytics': 'Analytics',
    'analyticsDesc': 'Help improve the service with usage data',
    
    // Keyboard Shortcuts
    'keyboardShortcuts': 'Keyboard Shortcuts',
    'checkGrammar': 'Check grammar',
    'aiAssistant': 'AI assistant',
    'openSettings': 'Settings',
    'exportDocument': 'Export document',
    
    // Reset & Backup
    'resetAndBackup': 'Reset & Backup',
    'exportSettings': 'Export Settings',
    'importSettings': 'Import Settings',
    'resetToDefaults': 'Reset to Defaults',
    
    // Dictionary
    'dictionarySettingsTitle': 'Dictionary Settings',
    'personalDictionary': 'Personal Dictionary',
    'addWordToDictionary': 'Add word to dictionary',
    'addWord': 'Add Word',
    'wordList': 'Word List',
    'noWordsYet': 'No words in personal dictionary yet',
    'remove': 'Remove',
    
    // Appearance
    'appearanceSettingsTitle': 'Appearance Settings',
    'theme': 'Theme',
    'lightTheme': 'Light',
    'darkTheme': 'Dark',
    'autoTheme': 'Auto (follow system)',
    'fontSize': 'Font Size',
    'small': 'Small',
    'normal': 'Normal',
    'large': 'Large',
    
    // Help Modal
    'helpAndTutorial': 'Help & Tutorial',
    'gettingStarted': 'Getting Started',
    'errorTypes': 'Error Types',
    
    // Status Messages
    'analyzing': 'Analyzing...',
    'analysisComplete': 'Analysis complete',
    'checkingGrammar': 'Checking grammar...',
    'toneAdjusted': 'Tone adjusted',
    'documentExported': 'Document exported',
    'editorCleared': 'Editor cleared',
    'settingsSaved': 'Settings saved',
    'wordAddedToDictionary': 'Word added to dictionary',
    'wordRemovedFromDictionary': 'Word removed from dictionary',
    
    // Error Types
    'grammarAndSpellingErrors': 'Grammar and spelling errors',
    'styleAndFluencySuggestions': 'Style and fluency suggestions',
    'clarityImprovementsSuggestions': 'Clarity improvements',
    
    // Dictionary Lookup
    'lookingUpDefinition': 'Looking up definition...',
    'definitionNotFound': 'Definition not found',
    'close': 'Close'
  },
  
  'tr': {
    // Header
    'appName': 'GrammarPro',
    'currentLanguage': 'Geçerli Dil',
    'settings': 'Ayarlar',
    'help': 'Yardım',
    
    // Writing Tools
    'writingTools': 'Yazım Araçları',
    'writingMode': 'Yazım Modu',
    'casual': '📝 Gündelik',
    'formal': '🏢 Resmi',
    'academic': '🎓 Akademik',
    'creative': '🎨 Yaratıcı',
    'business': '💼 İş',
    
    // Import Document
    'importDocument': 'Belge İçe Aktar',
    'dropFilesHere': 'Dosyaları buraya sürükleyin veya',
    'browse': 'göz atın',
    
    // Quick Actions
    'quickActions': 'Hızlı İşlemler',
    'checkAll': 'Tümünü Kontrol Et',
    'aiRewrite': 'AI Yeniden Yazım',
    'export': 'Dışa Aktar',
    'clear': 'Temizle',
    
    // Writing Statistics
    'writingStatistics': 'Yazım İstatistikleri',
    'words': 'Kelimeler',
    'characters': 'Karakterler',
    'errors': 'Hatalar',
    'score': 'Puan',
    
    // Editor
    'documentEditor': 'Belge Düzenleyici',
    'ready': 'Hazır',
    'undo': 'Geri Al',
    'redo': 'Yinele',
    'editorPlaceholder': 'Metninizi buraya yazmaya başlayın... Dilbilgisi denetleyicisi yazarken gerçek zamanlı öneriler sunacaktır.',
    
    // Editor Footer
    'lastSaved': 'Son kaydedilen',
    'never': 'Hiç',
    'language': 'Dil',
    'line': 'Satır',
    
    // AI Assistant
    'aiWritingAssistant': 'AI Yazım Asistanı',
    'trySuggestion': 'Bu cümleyi daha iyi netlik için yeniden yazmayı deneyin:',
    'applySuggestion': 'Öneriyi Uygula',
    
    // Suggestions
    'suggestions': 'Öneriler',
    'startTyping': 'Önerileri görmek için yazmaya başlayın',
    'noIssues': 'Sorun bulunamadı!',
    
    // Tone Analysis
    'toneAnalysis': 'Ton Analizi',
    'confidence': 'Güven',
    'formality': 'Resmiyet',
    'emotion': 'Duygu',
    'adjustTone': 'Tonu Ayarla',
    
    // Recent Prompts
    'recentAiPrompts': 'Son AI İstemleri',
    
    // Settings Modal
    'settingsTitle': 'Ayarlar',
    'cancel': 'İptal',
    'saveChanges': 'Değişiklikleri Kaydet',
    
    // Settings Navigation
    'languageSettings': 'Dil',
    'featuresSettings': 'Özellikler',
    'dictionarySettings': 'Sözlük',
    'appearanceSettings': 'Görünüm',
    'advancedSettings': 'Gelişmiş',
    
    // Language Settings
    'languageSettingsTitle': 'Dil Ayarları',
    'primaryLanguage': 'Ana Dil',
    'autoDetect': '🌐 Otomatik algıla (Sistem Varsayılanı)',
    'dialectRegion': 'Lehçe/Bölge',
    'americanEnglish': '🇺🇸 Amerikan İngilizcesi',
    'britishEnglish': '🇬🇧 İngiliz İngilizcesi',
    'australianEnglish': '🇦🇺 Avustralya İngilizcesi',
    'languageProficiency': 'Dil Yeterliliği',
    'nativeSpeaker': 'Ana Dil',
    'advanced': 'İleri',
    'intermediate': 'Orta',
    'beginner': 'Başlangıç',
    'rtlSupport': 'RTL Desteği (Sağdan Sola)',
    'rtlSupportDesc': 'Arapça ve diğer RTL dilleri için otomatik olarak etkinleştir',
    
    // Feature Settings
    'featureCustomization': 'Özellik Özelleştirme',
    'generativeAi': 'Üretken AI',
    'showAiOnSelection': 'Metin seçiminde AI göster',
    'showAiOnSelectionDesc': 'Metin seçildiğinde AI önerileri göster',
    'quickAiReplies': 'Hızlı AI yanıtları',
    'quickAiRepliesDesc': 'Yaygın görevler için hızlı yanıt seçenekleri göster',
    'savePromptHistory': 'İstem geçmişini kaydet',
    'savePromptHistoryDesc': 'Son AI istemlerinizi hatırla',
    
    // Error Highlighting
    'errorHighlighting': 'Hata Vurgulama',
    'grammarErrors': 'Dilbilgisi hataları',
    'spellingErrors': 'Yazım hataları',
    'styleSuggestions': 'Stil önerileri',
    'clarityImprovements': 'Netlik iyileştirmeleri',
    'highContrastMode': 'Yüksek kontrast modu',
    'highContrastModeDesc': 'Görme engelli kullanıcılar için erişilebilirliği artırır',
    
    // Advanced Settings
    'advancedSettingsTitle': 'Gelişmiş Ayarlar',
    'performance': 'Performans',
    'realTimeChecking': 'Gerçek zamanlı kontrol',
    'realTimeCheckingDesc': 'Yazarken dilbilgisini kontrol et',
    'checkDelay': 'Kontrol gecikmesi (ms)',
    'fast': 'Hızlı (100ms)',
    'slow': 'Yavaş (2000ms)',
    
    // Privacy & Data
    'privacyAndData': 'Gizlilik ve Veri',
    'saveWritingHistory': 'Yazım geçmişini kaydet',
    'saveWritingHistoryDesc': 'Belgeleri hızlı erişim için yerel olarak sakla',
    'analytics': 'Analitik',
    'analyticsDesc': 'Kullanım verileriyle hizmeti iyileştirmeye yardımcı ol',
    
    // Keyboard Shortcuts
    'keyboardShortcuts': 'Klavye Kısayolları',
    'checkGrammar': 'Dilbilgisini kontrol et',
    'aiAssistant': 'AI asistanı',
    'openSettings': 'Ayarlar',
    'exportDocument': 'Belgeyi dışa aktar',
    
    // Reset & Backup
    'resetAndBackup': 'Sıfırla ve Yedekle',
    'exportSettings': 'Ayarları Dışa Aktar',
    'importSettings': 'Ayarları İçe Aktar',
    'resetToDefaults': 'Varsayılanlara Sıfırla',
    
    // Dictionary
    'dictionarySettingsTitle': 'Sözlük Ayarları',
    'personalDictionary': 'Kişisel Sözlük',
    'addWordToDictionary': 'Sözlüğe kelime ekle',
    'addWord': 'Kelime Ekle',
    'wordList': 'Kelime Listesi',
    'noWordsYet': 'Henüz kişisel sözlükte kelime yok',
    'remove': 'Kaldır',
    
    // Appearance
    'appearanceSettingsTitle': 'Görünüm Ayarları',
    'theme': 'Tema',
    'lightTheme': 'Açık',
    'darkTheme': 'Koyu',
    'autoTheme': 'Otomatik (sistemi takip et)',
    'fontSize': 'Yazı Boyutu',
    'small': 'Küçük',
    'normal': 'Normal',
    'large': 'Büyük',
    
    // Help Modal
    'helpAndTutorial': 'Yardım ve Öğretici',
    'gettingStarted': 'Başlarken',
    'errorTypes': 'Hata Türleri',
    
    // Status Messages
    'analyzing': 'Analiz ediliyor...',
    'analysisComplete': 'Analiz tamamlandı',
    'checkingGrammar': 'Dilbilgisi kontrol ediliyor...',
    'toneAdjusted': 'Ton ayarlandı',
    'documentExported': 'Belge dışa aktarıldı',
    'editorCleared': 'Düzenleyici temizlendi',
    'settingsSaved': 'Ayarlar kaydedildi',
    'wordAddedToDictionary': 'Kelime sözlüğe eklendi',
    'wordRemovedFromDictionary': 'Kelime sözlükten kaldırıldı',
    
    // Error Types
    'grammarAndSpellingErrors': 'Dilbilgisi ve yazım hataları',
    'styleAndFluencySuggestions': 'Stil ve akıcılık önerileri',
    'clarityImprovementsSuggestions': 'Netlik iyileştirmeleri',
    
    // Dictionary Lookup
    'lookingUpDefinition': 'Tanım aranıyor...',
    'definitionNotFound': 'Tanım bulunamadı',
    'close': 'Kapat'
  },
  
  'ar': {
    // Header
    'appName': 'جرامر برو',
    'currentLanguage': 'اللغة الحالية',
    'settings': 'الإعدادات',
    'help': 'المساعدة',
    
    // Writing Tools
    'writingTools': 'أدوات الكتابة',
    'writingMode': 'وضع الكتابة',
    'casual': '📝 عادي',
    'formal': '🏢 رسمي',
    'academic': '🎓 أكاديمي',
    'creative': '🎨 إبداعي',
    'business': '💼 أعمال',
    
    // Import Document
    'importDocument': 'استيراد مستند',
    'dropFilesHere': 'أسقط الملفات هنا أو',
    'browse': 'تصفح',
    
    // Quick Actions
    'quickActions': 'إجراءات سريعة',
    'checkAll': 'تحقق من الكل',
    'aiRewrite': 'إعادة كتابة الذكاء الاصطناعي',
    'export': 'تصدير',
    'clear': 'مسح',
    
    // Writing Statistics
    'writingStatistics': 'إحصائيات الكتابة',
    'words': 'كلمات',
    'characters': 'أحرف',
    'errors': 'أخطاء',
    'score': 'النتيجة',
    
    // Editor
    'documentEditor': 'محرر المستندات',
    'ready': 'جاهز',
    'undo': 'تراجع',
    'redo': 'إعادة',
    'editorPlaceholder': 'ابدأ الكتابة هنا... سيقدم مدقق القواعد اقتراحات في الوقت الفعلي أثناء الكتابة.',
    
    // Editor Footer
    'lastSaved': 'آخر حفظ',
    'never': 'أبدًا',
    'language': 'اللغة',
    'line': 'سطر',
    
    // AI Assistant
    'aiWritingAssistant': 'مساعد الكتابة بالذكاء الاصطناعي',
    'trySuggestion': 'حاول إعادة كتابة هذه الجملة لوضوح أفضل:',
    'applySuggestion': 'تطبيق الاقتراح',
    
    // Suggestions
    'suggestions': 'اقتراحات',
    'startTyping': 'ابدأ الكتابة لرؤية الاقتراحات',
    'noIssues': 'لم يتم العثور على مشاكل!',
    
    // Tone Analysis
    'toneAnalysis': 'تحليل النبرة',
    'confidence': 'الثقة',
    'formality': 'الرسمية',
    'emotion': 'العاطفة',
    'adjustTone': 'ضبط النبرة',
    
    // Recent Prompts
    'recentAiPrompts': 'مطالبات الذكاء الاصطناعي الأخيرة',
    
    // Settings Modal
    'settingsTitle': 'الإعدادات',
    'cancel': 'إلغاء',
    'saveChanges': 'حفظ التغييرات',
    
    // Settings Navigation
    'languageSettings': 'اللغة',
    'featuresSettings': 'الميزات',
    'dictionarySettings': 'القاموس',
    'appearanceSettings': 'المظهر',
    'advancedSettings': 'متقدم',
    
    // Language Settings
    'languageSettingsTitle': 'إعدادات اللغة',
    'primaryLanguage': 'اللغة الأساسية',
    'autoDetect': '🌐 كشف تلقائي (افتراضي النظام)',
    'dialectRegion': 'اللهجة/المنطقة',
    'americanEnglish': '🇺🇸 الإنجليزية الأمريكية',
    'britishEnglish': '🇬🇧 الإنجليزية البريطانية',
    'australianEnglish': '🇦🇺 الإنجليزية الأسترالية',
    'languageProficiency': 'إتقان اللغة',
    'nativeSpeaker': 'متحدث أصلي',
    'advanced': 'متقدم',
    'intermediate': 'متوسط',
    'beginner': 'مبتدئ',
    'rtlSupport': 'دعم RTL (من اليمين إلى اليسار)',
    'rtlSupportDesc': 'تمكين تلقائي للغة العربية واللغات RTL الأخرى',
    
    // Feature Settings
    'featureCustomization': 'تخصيص الميزات',
    'generativeAi': 'الذكاء الاصطناعي التوليدي',
    'showAiOnSelection': 'إظهار الذكاء الاصطناعي عند تحديد النص',
    'showAiOnSelectionDesc': 'عرض اقتراحات الذكاء الاصطناعي عند تحديد النص',
    'quickAiReplies': 'ردود الذكاء الاصطناعي السريعة',
    'quickAiRepliesDesc': 'عرض خيارات الرد السريع للمهام الشائعة',
    'savePromptHistory': 'حفظ
(Content truncated due to size limit. Use line ranges to read in chunks)