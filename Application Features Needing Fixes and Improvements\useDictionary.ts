import { useCallback, useState } from 'react';

export interface DictionaryDefinition {
  word: string;
  phonetic?: string;
  meanings: {
    partOfSpeech: string;
    definitions: {
      definition: string;
      example?: string;
    }[];
  }[];
  error?: string;
}

/**
 * Hook to manage dictionary functionality
 * @returns Object with dictionary state and functions
 */
export const useDictionary = () => {
  const [personalDictionary, setPersonalDictionary] = useState<string[]>(['grammarpro', 'multilingual']);
  const [lookupLoading, setLookupLoading] = useState(false);
  
  /**
   * Look up a word in the dictionary
   */
  const lookupWord = useCallback(async (word: string): Promise<DictionaryDefinition> => {
    setLookupLoading(true);
    
    try {
      // This would be replaced with an actual API call in production
      // For now, simulate API call with timeout
      return new Promise((resolve) => {
        setTimeout(() => {
          const definition = getMockDefinition(word);
          setLookupLoading(false);
          resolve(definition);
        }, 500);
      });
    } catch (error) {
      setLookupLoading(false);
      console.error('Dictionary lookup failed:', error);
      return { 
        word,
        error: 'Definition not found',
        meanings: [] 
      };
    }
  }, []);
  
  /**
   * Add a word to the personal dictionary
   */
  const addToDictionary = useCallback((word: string) => {
    setPersonalDictionary(prev => [...prev, word.toLowerCase()]);
  }, []);
  
  /**
   * Remove a word from the personal dictionary
   */
  const removeFromDictionary = useCallback((word: string) => {
    setPersonalDictionary(prev => prev.filter(w => w !== word));
  }, []);
  
  /**
   * Check if a word is in the personal dictionary
   */
  const isInPersonalDictionary = useCallback((word: string): boolean => {
    return personalDictionary.includes(word.toLowerCase());
  }, [personalDictionary]);
  
  return {
    personalDictionary,
    addToDictionary,
    removeFromDictionary,
    lookupWord,
    lookupLoading,
    isInPersonalDictionary,
    
    // Helper for translating dictionary-related text
    getDictionaryTranslation: (key: string) => key // Simplified for now
  };
};

/**
 * Get mock definition for testing
 */
function getMockDefinition(word: string): DictionaryDefinition {
  // Simple mock definitions for testing
  const mockDefinitions: Record<string, DictionaryDefinition> = {
    'grammar': {
      word: 'grammar',
      phonetic: '/ˈɡramər/',
      meanings: [
        {
          partOfSpeech: 'noun',
          definitions: [
            {
              definition: 'The whole system and structure of a language or of languages in general, usually taken as consisting of syntax and morphology.',
              example: 'Teachers of English focus too much on grammar'
            },
            {
              definition: 'A book on grammar.',
              example: 'My old Latin grammar'
            }
          ]
        }
      ]
    },
    'spelling': {
      word: 'spelling',
      phonetic: '/ˈspelɪŋ/',
      meanings: [
        {
          partOfSpeech: 'noun',
          definitions: [
            {
              definition: 'The process or activity of writing or naming the letters of a word.',
              example: 'The spelling of his name was incorrect'
            },
            {
              definition: 'The way a word is spelled.',
              example: 'American spelling differs from British spelling'
            }
          ]
        }
      ]
    }
  };
  
  // Return mock definition or error
  return mockDefinitions[word.toLowerCase()] || { 
    word,
    error: 'Definition not found',
    meanings: [] 
  };
}
