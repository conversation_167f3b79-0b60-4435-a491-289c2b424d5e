export type Language = 'en' | 'es' | 'fr' | 'de' | 'tr' | 'ar' | 'zh' | 'ja' | 'ru' | 'it';

export interface LanguageInfo {
  name: string;
  flag: string;
  rtl: boolean;
}

// Define available languages
const languages: Record<Language, LanguageInfo> = {
  en: { name: 'English', flag: '🇺🇸', rtl: false },
  es: { name: 'Español', flag: '🇪🇸', rtl: false },
  fr: { name: 'Français', flag: '🇫🇷', rtl: false },
  de: { name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪', rtl: false },
  tr: { name: 'Türkçe', flag: '🇹🇷', rtl: false },
  ar: { name: 'العربية', flag: '🇸🇦', rtl: true },
  zh: { name: '中文', flag: '🇨🇳', rtl: false },
  ja: { name: '日本語', flag: '🇯🇵', rtl: false },
  ru: { name: 'Русский', flag: '🇷🇺', rtl: false },
  it: { name: 'Italiano', flag: '🇮🇹', rtl: false }
};

// Define translation strings
const strings: Record<Language, Record<string, string>> = {
  en: {
    appName: 'GrammarPro',
    currentLanguage: 'Language',
    lightTheme: 'Light Theme',
    darkTheme: 'Dark Theme',
    autoTheme: 'Auto Theme',
    settings: 'Settings',
    help: 'Help',
    writingTools: 'Writing Tools',
    writingMode: 'Writing Mode',
    casual: 'Casual',
    formal: 'Formal',
    academic: 'Academic',
    creative: 'Creative',
    business: 'Business',
    importDocument: 'Import Document',
    dropFilesHere: 'Drop files here or',
    browse: 'browse',
    quickActions: 'Quick Actions',
    checkAll: 'Check All',
    aiRewrite: 'AI Rewrite',
    export: 'Export',
    clear: 'Clear',
    writingStatistics: 'Writing Statistics',
    words: 'Words',
    characters: 'Characters',
    errors: 'Errors',
    score: 'Score',
    suggestions: 'Suggestions',
    analyzing: 'Analyzing your text...',
    noIssues: 'No issues found. Great job!',
    recentAiPrompts: 'Recent AI Prompts',
    settingsTitle: 'Settings',
    languageSettings: 'Language',
    featuresSettings: 'Features',
    dictionarySettings: 'Dictionary',
    appearanceSettings: 'Appearance',
    advancedSettings: 'Advanced',
    languageSettingsTitle: 'Language Settings',
    primaryLanguage: 'Primary Language',
    selectLanguage: 'Select language',
    rtlSupport: 'Right-to-left support',
    rtlSupportDesc: 'Enable support for right-to-left languages like Arabic and Hebrew',
    featureCustomization: 'Feature Customization',
    generativeAi: 'Generative AI',
    showAiOnSelection: 'Show AI suggestions on text selection',
    showAiOnSelectionDesc: 'Automatically show AI suggestions when you select text',
    errorHighlighting: 'Error Highlighting',
    grammarErrors: 'Grammar errors',
    spellingErrors: 'Spelling errors',
    styleSuggestions: 'Style suggestions',
    dictionarySettingsTitle: 'Dictionary Settings',
    addWordToDictionary: 'Add Word to Dictionary',
    enterWord: 'Enter word',
    addWord: 'Add',
    wordList: 'Personal Dictionary',
    noWordsYet: 'No words added yet',
    remove: 'Remove',
    appearanceSettingsTitle: 'Appearance Settings',
    theme: 'Theme',
    fontSize: 'Font Size',
    small: 'Small',
    normal: 'Normal',
    large: 'Large',
    highContrastMode: 'High contrast mode',
    highContrastModeDesc: 'Increase contrast for better readability',
    advancedSettingsTitle: 'Advanced Settings',
    performance: 'Performance',
    realTimeChecking: 'Real-time checking',
    realTimeCheckingDesc: 'Check grammar and spelling as you type',
    privacyAndData: 'Privacy & Data',
    saveWritingHistory: 'Save writing history',
    saveWritingHistoryDesc: 'Save your writing history for future reference',
    resetAndBackup: 'Reset & Backup',
    exportSettings: 'Export Settings',
    importSettings: 'Import Settings',
    resetToDefaults: 'Reset to Defaults',
    cancel: 'Cancel',
    saveChanges: 'Save Changes',
    helpAndTutorial: 'Help & Tutorial',
    gettingStarted: 'Getting Started',
    errorTypes: 'Error Types',
    keyboardShortcuts: 'Shortcuts',
    undo: 'Undo',
    redo: 'Redo',
    checkGrammar: 'Check Grammar',
    aiAssistant: 'AI Assistant',
    openSettings: 'Open Settings',
    exportDocument: 'Export Document',
    enterWordToLookup: 'Enter word to look up',
    lookingUpDefinition: 'Looking up...',
    search: 'Search',
    definitionNotFound: 'Definition not found'
  },
  es: {
    appName: 'GrammarPro',
    currentLanguage: 'Idioma',
    lightTheme: 'Tema Claro',
    darkTheme: 'Tema Oscuro',
    autoTheme: 'Tema Automático',
    settings: 'Configuración',
    help: 'Ayuda',
    // Add more Spanish translations
    writingTools: 'Herramientas de Escritura',
    writingMode: 'Modo de Escritura',
    casual: 'Casual',
    formal: 'Formal',
    academic: 'Académico',
    creative: 'Creativo',
    business: 'Negocios',
    // Add remaining Spanish translations as needed
    cancel: 'Cancelar',
    saveChanges: 'Guardar Cambios'
  },
  // Add translations for other languages
  fr: {
    appName: 'GrammarPro',
    currentLanguage: 'Langue',
    // Add French translations
    cancel: 'Annuler',
    saveChanges: 'Enregistrer'
  },
  de: {
    appName: 'GrammarPro',
    currentLanguage: 'Sprache',
    // Add German translations
    cancel: 'Abbrechen',
    saveChanges: 'Speichern'
  },
  tr: {
    appName: 'GrammarPro',
    currentLanguage: 'Dil',
    // Add Turkish translations
    cancel: 'İptal',
    saveChanges: 'Kaydet'
  },
  ar: {
    appName: 'جرامر برو',
    currentLanguage: 'اللغة',
    // Add Arabic translations
    cancel: 'إلغاء',
    saveChanges: 'حفظ التغييرات'
  },
  zh: {
    appName: '语法专家',
    currentLanguage: '语言',
    // Add Chinese translations
    cancel: '取消',
    saveChanges: '保存更改'
  },
  ja: {
    appName: 'グラマープロ',
    currentLanguage: '言語',
    // Add Japanese translations
    cancel: 'キャンセル',
    saveChanges: '変更を保存'
  },
  ru: {
    appName: 'ГрамматикаПро',
    currentLanguage: 'Язык',
    // Add Russian translations
    cancel: 'Отмена',
    saveChanges: 'Сохранить'
  },
  it: {
    appName: 'GrammarPro',
    currentLanguage: 'Lingua',
    // Add Italian translations
    cancel: 'Annulla',
    saveChanges: 'Salva'
  }
};

// Export translations
export const translations = {
  languages,
  strings
};
