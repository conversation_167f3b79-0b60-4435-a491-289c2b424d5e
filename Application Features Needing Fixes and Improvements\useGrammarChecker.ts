import { useCallback, useState } from 'react';

export interface ErrorType {
  type: 'spelling' | 'grammar' | 'style' | 'clarity';
  word: string;
  index: number;
  position: number;
  suggestions: string[];
  explanation: string;
}

/**
 * Hook to manage grammar and spelling functionality
 * @returns Object with grammar state and functions
 */
export const useGrammarChecker = () => {
  const [settings] = useState({
    grammarCheck: true,
    spellCheck: true,
    styleCheck: true,
    realTimeCheck: true
  });
  const [personalDictionary] = useState<string[]>(['grammarpro', 'multilingual']);
  const [currentLanguage] = useState('en');
  
  const [errors, setErrors] = useState<ErrorType[]>([]);
  const [analyzing, setAnalyzing] = useState(false);
  
  /**
   * Analyze text for grammar and spelling errors
   */
  const analyzeText = useCallback((text: string) => {
    if (!text.trim()) {
      setErrors([]);
      return;
    }
    
    setAnalyzing(true);
    
    // Simulate analysis delay
    setTimeout(() => {
      const foundErrors = findErrors(text);
      setErrors(foundErrors);
      setAnalyzing(false);
    }, 300);
  }, [currentLanguage, personalDictionary, settings]);
  
  /**
   * Find errors in text with improved position tracking
   */
  const findErrors = useCallback((text: string): ErrorType[] => {
    const errors: ErrorType[] = [];
    
    // Skip if features are disabled
    if (!settings.grammarCheck && !settings.spellCheck && !settings.styleCheck) {
      return errors;
    }
    
    const words = text.split(/\s+/);
    
    // Track word positions for accurate highlighting
    let position = 0;
    
    words.forEach((word, index) => {
      const wordPosition = text.indexOf(word, position);
      if (wordPosition === -1) return; // Skip if word not found
      
      position = wordPosition + word.length;
      
      // Check against dictionary if spell check is enabled
      if (settings.spellCheck && !isInDictionary(word) && !personalDictionary.includes(word.toLowerCase())) {
        errors.push({
          type: 'spelling',
          word: word,
          index: wordPosition,
          position: index,
          suggestions: generateSpellingSuggestions(word),
          explanation: `"${word}" may be misspelled.`
        });
      }
      
      // Check grammar rules if grammar check is enabled
      if (settings.grammarCheck && checkGrammarRules(words, index)) {
        errors.push({
          type: 'grammar',
          word: word,
          index: wordPosition,
          position: index,
          suggestions: generateGrammarSuggestions(words, index),
          explanation: 'Grammar issue detected'
        });
      }
      
      // Style checks if style check is enabled
      if (settings.styleCheck) {
        // Check for overly long words
        if (word.length > 10 && Math.random() < 0.2) {
          errors.push({
            type: 'style',
            word: word,
            index: wordPosition,
            position: index,
            suggestions: [word.substring(0, 5) + word.substring(word.length - 2)],
            explanation: 'Consider using a simpler word'
          });
        }
        
        // Check for vague words
        if (word.length < 3 && Math.random() < 0.1) {
          errors.push({
            type: 'clarity',
            word: word,
            index: wordPosition,
            position: index,
            suggestions: ['a more descriptive term'],
            explanation: 'This word may be too vague'
          });
        }
      }
    });
    
    return errors;
  }, [personalDictionary, settings]);
  
  /**
   * Apply a suggestion to fix an error
   */
  const applySuggestion = useCallback((element: HTMLElement, suggestion: string) => {
    if (element) {
      element.textContent = suggestion;
      element.className = ''; // Remove error highlighting
      element.removeAttribute('data-error');
    }
  }, []);
  
  return {
    errors,
    analyzing,
    analyzeText,
    applySuggestion,
    
    // Helper functions for components
    getErrorClass: (type: ErrorType['type']) => {
      switch (type) {
        case 'spelling':
          return 'spelling-error';
        case 'grammar':
          return 'grammar-error';
        case 'style':
          return 'style-suggestion';
        case 'clarity':
          return 'clarity-improvement';
        default:
          return 'spelling-error';
      }
    },
    
    getErrorColor: (type: ErrorType['type']) => {
      const colors = {
        spelling: 'bg-red-500',
        grammar: 'bg-blue-500',
        style: 'bg-green-500',
        clarity: 'bg-yellow-500'
      };
      return colors[type] || 'bg-gray-500';
    },
    
    getErrorTypeLabel: (type: ErrorType['type']) => {
      const labels = {
        'spelling': 'Spelling Error',
        'grammar': 'Grammar Issue',
        'style': 'Style Suggestion',
        'clarity': 'Clarity Improvement'
      };
      return labels[type] || 'Issue';
    }
  };
};

/**
 * Add CSS for error highlighting
 */
export const addGrammarStyles = () => {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    .spelling-error {
      border-bottom: 2px wavy var(--error-color, #dc2626);
      cursor: pointer;
    }
    
    .grammar-error {
      border-bottom: 2px wavy #0284c7;
      cursor: pointer;
    }
    
    .style-suggestion {
      border-bottom: 2px wavy var(--success-color, #16a34a);
      cursor: pointer;
    }
    
    .clarity-improvement {
      border-bottom: 2px wavy var(--warning-color, #d97706);
      cursor: pointer;
    }
    
    .suggestion-popup {
      animation: fadeIn 0.2s ease-in;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(5px); }
      to { opacity: 1; transform: translateY(0); }
    }
  `;
  document.head.appendChild(styleElement);
};

// Helper functions

/**
 * Check if a word is in the dictionary
 */
function isInDictionary(word: string): boolean {
  // This would connect to a real dictionary API or use a local dictionary
  // For now, just a simple check for common words
  const commonWords = ['the', 'and', 'is', 'in', 'to', 'of', 'that', 'for', 'it', 'with', 'as', 'be', 'on', 'not', 'by', 'at', 'from', 'we', 'this', 'but', 'or', 'an', 'will', 'all', 'have', 'are', 'there', 'been', 'has', 'would', 'more', 'if', 'no', 'when', 'can', 'who', 'which', 'you', 'they', 'their', 'what', 'so', 'up', 'out', 'about', 'into', 'than', 'them', 'then', 'some', 'her', 'him', 'one', 'its', 'time', 'only', 'do', 'may', 'such', 'should', 'now'];
  return commonWords.includes(word.toLowerCase());
}

/**
 * Generate spelling suggestions for a word
 */
function generateSpellingSuggestions(word: string): string[] {
  // This would use a real spell checking algorithm
  // For now, just generate some simple variations
  const suggestions = [];
  
  // Add 's' if not present
  if (!word.endsWith('s')) {
    suggestions.push(word + 's');
  }
  
  // Remove 's' if present
  if (word.endsWith('s')) {
    suggestions.push(word.slice(0, -1));
  }
  
  // Add 'ed' if not present
  if (!word.endsWith('ed')) {
    suggestions.push(word + 'ed');
  }
  
  // Add a common prefix
  suggestions.push('re' + word);
  
  return suggestions.slice(0, 3); // Limit to 3 suggestions
}

/**
 * Check grammar rules for a word in context
 */
function checkGrammarRules(words: string[], index: number): boolean {
  // This would implement real grammar rules
  // For now, just a simple example rule
  
  // Check for repeated words
  if (index > 0 && words[index].toLowerCase() === words[index - 1].toLowerCase()) {
    return true;
  }
  
  // Random grammar issues for demonstration
  return Math.random() < 0.05;
}

/**
 * Generate grammar suggestions for a word in context
 */
function generateGrammarSuggestions(words: string[], index: number): string[] {
  // This would implement real grammar suggestion logic
  // For now, just some simple examples
  
  // For repeated words
  if (index > 0 && words[index].toLowerCase() === words[index - 1].toLowerCase()) {
    return ['Remove repeated word'];
  }
  
  // Generic grammar suggestions
  return [
    'Rephrase this sentence',
    'Consider different wording',
    'Check verb agreement'
  ];
}
