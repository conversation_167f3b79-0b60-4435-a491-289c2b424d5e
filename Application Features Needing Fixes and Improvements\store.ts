import { create } from 'zustand';
import { Language, translations } from './features/language/translations';

export type FontSize = 'small' | 'normal' | 'large';

export type WritingStats = {
  words: number;
  characters: number;
  score: number;
};

export type AppSettings = {
  rtlSupport: boolean;
  aiOnSelection: boolean;
  grammarCheck: boolean;
  spellCheck: boolean;
  styleCheck: boolean;
  highContrast: boolean;
  realTimeCheck: boolean;
  saveHistory: boolean;
};

interface AppState {
  // Language state
  currentLanguage: Language;
  setCurrentLanguage: (language: Language) => void;
  translations: typeof translations;
  
  // Theme state
  fontSize: FontSize;
  setFontSize: (size: FontSize) => void;
  
  // Settings
  settings: AppSettings;
  updateSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => void;
  resetSettings: () => void;
  
  // Dictionary
  personalDictionary: string[];
  addToDictionary: (word: string) => void;
  removeFromDictionary: (word: string) => void;
  
  // Writing stats
  writingStats: WritingStats;
  updateWritingStats: (stats: Partial<WritingStats>) => void;
  
  // Recent prompts
  recentPrompts: string[];
  addRecentPrompt: (prompt: string) => void;
}

// Default settings
const DEFAULT_SETTINGS: AppSettings = {
  rtlSupport: true,
  aiOnSelection: true,
  grammarCheck: true,
  spellCheck: true,
  styleCheck: true,
  highContrast: false,
  realTimeCheck: true,
  saveHistory: true
};

// Create store
export const useAppStore = create<AppState>((set) => ({
  // Language state
  currentLanguage: 'en',
  setCurrentLanguage: (language) => set({ currentLanguage: language }),
  translations,
  
  // Theme state
  fontSize: 'normal',
  setFontSize: (size) => {
    set({ fontSize: size });
    localStorage.setItem('fontSize', size);
  },
  
  // Settings
  settings: DEFAULT_SETTINGS,
  updateSetting: (key, value) => set((state) => {
    const newSettings = { ...state.settings, [key]: value };
    localStorage.setItem('appSettings', JSON.stringify(newSettings));
    return { settings: newSettings };
  }),
  resetSettings: () => {
    localStorage.removeItem('appSettings');
    set({ settings: DEFAULT_SETTINGS });
  },
  
  // Dictionary
  personalDictionary: [],
  addToDictionary: (word) => set((state) => {
    if (state.personalDictionary.includes(word)) return state;
    const newDictionary = [...state.personalDictionary, word];
    localStorage.setItem('personalDictionary', JSON.stringify(newDictionary));
    return { personalDictionary: newDictionary };
  }),
  removeFromDictionary: (word) => set((state) => {
    const newDictionary = state.personalDictionary.filter(w => w !== word);
    localStorage.setItem('personalDictionary', JSON.stringify(newDictionary));
    return { personalDictionary: newDictionary };
  }),
  
  // Writing stats
  writingStats: {
    words: 0,
    characters: 0,
    score: 100
  },
  updateWritingStats: (stats) => set((state) => ({
    writingStats: { ...state.writingStats, ...stats }
  })),
  
  // Recent prompts
  recentPrompts: [
    'Rewrite this paragraph to be more formal',
    'Fix grammar errors in this text',
    'Summarize this content in 3 sentences',
    'Translate this to Spanish'
  ],
  addRecentPrompt: (prompt) => set((state) => {
    // Avoid duplicates and limit to 10 items
    const filteredPrompts = state.recentPrompts.filter(p => p !== prompt);
    const newPrompts = [prompt, ...filteredPrompts].slice(0, 10);
    return { recentPrompts: newPrompts };
  })
}));
