/**
 * Enhanced language switching functionality for GrammarPro application
 * Properly applies translations to all UI elements when language is changed
 */

// Language definitions with metadata
const languages = {
  'en': {
    name: 'English',
    rtl: false,
    flag: '🇺🇸'
  },
  'tr': {
    name: 'Turkish',
    rtl: false,
    flag: '🇹🇷'
  },
  'ar': {
    name: 'Arabic',
    rtl: true,
    flag: '🇸🇦'
  },
  'es': {
    name: 'Spanish',
    rtl: false,
    flag: '🇪🇸'
  },
  'de': {
    name: 'German',
    rtl: false,
    flag: '🇩🇪'
  },
  'fr': {
    name: 'French',
    rtl: false,
    flag: '🇫🇷'
  },
  'nl': {
    name: 'Dutch',
    rtl: false,
    flag: '🇳🇱'
  },
  'it': {
    name: 'Italian',
    rtl: false,
    flag: '🇮🇹'
  }
};

/**
 * Detect system language and set as current language if supported
 */
function detectSystemLanguage() {
  const browserLang = navigator.language || navigator.userLanguage;
  const langCode = browserLang.split('-')[0];
  
  if (languages[langCode]) {
    state.currentLanguage = langCode;
    const languageSelector = document.getElementById('languageSelector');
    if (languageSelector) {
      languageSelector.value = langCode;
    }
    updateLanguageUI();
  }
}

/**
 * Change application language
 * @param {Event} event - Change event from language selector
 */
function changeLanguage(event) {
  const newLang = event.target.value;
  if (languages[newLang]) {
    state.currentLanguage = newLang;
    updateLanguageUI();
    analyzeText(); // Re-analyze with new language rules
    saveSettings();
  }
}

/**
 * Update UI to reflect current language selection
 * This enhanced function properly applies translations to all UI elements
 */
function updateLanguageUI() {
  const lang = languages[state.currentLanguage];
  if (!lang) return; // Safety check
  
  const editor = document.getElementById('textEditor');
  const app = document.getElementById('app');
  
  // Update RTL support
  if (lang.rtl) {
    document.documentElement.setAttribute('dir', 'rtl');
    if (editor) editor.classList.add('rtl');
    if (app) app.classList.add('rtl');
  } else {
    document.documentElement.setAttribute('dir', 'ltr');
    if (editor) editor.classList.remove('rtl');
    if (app) app.classList.remove('rtl');
  }
  
  // Update current language display
  const currentLangElement = document.getElementById('currentLang');
  if (currentLangElement) {
    currentLangElement.textContent = lang.name;
  }
  
  // Apply translations to all UI elements
  applyTranslations(state.currentLanguage);
  
  // Update editor placeholder if empty
  if (editor && editor.textContent.trim() === '') {
    const placeholder = getTranslation('editorPlaceholder', state.currentLanguage);
    editor.innerHTML = `<div class="text-gray-400">${placeholder}</div>`;
  }
  
  // Update status
  updateStatus(getTranslation('languageChanged', state.currentLanguage) || `Switched to ${lang.name}`);
}

/**
 * Initialize language functionality
 * Sets up event listeners and loads initial language
 */
function initializeLanguage() {
  // Set up language selector event listener
  const languageSelector = document.getElementById('languageSelector');
  if (languageSelector) {
    languageSelector.addEventListener('change', changeLanguage);
  }
  
  // Add data-i18n attributes to all text elements that need translation
  addTranslationAttributes();
  
  // Load saved language or detect system language
  const savedLang = state.currentLanguage;
  if (savedLang && languages[savedLang]) {
    if (languageSelector) {
      languageSelector.value = savedLang;
    }
    updateLanguageUI();
  } else {
    detectSystemLanguage();
  }
}

/**
 * Add data-i18n attributes to all text elements that need translation
 * This is a one-time setup function that should be called during initialization
 */
function addTranslationAttributes() {
  // Header elements
  addAttribute('settingsBtn', 'settings', 'title');
  addAttribute('themeToggle', 'darkMode', 'title');
  addAttribute('helpBtn', 'help', 'title');
  
  // Writing tools section
  const writingToolsHeader = document.querySelector('.lg\\:col-span-1 h3');
  if (writingToolsHeader) addAttribute(writingToolsHeader, 'writingTools');
  
  // Writing mode label
  const writingModeLabel = document.querySelector('label.text-sm.font-medium.text-gray-700');
  if (writingModeLabel) addAttribute(writingModeLabel, 'writingMode');
  
  // Import document section
  const importDocLabel = document.querySelectorAll('label.text-sm.font-medium.text-gray-700')[1];
  if (importDocLabel) addAttribute(importDocLabel, 'importDocument');
  
  // Quick actions section
  const quickActionsLabel = document.querySelectorAll('label.text-sm.font-medium.text-gray-700')[2];
  if (quickActionsLabel) addAttribute(quickActionsLabel, 'quickActions');
  
  // Button texts
  addAttribute('checkGrammarBtn', 'checkAll');
  addAttribute('aiRewriteBtn', 'aiRewrite');
  addAttribute('exportBtn', 'export');
  addAttribute('clearBtn', 'clear');
  
  // Writing stats section
  const writingStatsHeader = document.querySelector('.writing-stats h4');
  if (writingStatsHeader) addAttribute(writingStatsHeader, 'writingStatistics');
  
  // Editor section
  const editorHeader = document.querySelector('.lg\\:col-span-2 h3');
  if (editorHeader) addAttribute(editorHeader, 'documentEditor');
  
  // Status text
  const statusText = document.getElementById('statusText');
  if (statusText && statusText.textContent === 'Ready') addAttribute(statusText, 'ready');
  
  // Editor buttons
  addAttribute('undoBtn', 'undo', 'title');
  addAttribute('redoBtn', 'redo', 'title');
  
  // Editor footer
  const lastSavedLabel = document.querySelector('.flex.items-center.space-x-4 span');
  if (lastSavedLabel) {
    const text = lastSavedLabel.textContent;
    if (text.includes('Last saved:')) {
      lastSavedLabel.innerHTML = `<span data-i18n="lastSaved">Last saved</span>: <span id="lastSaved" data-i18n="never">Never</span>`;
    }
  }
  
  // Add more attributes for other UI elements
  // This is a partial implementation - in a real scenario, all text elements would need attributes
}

/**
 * Helper function to add data-i18n attribute to an element
 * @param {string|Element} element - Element ID or element object
 * @param {string} key - Translation key
 * @param {string} attrType - Type of attribute (default: direct text content)
 */
function addAttribute(element, key, attrType = null) {
  const el = typeof element === 'string' ? document.getElementById(element) : element;
  if (!el) return;
  
  if (attrType === 'title') {
    el.setAttribute('data-i18n-title', key);
  } else if (attrType === 'placeholder') {
    el.setAttribute('data-i18n-placeholder', key);
  } else {
    el.setAttribute('data-i18n', key);
  }
}
