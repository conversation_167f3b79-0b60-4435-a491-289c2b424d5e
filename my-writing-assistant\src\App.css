@tailwind base;
@tailwind components;
@tailwind utilities;

/* You can keep other global CSS below these lines */
body {
  margin: 0;
  font-family: 'Inter', sans-serif; /* Ensure Inter font is loaded */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Add any custom styles from the <style> block in the React code here */
.toggle-switch {
  appearance: none;
  width: 48px;
  height: 24px;
  border-radius: 9999px;
  background-color: #d1d5db; /* light gray */
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  outline: none;
}

.toggle-switch:checked {
  background-color: #2563eb; /* blue-600 */
}

.toggle-switch::before {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #ffffff;
  top: 2px;
  left: 2px;
  transition: transform 0.2s ease-in-out;
}

.toggle-switch:checked::before {
  transform: translateX(24px);
}

.toggle-switch:focus-visible {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); /* blue-500 with opacity */
}

/* High Contrast Mode */
.high-contrast-mode {
  background-color: #000 !important;
  color: #fff !important;
}
.high-contrast-mode .bg-white {
  background-color: #000 !important;
  border-color: #fff !important;
}
.high-contrast-mode .dark\:bg-gray-800 { /* Note: Backslash for escaping in CSS */
  background-color: #000 !important;
  border-color: #fff !important;
}
.high-contrast-mode .text-gray-900 {
  color: #fff !important;
}
.high-contrast-mode .dark\:text-gray-100 {
  color: #fff !important;
}
.high-contrast-mode .text-gray-700 {
  color: #fff !important;
}
.high-contrast-mode .dark\:text-gray-300 {
  color: #fff !important;
}
.high-contrast-mode .border-gray-200 {
  border-color: #fff !important;
}
.high-contrast-mode .dark\:border-gray-700 {
  border-color: #fff !important;
}
.high-contrast-mode .bg-gray-50 {
  background-color: #333 !important;
}
.high-contrast-mode .dark\:bg-gray-700 {
  background-color: #333 !important;
}
.high-contrast-mode .border-gray-300 {
  border-color: #fff !important;
}
.high-contrast-mode .dark\:border-gray-600 {
  border-color: #fff !important;
}
.high-contrast-mode select,
.high-contrast-mode textarea,
.high-contrast-mode input[type="text"] {
  background-color: #333 !important;
  color: #fff !important;
  border-color: #fff !important;
}
.high-contrast-mode button {
  border: 1px solid #fff !important;
  color: #fff !important;
  background-color: #000 !important;
}
.high-contrast-mode button.bg-blue-600,
.high-contrast-mode button.bg-green-600,
.high-contrast-mode button.bg-purple-600,
.high-contrast-mode button.bg-indigo-600,
.high-contrast-mode button.bg-cyan-600,
.high-contrast-mode button.bg-teal-600,
.high-contrast-mode button.bg-red-500,
.high-contrast-mode button.bg-pink-600,
.high-contrast-mode button.bg-orange-600 {
  background-color: #000 !important;
  border-color: #fff !important;
  color: #fff !important;
}
.high-contrast-mode button:hover {
  background-color: #333 !important;
}
.high-contrast-mode .toggle-switch {
  background-color: #666 !important;
}
.high-contrast-mode .toggle-switch:checked {
  background-color: #fff !important;
}
.high-contrast-mode .toggle-switch::before {
  background-color: #000 !important;
}
.high-contrast-mode .toggle-switch:checked::before {
  background-color: #000 !important;
}
.high-contrast-mode .bg-blue-50 {
    background-color: #000 !important;
    border-color: #fff !important;
}
.high-contrast-mode .dark\:bg-blue-900 {
    background-color: #000 !important;
    border-color: #fff !important;
}
.high-contrast-mode .text-blue-800 {
    color: #fff !important;
}
.high-contrast-mode .dark\:text-blue-200 {
    color: #fff !important;
}
.high-contrast-mode .bg-red-50 {
    background-color: #000 !important;
    border-color: #fff !important;
}
.high-contrast-mode .dark\:bg-red-900 {
    background-color: #000 !important;
    border-color: #fff !important;
}
.high-contrast-mode .text-red-500 {
    color: #fff !important;
}
.high-contrast-mode .dark\:text-red-400 {
    color: #fff !important;
}
