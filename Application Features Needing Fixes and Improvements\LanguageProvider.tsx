import { createContext, useContext, useState } from 'react';
import { translations, Language, LanguageInfo } from '../lib/features/language/translations';

// Create context
interface LanguageContextType {
  currentLanguage: Language;
  languages: Record<Language, LanguageInfo>;
  updateLanguage: (language: Language) => void;
  detectSystemLanguage: () => Language;
  rtlEnabled: boolean;
  t: (key: string) => string;
  getCurrentLanguageInfo: () => LanguageInfo;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Provider component
export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  
  // Get all available languages from translations
  const languages = translations.languages;
  
  // Detect system language
  const detectSystemLanguage = (): Language => {
    const browserLang = navigator.language.split('-')[0];
    return (browserLang as Language) in languages ? (browserLang as Language) : 'en';
  };
  
  // Update language
  const updateLanguage = (language: Language) => {
    setCurrentLanguage(language);
    document.documentElement.lang = language;
    
    // Set RTL attribute if needed
    if (languages[language].rtl) {
      document.documentElement.dir = 'rtl';
    } else {
      document.documentElement.dir = 'ltr';
    }
    
    // Save preference
    localStorage.setItem('preferredLanguage', language);
  };
  
  // Translation function
  const t = (key: string): string => {
    return translations.strings[currentLanguage]?.[key] || translations.strings.en[key] || key;
  };
  
  // Get current language info
  const getCurrentLanguageInfo = (): LanguageInfo => {
    return languages[currentLanguage];
  };
  
  // Check if current language is RTL
  const rtlEnabled = languages[currentLanguage]?.rtl || false;
  
  return (
    <LanguageContext.Provider
      value={{
        currentLanguage,
        languages,
        updateLanguage,
        detectSystemLanguage,
        rtlEnabled,
        t,
        getCurrentLanguageInfo
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

// Hook for using the language context
export function useLanguageContext() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguageContext must be used within a LanguageProvider');
  }
  return context;
}
