import { useLanguageContext } from './LanguageProvider';
import { Button } from './ui/button';
import { useThemeContext } from './ThemeProvider';

export function Header() {
  const { t, currentLanguage, languages, updateLanguage } = useLanguageContext();
  const { theme, toggleTheme } = useThemeContext();
  
  return (
    <header className="bg-primary text-white shadow-md">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo and App Name */}
          <div className="flex items-center space-x-2">
            <span className="text-xl font-bold">{t('appName')}</span>
          </div>
          
          {/* Language Selector */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm hidden md:inline">{t('currentLanguage')}:</span>
              <select
                className="bg-primary-dark text-white rounded px-2 py-1 text-sm"
                value={currentLanguage}
                onChange={(e) => updateLanguage(e.target.value as any)}
                aria-label={t('currentLanguage')}
              >
                {Object.entries(languages).map(([code, info]) => (
                  <option key={code} value={code}>
                    {info.flag} {info.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              title={theme === 'dark' ? t('lightTheme') : theme === 'light' ? t('darkTheme') : t('autoTheme')}
            >
              {theme === 'dark' ? (
                <i className="fas fa-sun text-lg"></i>
              ) : theme === 'light' ? (
                <i className="fas fa-moon text-lg"></i>
              ) : (
                <i className="fas fa-adjust text-lg"></i>
              )}
            </Button>
            
            {/* Settings Button */}
            <Button
              variant="ghost"
              size="icon"
              title={t('settings')}
            >
              <i className="fas fa-cog text-lg"></i>
            </Button>
            
            {/* Help Button */}
            <Button
              variant="ghost"
              size="icon"
              title={t('help')}
            >
              <i className="fas fa-question-circle text-lg"></i>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
