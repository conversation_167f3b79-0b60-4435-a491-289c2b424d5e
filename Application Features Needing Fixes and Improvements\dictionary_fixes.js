/**
 * Enhanced dictionary functionality for GrammarPro application
 * Implements proper dictionary lookup and personal dictionary management
 */

/**
 * Dictionary API service
 * Handles word lookups and definition formatting
 */
const dictionaryService = {
  /**
   * Look up a word in the dictionary
   * @param {string} word - The word to look up
   * @param {string} language - The language code (defaults to current language)
   * @returns {Promise} - Promise resolving to definition data
   */
  async lookupWord(word, language = 'en') {
    try {
      // This would be replaced with an actual API call in production
      // For now, simulate API call with timeout
      return new Promise((resolve) => {
        setTimeout(() => {
          const definition = this.getMockDefinition(word, language);
          resolve(definition);
        }, 500);
      });
    } catch (error) {
      console.error('Dictionary lookup failed:', error);
      return { error: 'Definition not found' };
    }
  },
  
  /**
   * Format definition data for display
   * @param {Object} data - Raw definition data
   * @returns {Object} - Formatted definition
   */
  formatDefinition(data) {
    if (!data || !data[0]) return { error: 'No definition available' };
    
    const entry = data[0];
    const result = {
      word: entry.word,
      phonetic: entry.phonetic || '',
      meanings: []
    };
    
    if (entry.meanings && entry.meanings.length) {
      entry.meanings.forEach(meaning => {
        result.meanings.push({
          partOfSpeech: meaning.partOfSpeech,
          definitions: meaning.definitions.map(def => ({
            definition: def.definition,
            example: def.example || ''
          }))
        });
      });
    }
    
    return result;
  },
  
  /**
   * Get mock definition for testing
   * @param {string} word - The word to define
   * @param {string} language - The language code
   * @returns {Object} - Mock definition data
   */
  getMockDefinition(word, language) {
    // Simple mock definitions for testing
    const mockDefinitions = {
      'grammar': [{
        word: 'grammar',
        phonetic: '/ˈɡramər/',
        meanings: [
          {
            partOfSpeech: 'noun',
            definitions: [
              {
                definition: 'The whole system and structure of a language or of languages in general, usually taken as consisting of syntax and morphology.',
                example: 'Teachers of English focus too much on grammar'
              },
              {
                definition: 'A book on grammar.',
                example: 'My old Latin grammar'
              }
            ]
          }
        ]
      }],
      'spelling': [{
        word: 'spelling',
        phonetic: '/ˈspelɪŋ/',
        meanings: [
          {
            partOfSpeech: 'noun',
            definitions: [
              {
                definition: 'The process or activity of writing or naming the letters of a word.',
                example: 'The spelling of his name was incorrect'
              },
              {
                definition: 'The way a word is spelled.',
                example: 'American spelling differs from British spelling'
              }
            ]
          }
        ]
      }]
    };
    
    // Return mock definition or error
    return mockDefinitions[word.toLowerCase()] || [{ error: 'Definition not found' }];
  }
};

/**
 * Show dictionary lookup UI for a word
 * @param {string} word - The word to look up
 */
function showDictionaryLookup(word) {
  // Create or get lookup container
  let lookupContainer = document.getElementById('dictionaryLookup');
  if (!lookupContainer) {
    lookupContainer = document.createElement('div');
    lookupContainer.id = 'dictionaryLookup';
    lookupContainer.className = 'fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-xl border border-gray-200 w-96 max-w-full z-50';
    document.body.appendChild(lookupContainer);
  }
  
  // Show loading state
  lookupContainer.innerHTML = `
    <div class="text-center py-4">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-emerald-600 mx-auto"></div>
      <p class="text-sm mt-2" data-i18n="lookingUpDefinition">Looking up definition...</p>
    </div>
  `;
  lookupContainer.classList.remove('hidden');
  
  // Apply theme
  const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
  if (isDark) {
    lookupContainer.classList.remove('bg-white');
    lookupContainer.classList.add('bg-gray-800', 'text-white');
  } else {
    lookupContainer.classList.remove('bg-gray-800', 'text-white');
    lookupContainer.classList.add('bg-white');
  }
  
  // Fetch definition
  dictionaryService.lookupWord(word, state.currentLanguage)
    .then(result => {
      if (result.error) {
        lookupContainer.innerHTML = `
          <div class="text-center py-4">
            <p class="text-sm text-gray-500" data-i18n="definitionNotFound">Definition not found</p>
            <button id="closeDictionary" class="mt-2 px-4 py-1 bg-gray-200 rounded text-sm hover:bg-gray-300 transition-colors">
              ${getTranslation('close', state.currentLanguage)}
            </button>
          </div>
        `;
        return;
      }
      
      // Format and display definition
      let html = `
        <div class="p-4">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-lg font-medium">${result.word}</h3>
            <span class="text-gray-500">${result.phonetic}</span>
          </div>
      `;
      
      result.meanings.forEach(meaning => {
        html += `
          <div class="mb-3">
            <div class="text-sm font-medium text-emerald-600 mb-1">${meaning.partOfSpeech}</div>
            <ol class="list-decimal list-inside space-y-1">
        `;
        
        meaning.definitions.forEach(def => {
          html += `
            <li class="text-sm">
              ${def.definition}
              ${def.example ? `<div class="text-xs text-gray-500 ml-5 mt-1">"${def.example}"</div>` : ''}
            </li>
          `;
        });
        
        html += `
            </ol>
          </div>
        `;
      });
      
      html += `
          <div class="mt-3 text-right">
            <button id="closeDictionary" class="px-4 py-1 bg-gray-200 rounded text-sm hover:bg-gray-300 transition-colors">
              ${getTranslation('close', state.currentLanguage)}
            </button>
          </div>
        </div>
      `;
      
      lookupContainer.innerHTML = html;
      
      // Apply dark theme to content if needed
      if (isDark) {
        lookupContainer.querySelectorAll('.text-gray-500').forEach(el => {
          el.classList.remove('text-gray-500');
          el.classList.add('text-gray-300');
        });
        lookupContainer.querySelectorAll('.bg-gray-200').forEach(el => {
          el.classList.remove('bg-gray-200', 'hover:bg-gray-300');
          el.classList.add('bg-gray-700', 'hover:bg-gray-600', 'text-white');
        });
      }
      
      // Add close button event listener
      document.getElementById('closeDictionary').addEventListener('click', () => {
        lookupContainer.classList.add('hidden');
      });
    });
}

/**
 * Add a word to the personal dictionary
 */
function addWordToDictionary() {
  const input = document.getElementById('newWord');
  if (!input) return;
  
  const word = input.value.trim();
  
  if (word && !state.personalDictionary.includes(word)) {
    state.personalDictionary.push(word);
    input.value = '';
    updateDictionaryList();
    saveSettings();
    updateStatus(getTranslation('wordAddedToDictionary', state.currentLanguage) || `Added "${word}" to dictionary`);
  }
}

/**
 * Remove a word from the personal dictionary
 * @param {string} word - The word to remove
 */
function removeFromDictionary(word) {
  const index = state.personalDictionary.indexOf(word);
  if (index > -1) {
    state.personalDictionary.splice(index, 1);
    updateDictionaryList();
    saveSettings();
    updateStatus(getTranslation('wordRemovedFromDictionary', state.currentLanguage) || `Removed "${word}" from dictionary`);
  }
}

/**
 * Update the personal dictionary word list in the UI
 * This enhanced function uses DOM manipulation for better security
 */
function updateDictionaryList() {
  const list = document.getElementById('dictionaryList');
  if (!list) return;
  
  // Clear the list
  list.innerHTML = '';
  
  // Create container
  const container = document.createElement('div');
  container.className = 'space-y-1';
  
  if (state.personalDictionary.length === 0) {
    const emptyMessage = document.createElement('div');
    emptyMessage.className = 'text-sm text-gray-500 text-center py-2';
    emptyMessage.textContent = getTranslation('noWordsYet', state.currentLanguage) || 'No words in personal dictionary yet';
    container.appendChild(emptyMessage);
  } else {
    // Add each word
    state.personalDictionary.forEach(word => {
      const wordItem = document.createElement('div');
      wordItem.className = 'flex items-center justify-between text-sm';
      
      const wordSpan = document.createElement('span');
      wordSpan.textContent = word;
      
      const removeButton = document.createElement('button');
      removeButton.className = 'text-red-500 hover:text-red-700 text-xs';
      removeButton.textContent = getTranslation('remove', state.currentLanguage) || 'Remove';
      removeButton.addEventListener('click', () => removeFromDictionary(word));
      
      wordItem.appendChild(wordSpan);
      wordItem.appendChild(removeButton);
      container.appendChild(wordItem);
    });
  }
  
  // Add to the list
  list.appendChild(container);
}

/**
 * Check if a word is in the personal dictionary
 * @param {string} word - The word to check
 * @returns {boolean} - Whether the word is in the personal dictionary
 */
function isInPersonalDictionary(word) {
  return state.personalDictionary.includes(word.toLowerCase());
}

/**
 * Initialize dictionary functionality
 * Sets up event listeners and loads initial dictionary
 */
function initializeDictionary() {
  // Set up add word button
  const addWordBtn = document.getElementById('addWordBtn');
  if (addWordBtn) {
    addWordBtn.addEventListener('click', addWordToDictionary);
  }
  
  // Set up new word input
  const newWordInput = document.getElementById('newWord');
  if (newWordInput) {
    newWordInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') addWordToDictionary();
    });
  }
  
  // Initialize dictionary list
  updateDictionaryList();
  
  // Add double-click handler to editor for word lookup
  const editor = document.getElementById('textEditor');
  if (editor) {
    editor.addEventListener('dblclick', (e) => {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();
      
      if (selectedText && selectedText.length > 0) {
        showDictionaryLookup(selectedText);
      }
    });
  }
}
