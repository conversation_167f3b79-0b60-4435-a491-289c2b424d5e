import { useEffect, useState } from 'react';

export type FontSize = 'small' | 'normal' | 'large';

/**
 * Hook to manage appearance functionality
 * @returns Object with appearance state and functions
 */
export const useAppearance = () => {
  const [fontSize, setFontSize] = useState<FontSize>('normal');
  const [highContrast, setHighContrast] = useState(false);
  
  // Apply font size when it changes
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove existing font size classes
    root.classList.remove('text-sm', 'text-base', 'text-lg');
    
    // Apply font size class
    switch (fontSize) {
      case 'small':
        root.classList.add('text-sm');
        break;
      case 'normal':
        root.classList.add('text-base');
        break;
      case 'large':
        root.classList.add('text-lg');
        break;
    }
  }, [fontSize]);
  
  // Apply high contrast when it changes
  useEffect(() => {
    const root = document.documentElement;
    
    if (highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
  }, [highContrast]);
  
  return {
    fontSize,
    setFontSize,
    highContrast,
    setHighContrast
  };
};

/**
 * Add CSS for appearance settings
 */
export const addAppearanceStyles = () => {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    .text-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
    
    .text-base {
      font-size: 1rem;
      line-height: 1.5rem;
    }
    
    .text-lg {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
    
    .high-contrast {
      --background: #000000;
      --foreground: #ffffff;
      --primary: #3b82f6;
      --primary-dark: #2563eb;
      --primary-foreground: #ffffff;
      --secondary: #1f2937;
      --secondary-foreground: #ffffff;
      --accent: #1f2937;
      --accent-foreground: #ffffff;
      --destructive: #ef4444;
      --destructive-foreground: #ffffff;
      --ring: #ffffff;
      --error-color: #ef4444;
      --success-color: #22c55e;
      --warning-color: #f59e0b;
    }
    
    .high-contrast.light {
      --background: #ffffff;
      --foreground: #000000;
      --primary: #0000ff;
      --primary-dark: #0000cc;
      --primary-foreground: #ffffff;
      --secondary: #f3f4f6;
      --secondary-foreground: #000000;
      --accent: #f3f4f6;
      --accent-foreground: #000000;
      --destructive: #ff0000;
      --destructive-foreground: #ffffff;
      --ring: #000000;
      --error-color: #ff0000;
      --success-color: #008000;
      --warning-color: #ff8c00;
    }
    
    .rtl {
      direction: rtl;
      text-align: right;
    }
  `;
  document.head.appendChild(styleElement);
};
