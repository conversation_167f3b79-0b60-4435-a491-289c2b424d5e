import { createContext, useContext, useState, useEffect } from 'react';

export type Theme = 'light' | 'dark' | 'auto';

interface ThemeContextType {
  theme: Theme;
  setTheme: React.Dispatch<React.SetStateAction<Theme>>;
  toggleTheme: () => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('auto');
  const [isDark, setIsDark] = useState(false);
  
  // Toggle between light, dark, and auto
  const toggleTheme = () => {
    setTheme(current => {
      if (current === 'light') return 'dark';
      if (current === 'dark') return 'auto';
      return 'light';
    });
  };
  
  // Apply theme to document
  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light-theme', 'dark-theme');
    
    // Determine if we should use dark mode
    let shouldUseDark = false;
    
    if (newTheme === 'dark') {
      shouldUseDark = true;
    } else if (newTheme === 'auto') {
      shouldUseDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    
    // Apply appropriate theme class
    if (shouldUseDark) {
      root.classList.add('dark-theme');
      setIsDark(true);
    } else {
      root.classList.add('light-theme');
      setIsDark(false);
    }
    
    // Save preference
    localStorage.setItem('theme', newTheme);
  };
  
  // Effect to initialize theme on load
  useEffect(() => {
    // Load saved theme or use auto
    const savedTheme = localStorage.getItem('theme') as Theme || 'auto';
    setTheme(savedTheme);
    applyTheme(savedTheme);
    
    // Listen for system preference changes if using auto
    if (savedTheme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => applyTheme('auto');
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, []);
  
  // Effect to apply theme when it changes
  useEffect(() => {
    applyTheme(theme);
  }, [theme]);
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme, isDark }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useThemeContext() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
}
