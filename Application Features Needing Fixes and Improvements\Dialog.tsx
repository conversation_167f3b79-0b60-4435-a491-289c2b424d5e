import * as React from "react";
import { cn } from "../../lib/utils";

const Dialog = ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    role="dialog"
    {...props}
    className={cn("fixed inset-0 z-50 flex items-center justify-center", props.className)}
  >
    {children}
  </div>
);

const DialogContent = ({
  children,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "bg-background p-6 shadow-lg rounded-lg max-w-md w-full max-h-[85vh] overflow-auto",
      className
    )}
    {...props}
  >
    {children}
  </div>
);

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("flex flex-col space-y-1.5 text-center sm:text-left mb-4", className)}
    {...props}
  />
);

const DialogTitle = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) => (
  <h3
    className={cn("text-lg font-semibold leading-none tracking-tight", className)}
    {...props}
  />
);

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-4", className)}
    {...props}
  />
);

export { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter };
