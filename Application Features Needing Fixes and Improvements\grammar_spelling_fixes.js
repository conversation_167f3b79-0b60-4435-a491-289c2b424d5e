/**
 * Enhanced grammar and spelling assistance functionality for GrammarPro application
 * Properly implements error highlighting and suggestion display
 */

/**
 * Analyze text for grammar and spelling errors
 * This enhanced function properly tracks word positions and checks against rules
 */
function analyzeText() {
  const editor = document.getElementById('textEditor');
  if (!editor) return;
  
  const text = editor.textContent;
  
  if (!text.trim()) {
    clearSuggestions();
    return;
  }
  
  updateStatus(getTranslation('analyzing', state.currentLanguage));
  
  // Simulate analysis delay
  setTimeout(() => {
    const errors = findErrors(text);
    highlightErrors(editor, errors);
    updateSuggestions(errors);
    updateWritingStats();
    updateToneAnalysis(text);
    updateStatus(getTranslation('analysisComplete', state.currentLanguage));
  }, 300);
}

/**
 * Find errors in text with improved position tracking
 * @param {string} text - The text to analyze
 * @returns {Array} - Array of error objects
 */
function findErrors(text) {
  const errors = [];
  const words = text.split(/\s+/);
  
  // Track word positions for accurate highlighting
  let position = 0;
  
  words.forEach((word, index) => {
    const wordPosition = text.indexOf(word, position);
    if (wordPosition === -1) return; // Skip if word not found
    
    position = wordPosition + word.length;
    
    // Check against dictionary
    if (!isInDictionary(word) && !state.personalDictionary.includes(word.toLowerCase())) {
      errors.push({
        type: 'spelling',
        word: word,
        index: wordPosition,
        position: index,
        suggestions: generateSpellingSuggestions(word),
        explanation: `"${word}" may be misspelled.`
      });
    }
    
    // Check grammar rules (simplified example)
    if (checkGrammarRules(words, index)) {
      errors.push({
        type: 'grammar',
        word: word,
        index: wordPosition,
        position: index,
        suggestions: generateGrammarSuggestions(words, index),
        explanation: 'Grammar issue detected'
      });
    }
    
    // Style checks
    if (word.length > 10 && Math.random() < 0.2) {
      errors.push({
        type: 'style',
        word: word,
        index: wordPosition,
        position: index,
        suggestions: [word.substring(0, 5) + word.substring(word.length - 2)],
        explanation: 'Consider using a simpler word'
      });
    }
    
    // Clarity checks
    if (word.length < 3 && Math.random() < 0.1) {
      errors.push({
        type: 'clarity',
        word: word,
        index: wordPosition,
        position: index,
        suggestions: ['a more descriptive term'],
        explanation: 'This word may be too vague'
      });
    }
  });
  
  return errors;
}

/**
 * Highlight errors in the editor using DOM manipulation
 * @param {Element} editor - The editor element
 * @param {Array} errors - Array of error objects
 */
function highlightErrors(editor, errors) {
  // Store original content and selection
  const selection = window.getSelection();
  const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
  
  // Remove existing highlights using DOM manipulation instead of regex
  const errorSpans = editor.querySelectorAll('.spelling-error, .grammar-error, .style-suggestion, .clarity-improvement');
  errorSpans.forEach(span => {
    const textNode = document.createTextNode(span.textContent);
    span.parentNode.replaceChild(textNode, span);
  });
  
  // Create a document fragment to work with
  const content = editor.textContent;
  const tempDiv = document.createElement('div');
  tempDiv.textContent = content;
  
  // Sort errors by position to process them in reverse order
  // (processing in reverse prevents position shifts)
  errors.sort((a, b) => b.index - a.index);
  
  errors.forEach(error => {
    // Find the actual position of the error in the text
    const errorIndex = error.index;
    if (errorIndex === -1) return; // Skip if word not found
    
    // Create the error highlight span
    const errorSpan = document.createElement('span');
    errorSpan.textContent = error.word;
    errorSpan.className = getErrorClass(error.type);
    errorSpan.dataset.error = JSON.stringify(error);
    
    // Split text into before, error word, and after
    const before = content.substring(0, errorIndex);
    const after = content.substring(errorIndex + error.word.length);
    
    // Update the temp div content
    tempDiv.innerHTML = '';
    tempDiv.appendChild(document.createTextNode(before));
    tempDiv.appendChild(errorSpan);
    tempDiv.appendChild(document.createTextNode(after));
  });
  
  // Replace editor content
  editor.innerHTML = tempDiv.innerHTML;
  
  // Add click handlers for error highlights
  editor.querySelectorAll('[data-error]').forEach(span => {
    span.addEventListener('click', function() {
      const error = JSON.parse(this.dataset.error);
      showErrorSuggestion(error, this);
    });
  });
  
  // Restore selection if possible
  if (range) {
    try {
      selection.removeAllRanges();
      selection.addRange(range);
    } catch (e) {
      console.warn('Could not restore selection', e);
    }
  }
}

/**
 * Get the appropriate CSS class for an error type
 * @param {string} type - The error type
 * @returns {string} - The CSS class name
 */
function getErrorClass(type) {
  switch (type) {
    case 'spelling':
      return 'spelling-error';
    case 'grammar':
      return 'grammar-error';
    case 'style':
      return 'style-suggestion';
    case 'clarity':
      return 'clarity-improvement';
    default:
      return 'spelling-error';
  }
}

/**
 * Show suggestion popup for an error
 * @param {Object} error - The error object
 * @param {Element} element - The element containing the error
 */
function showErrorSuggestion(error, element) {
  // Remove any existing popups
  const existingPopup = document.querySelector('.suggestion-popup');
  if (existingPopup) {
    document.body.removeChild(existingPopup);
  }
  
  // Create suggestion popup
  const popup = document.createElement('div');
  popup.className = 'suggestion-popup bg-white shadow-lg rounded-lg p-3 absolute z-50';
  popup.style.minWidth = '200px';
  popup.style.maxWidth = '300px';
  
  // Calculate position
  const rect = element.getBoundingClientRect();
  popup.style.top = `${rect.bottom + window.scrollY + 5}px`;
  popup.style.left = `${rect.left + window.scrollX}px`;
  
  // Create content
  let content = `
    <div class="text-sm font-medium mb-2">${getErrorTypeLabel(error.type)}</div>
    <p class="text-xs text-gray-600 mb-2">${error.explanation}</p>
  `;
  
  if (error.suggestions && error.suggestions.length > 0) {
    content += '<div class="suggestions-list space-y-1">';
    error.suggestions.forEach(suggestion => {
      content += `
        <button class="w-full text-left text-xs bg-gray-50 hover:bg-gray-100 p-1 rounded apply-suggestion">
          ${suggestion}
        </button>
      `;
    });
    content += '</div>';
  }
  
  popup.innerHTML = content;
  document.body.appendChild(popup);
  
  // Apply theme to popup
  const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
  if (isDark) {
    popup.classList.remove('bg-white');
    popup.classList.add('bg-gray-800');
    popup.querySelectorAll('.text-gray-600').forEach(el => {
      el.classList.remove('text-gray-600');
      el.classList.add('text-gray-300');
    });
    popup.querySelectorAll('.bg-gray-50').forEach(el => {
      el.classList.remove('bg-gray-50');
      el.classList.add('bg-gray-700');
    });
    popup.querySelectorAll('.hover\\:bg-gray-100').forEach(el => {
      el.classList.remove('hover:bg-gray-100');
      el.classList.add('hover:bg-gray-600');
    });
  }
  
  // Add event listeners
  popup.querySelectorAll('.apply-suggestion').forEach((btn, index) => {
    btn.addEventListener('click', () => {
      applySuggestion(element, error.suggestions[index]);
      document.body.removeChild(popup);
    });
  });
  
  // Close when clicking outside
  document.addEventListener('click', function closePopup(e) {
    if (!popup.contains(e.target) && e.target !== element) {
      if (document.body.contains(popup)) {
        document.body.removeChild(popup);
      }
      document.removeEventListener('click', closePopup);
    }
  });
}

/**
 * Get a user-friendly label for an error type
 * @param {string} type - The error type
 * @returns {string} - The label
 */
function getErrorTypeLabel(type) {
  const labels = {
    'spelling': 'Spelling Error',
    'grammar': 'Grammar Issue',
    'style': 'Style Suggestion',
    'clarity': 'Clarity Improvement'
  };
  return labels[type] || 'Issue';
}

/**
 * Apply a suggestion to fix an error
 * @param {Element} element - The element containing the error
 * @param {string} suggestion - The suggestion to apply
 */
function applySuggestion(element, suggestion) {
  element.textContent = suggestion;
  element.className = ''; // Remove error highlighting
  element.removeAttribute('data-error');
  
  // Update stats
  updateWritingStats();
  
  // Show feedback
  const feedback = document.createElement('div');
  feedback.className = 'fixed bottom-4 right-4 bg-emerald-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 fade-in';
  feedback.textContent = 'Correction applied';
  document.body.appendChild(feedback);
  
  // Remove after delay
  setTimeout(() => {
    feedback.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(feedback)) {
        document.body.removeChild(feedback);
      }
    }, 300);
  }, 2000);
}

/**
 * Update suggestions panel with current errors
 * @param {Array} errors - Array of error objects
 */
function updateSuggestions(errors) {
  const suggestionsList = document.getElementById('suggestionsList');
  if (!suggestionsList) return;
  
  if (errors.length === 0) {
    suggestionsList.innerHTML = `
      <div class="text-sm text-green-600 text-center py-8">
        <i class="fas fa-check-circle text-2xl mb-2"></i>
        <p data-i18n="noIssues">No issues found!</p>
      </div>
    `;
    return;
  }
  
  const suggestionsHTML = errors.slice(0, 5).map(error => `
    <div class="suggestion-card border border-gray-200 rounded p-3 hover:shadow-md transition-shadow cursor-pointer">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center mb-1">
            <span class="inline-block w-2 h-2 rounded-full ${getErrorColor(error.type)} mr-2"></span>
            <span class="text-sm font-medium capitalize">${error.type}</span>
          </div>
          <p class="text-sm text-gray-600 mb-2">${error.explanation}</p>
          <div class="text-xs text-gray-500">
            Suggestions: ${error.suggestions.slice(0, 2).join(', ')}
          </div>
        </div>
        <button class="text-emerald-600 hover:text-emerald-700 text-sm font-medium ml-2 fix-error" data-error-index="${errors.indexOf(error)}">
          Fix
        </button>
      </div>
    </div>
  `).join('');
  
  suggestionsList.innerHTML = suggestionsHTML;
  
  // Add event listeners to fix buttons
  suggestionsList.querySelectorAll('.fix-error').forEach(button => {
    button.addEventListener('click', function() {
      const errorIndex = parseInt(this.dataset.errorIndex);
      const error = errors[errorIndex];
      
      // Find the corresponding error span in the editor
      const editor = document.getElementById('textEditor');
      if (editor) {
        const errorSpans = editor.querySelectorAll('[data-error]');
        errorSpans.forEach(span => {
          const spanError = JSON.parse(span.dataset.error);
          if (spanError.index === error.index && spanError.word === error.word) {
            // Show suggestion popup for this error
            showErrorSuggestion(error, span);
          }
        });
      }
    });
  });
  
  // Update error count
  state.writingStats.errors = errors.length;
  const errorCount = document.getElementById('errorCount');
  if (errorCount) {
    errorCount.textContent = errors.length;
  }
}

/**
 * Get color class for an error type
 * @param {string} type - The error type
 * @returns {string} - The color class
 */
function getErrorColor(type) {
  const colors = {
    spelling: 'bg-red-500',
    grammar: 'bg-blue-500',
    style: 'bg-green-500',
    clarity: 'bg-yellow-500'
  };
  return colors[type] || 'bg-gray-500';
}

/**
 * Check if a word is in the dictionary
 * @param {string} word - The word to check
 * @returns {boolean} - Whether the word is in the dictionary
 */
function isInDictionary(word) {
  // This would connect to a real dictionary API or use a local dictionary
  // For now, just a simple check for common words
  const commonWords = ['the', 'and', 'is', 'in', 'to', 'of', 'that', 'for', 'it', 'with', 'as', 'be', 'on', 'not', 'by', 'at', 'from', 'we', 'this', 'but', 'or', 'an', 'will', 'all', 'have', 'are', 'there', 'been', 'has', 'would', 'more', 'if', 'no', 'when', 'can', 'who', 'which', 'you', 'they', 'their', 'what', 'so', 'up', 'out', 'about', 'into', 'than', 'them', 'then', 'some', 'her', 'him', 'one', 'its', 'time', 'only', 'do', 'may', 'such', 'should', 'now'];
  return commonWords.includes(word.toLowerCase());
}

/**
 * Generate spelling suggestions for a word
 * @param {string} word - The misspelled word
 * @returns {Array} - Array of suggestions
 */
function generateSpellingSuggestions(word) {
  // This would use a real spell checking algorithm
  // For now, just generate some simple variations
  const suggestions = [];
  
  // Add 's' if not present
  if (!word.endsWith('s')) {
    suggestions.push(word + 's');
  }
  
  // Remove 's' if present
  if (word.endsWith('s')) {
    suggestions.push(word.slice(0, -1));
  }
  
  // Add 'ed' if not present
  if (!word.endsWith('ed')) {
    suggestions.push(word + 'ed');
  }
  
  // Add a common prefix
  suggestions.push('re' + word);
  
  return suggestions.slice(0, 3); // Limit to 3 suggestions
}

/**
 * Check grammar rules for a word in context
 * @param {Array} words - Array of words in the text
 * @param {number} index - Index of the current word
 * @returns {boolean} - Whether there's a grammar issue
 */
function checkGrammarRules(words, index) {
  // This would implement real grammar rules
  // For now, just a simple example rule
  
  // Check for repeated words
  if (index > 0 && words[index].toLowerCase() === words[index - 1].toLowerCase()) {
    return true;
  }
  
  // Random grammar issues for demonstration
  return Math.random() < 0.05;
}

/**
 * Generate grammar suggestions for a word in context
 * @param {Array} words - Array of words in the text
 * @param {number} index - Index of the current word
 * @returns {Array} - Array of suggestions
 */
function generateGrammarSuggestions(words, index) {
  // This would implement real grammar suggestion logic
  // For now, just some simple examples
  
  // For repeated words
  if (index > 0 && words[index].toLowerCase() === words[index - 1].toLowerCase()) {
    return ['Remove repeated word'];
  }
  
  // Generic grammar suggestions
  return [
    'Rephrase this sentence',
    'Consider different wording',
    'Check verb agreement'
  ];
}

/**
 * Initialize grammar and spelling functionality
 * Sets up event listeners and CSS
 */
function initializeGrammarAndSpelling() {
  // Set up CSS for error highlighting
  addErrorHighlightingStyles();
  
  // Set up event listeners
  const editor = document.getElementById('textEditor');
  if (editor) {
    editor.addEventListener('input', handleTextInput);
    editor.addEventListener('keydown', handleKeydown);
    editor.addEventListener('mouseup', handleTextSelection);
  }
  
  const checkGrammarBtn = document.getElementById('checkGrammarBtn');
  if (checkGrammarBtn) {
    checkGrammarBtn.addEventListener('click', checkGrammar);
  }
}

/**
 * Add CSS for error highlighting
 */
function addErrorHighlightingStyles() {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    .spelling-error {
      border-bottom: 2px wavy var(--error-color, #dc2626);
      cursor: pointer;
    }
    
    .grammar-error {
      border-bottom: 2px wavy #0284c7;
      cursor: pointer;
    }
    
    .style-suggestion {
      border-bottom: 2px wavy var(--success-color, #16a34a);
      cursor: pointer;
    }
    
    .clarity-improvement {
      border-bottom: 2px wavy var(--warning-color, #d97706);
      cursor: pointer;
    }
    
    .suggestion-popup {
      animation: fadeIn 0.2s ease-in;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(5px); }
      to { opacity: 1; transform: translateY(0); }
    }
  `;
  document.head.appendChild(styleElement);
}
