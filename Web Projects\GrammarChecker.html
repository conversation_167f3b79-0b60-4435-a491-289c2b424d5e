<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinguaFlow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --primary-color: #059669; /* Emerald 600 */
            --secondary-color: #047857; /* Emerald 700 */
            --accent-color: #06b6d4; /* Cyan 500 */
            --error-color: #dc2626; /* Red 600 */
            --warning-color: #f59e0b; /* Amber 500 */
            --success-color: #16a34a; /* Green 600 */
            --info-color: #3b82f6; /* Blue 500 */

            --text-primary: #1f2937; /* Gray 800 */
            --text-secondary: #6b7280; /* Gray 500 */
            --text-accent: var(--primary-color);

            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb; /* Gray 50 */
            --bg-alt: #f3f4f6; /* Gray 100 */
            --border-color: #e5e7eb; /* Gray 200 */

            --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        [data-theme="dark"] {
            --primary-color: #10b981; /* Emerald 500 */
            --secondary-color: #059669; /* Emerald 600 */
            --accent-color: #22d3ee; /* Cyan 400 */

            --text-primary: #f9fafb; /* Gray 50 */
            --text-secondary: #9ca3af; /* Gray 400 */
            --text-accent: var(--primary-color);

            --bg-primary: #111827; /* Gray 900 */
            --bg-secondary: #1f2937; /* Gray 800 */
            --bg-alt: #374151; /* Gray 700 */
            --border-color: #374151; /* Gray 700 */
        }

        /* High Contrast Mode - General structural changes if any */
        /* High Contrast Light Theme Variables */
        .high-contrast-mode[data-theme="light"] {
            --primary-color: #0000FF; /* Blue for links/actions */
            --secondary-color: #0000AA;
            --accent-color: #0000FF;
            --error-color: #FF0000;
            --warning-color: #FFA500; /* Orange */
            --success-color: #008000; /* Green */
            --info-color: #0000FF;

            --text-primary: #000000;
            --text-secondary: #000000; /* Make secondary text also black for HC */
            --text-accent: #0000FF;

            --bg-primary: #FFFFFF;
            --bg-secondary: #F0F0F0; /* Light gray */
            --bg-alt: #E0E0E0; /* Slightly darker gray */
            --border-color: #000000; /* Black borders */
        }
        /* Apply forced colors for HC Light */
        .high-contrast-mode[data-theme="light"] body,
        .high-contrast-mode[data-theme="light"] .modal-content,
        .high-contrast-mode[data-theme="light"] .settings-modal-content {
            color: #000000 !important;
            background-color: #FFFFFF !important;
        }


        /* High Contrast Dark Theme Variables */
        .high-contrast-mode[data-theme="dark"] {
            --primary-color: #FFFF00; /* Yellow for links/actions */
            --secondary-color: #DDDD00;
            --accent-color: #FFFF00;
            --error-color: #FF8888; /* Light red */
            --warning-color: #FFDD88; /* Light orange */
            --success-color: #88FF88; /* Light green */
            --info-color: #88FFFF;   /* Light cyan */

            --text-primary: #FFFFFF;
            --text-secondary: #FFFFFF; /* Make secondary text also white for HC */
            --text-accent: #FFFF00;

            --bg-primary: #000000;
            --bg-secondary: #222222; /* Dark gray */
            --bg-alt: #333333; /* Slightly lighter dark gray */
            --border-color: #FFFFFF; /* White borders */
        }
        /* Apply forced colors for HC Dark */
        .high-contrast-mode[data-theme="dark"] body,
        .high-contrast-mode[data-theme="dark"] .modal-content,
        .high-contrast-mode[data-theme="dark"] .settings-modal-content {
            color: #FFFFFF !important;
            background-color: #000000 !important;
        }


        body {
            font-family: var(--font-sans);
            color: var(--text-primary);
            background-color: var(--bg-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
            font-size: 1rem; /* Base font size, will be adjusted by settings */
        }

        .rtl { direction: rtl; text-align: right; }
        .rtl .mr-2 { margin-left: 0.5rem; margin-right: 0; }
        .rtl .ml-2 { margin-right: 0.5rem; margin-left: 0; }
        .rtl .text-left { text-align: right !important; }
        .rtl .text-right { text-align: left !important; }
        .rtl .space-x-2 > :not([hidden]) ~ :not([hidden]) { margin-right: 0.5rem; margin-left:0;}
        .rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) { margin-right: 1rem; margin-left:0;}


        .grammar-error { border-bottom: 2px solid var(--error-color); cursor: pointer; background-color: color-mix(in srgb, var(--error-color) 10%, transparent); }
        .spelling-error { border-bottom: 2px dashed var(--error-color); cursor: pointer; background-color: color-mix(in srgb, var(--error-color) 10%, transparent); }
        .style-suggestion { border-bottom: 2px dotted var(--success-color); cursor: pointer; background-color: color-mix(in srgb, var(--success-color) 10%, transparent); }
        .clarity-improvement { border-bottom: 2px solid var(--info-color); cursor: pointer; background-color: color-mix(in srgb, var(--info-color) 10%, transparent); }
        .tone-suggestion { border-bottom: 2px dashed var(--warning-color); cursor: pointer; background-color: color-mix(in srgb, var(--warning-color) 10%, transparent); }

        .editor-container { min-height: 400px; max-height: 60vh; overflow-y: auto; background-color: var(--bg-primary); border: 1px solid var(--border-color); border-radius: 0.375rem; }
        #textEditor { padding: 1.5rem; min-height: 380px; outline: none; color: var(--text-primary); line-height: 1.75; }
        #textEditor:empty:before { content: attr(placeholder); color: var(--text-secondary); cursor: text; }

        .settings-panel { max-height: calc(100vh - 200px); overflow-y: auto; }
        .settings-modal-content { background-color: var(--bg-primary); color: var(--text-primary); }
        .settings-nav { background-color: var(--bg-secondary); }

        .tooltip { position: relative; display: inline-block; }
        .tooltip .tooltiptext { visibility: hidden; background-color: var(--bg-secondary); color: var(--text-primary); text-align: center; border-radius: 6px; padding: 5px 10px; position: absolute; z-index: 100; bottom: 125%; left: 50%; margin-left: -60px; opacity: 0; transition: opacity 0.3s; border: 1px solid var(--border-color); box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); font-size: 0.75rem; }
        .tooltip:hover .tooltiptext { visibility: visible; opacity: 1; }

        .font-size-small { font-size: 0.875rem; } /* 14px */
        .font-size-normal { font-size: 1rem; }    /* 16px */
        .font-size-large { font-size: 1.125rem; } /* 18px */

        .writing-stats { background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white; }
        .progress-bar-bg { background-color: color-mix(in srgb, var(--primary-color) 20%, transparent); }
        .progress-bar { background: linear-gradient(90deg, var(--primary-color), var(--accent-color)); height: 6px; border-radius: 3px; transition: width 0.3s ease; }

        .custom-scrollbar::-webkit-scrollbar { width: 8px; }
        .custom-scrollbar::-webkit-scrollbar-track { background: var(--bg-alt); border-radius: 4px;}
        .custom-scrollbar::-webkit-scrollbar-thumb { background: var(--border-color); border-radius: 4px; }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover { background: var(--text-secondary); }

        .settings-section { border-bottom: 1px solid var(--border-color); margin-bottom: 1.5rem; padding-bottom: 1.5rem; }
        .settings-section:last-child { border-bottom: none; margin-bottom: 0; padding-bottom: 0; }

        .toggle-switch { position: relative; display: inline-block; width: 50px; height: 24px; }
        .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: var(--border-color); transition: .4s; border-radius: 24px; }
        .slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .slider { background-color: var(--primary-color); }
        input:focus + .slider { box-shadow: 0 0 1px var(--primary-color); }
        input:checked + .slider:before { transform: translateX(26px); }

        .suggestion-popup { animation: fadeIn 0.2s ease-out; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05); border: 1px solid var(--border-color); background-color: var(--bg-primary); color: var(--text-primary); z-index: 1000; }
        .suggestion-popup button:hover { background-color: var(--bg-secondary); }

        .modal-overlay { background-color: rgba(0,0,0,0.5); }
        .modal-content { background-color: var(--bg-primary); color: var(--text-primary); }

        .btn { padding: 0.5rem 1rem; border-radius: 0.375rem; font-weight: 500; transition: all 0.2s ease; cursor: pointer; border: 1px solid transparent; display: inline-flex; align-items: center; justify-content: center; }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-primary:hover { background-color: var(--secondary-color); }
        .btn-secondary { background-color: var(--bg-secondary); color: var(--text-primary); border-color: var(--border-color); }
        .btn-secondary:hover { background-color: var(--bg-alt); }
        .btn-danger { background-color: var(--error-color); color: white; }
        .btn-danger:hover { background-color: color-mix(in srgb, var(--error-color) 80%, black); }
        .btn-sm { padding: 0.25rem 0.75rem; font-size: 0.875rem; }

        .settings-nav-item.active { background-color: var(--primary-color); color: white !important; }
        .settings-nav-item.active:hover { background-color: var(--secondary-color); }
        .settings-nav-item:hover { background-color: var(--bg-alt); }

        #loadingOverlay { z-index: 9999; }
        .spinner { border-top-color: var(--primary-color); }

        @keyframes fadeIn { from { opacity: 0; transform: translateY(-10px); } to { opacity: 1; transform: translateY(0); } }
        .animate-fadeIn { animation: fadeIn 0.3s ease-out; }
    </style>
</head>
<body class="antialiased">
<div id="app" class="min-h-screen transition-all duration-300">
    <header class="sticky top-0 z-50 shadow-sm" style="background-color: var(--bg-primary); border-bottom: 1px solid var(--border-color);">
        <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-spell-check text-2xl" style="color: var(--primary-color);"></i>
                        <h1 class="text-xl font-bold" style="color: var(--text-primary);">GrammarPro</h1>
                    </div>
                    <div class="hidden md:flex items-center space-x-2">
                        <span class="text-sm" style="color: var(--text-secondary);">Language:</span>
                        <label for="languageSelectorHeader"></label><select id="languageSelectorHeader" class="text-sm border rounded px-2 py-1 focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-secondary); border-color: var(--border-color); color: var(--text-primary);">
                        </select>
                    </div>
                </div>
                <div class="flex items-center space-x-2 sm:space-x-3">
                    <button id="settingsBtn" class="p-2 rounded-lg tooltip" style="color: var(--text-secondary); background-color: var(--bg-alt);">
                        <i class="fas fa-cog text-lg"></i> <span class="tooltiptext">Settings</span>
                    </button>
                    <button id="themeToggleBtn" class="p-2 rounded-lg tooltip" style="color: var(--text-secondary); background-color: var(--bg-alt);">
                        <i class="fas fa-moon text-lg"></i> <span class="tooltiptext">Toggle Theme</span>
                    </button>
                    <button id="helpBtn" class="p-2 rounded-lg tooltip" style="color: var(--text-secondary); background-color: var(--bg-alt);">
                        <i class="fas fa-question-circle text-lg"></i> <span class="tooltiptext">Help</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
            <aside class="lg:col-span-3 space-y-6">
                <div class="rounded-lg shadow p-4 space-y-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-color);">
                    <h3 class="font-semibold flex items-center writing-tools-title" style="color: var(--text-primary);">
                        <i class="fas fa-tools mr-2" style="color: var(--primary-color);"></i> Writing Tools
                    </h3>

                    <div class="space-y-1">
                        <label for="writingMode" class="text-sm font-medium writing-mode-label" style="color: var(--text-secondary);">Writing Mode</label>
                        <select id="writingMode" class="w-full border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-primary); border-color: var(--border-color); color: var(--text-primary);">
                            <option value="casual">📝 Casual</option>
                            <option value="formal" selected>👔 Formal</option>
                            <option value="academic">🎓 Academic</option>
                            <option value="creative">🎨 Creative</option>
                            <option value="business">💼 Business</option>
                        </select>
                    </div>

                    <div class="space-y-1">
                        <label class="text-sm font-medium import-document-label" style="color: var(--text-secondary);">Import Document</label>
                        <div class="border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer hover:border-emerald-400" id="fileDropZone" style="border-color: var(--border-color);">
                            <i class="fas fa-cloud-upload-alt text-3xl mb-2" style="color: var(--text-secondary);"></i>
                            <p class="text-sm" style="color: var(--text-secondary);"><span class="drop-files-text">Drop files here or </span><span class="font-medium browse-text" style="color: var(--primary-color);">browse</span></p>
                            <input type="file" id="fileInput" class="hidden" accept=".txt,.md,.docx">
                        </div>
                    </div>

                    <div class="space-y-2">
                        <label class="text-sm font-medium quick-actions-label" style="color: var(--text-secondary);">Quick Actions</label>
                        <div class="grid grid-cols-2 gap-2">
                            <button id="checkTextBtn" class="btn btn-secondary btn-sm check-all-btn"><i class="fas fa-check mr-1"></i>Check Text</button>
                            <button id="aiRewriteSelectionBtn" class="btn btn-secondary btn-sm ai-rewrite-btn"><i class="fas fa-wand-magic-sparkles mr-1"></i>AI Rewrite ✨</button>
                            <button id="brainstormBtn" class="btn btn-secondary btn-sm"><i class="fas fa-lightbulb mr-1"></i>Brainstorm ✨</button>
                            <button id="exportBtn" class="btn btn-secondary btn-sm export-btn"><i class="fas fa-download mr-1"></i>Export</button>
                            <button id="clearBtn" class="btn btn-secondary btn-sm clear-btn col-span-2"><i class="fas fa-trash mr-1"></i>Clear Editor</button>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <label for="aiPromptInput" class="text-sm font-medium" style="color: var(--text-secondary);">AI Text Generation ✨</label>
                        <textarea id="aiPromptInput" rows="2" class="w-full border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" placeholder="Enter a prompt to generate text..." style="background-color: var(--bg-primary); border-color: var(--border-color); color: var(--text-primary);"></textarea>
                        <button id="generateTextBtn" class="btn btn-primary w-full mt-1 btn-sm"><i class="fas fa-robot mr-1"></i>Generate with AI ✨</button>
                    </div>
                </div>

                <div class="writing-stats rounded-lg p-4 space-y-3 shadow">
                    <h4 class="font-semibold writing-stats-title text-center">Writing Statistics</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between items-center"><span class="words-label">Words:</span> <span id="wordCount" class="font-medium">0</span></div>
                        <div class="flex justify-between items-center"><span class="characters-label">Characters:</span> <span id="charCount" class="font-medium">0</span></div>
                        <div class="flex justify-between items-center"><span class="errors-label">Issues:</span> <span id="errorCount" class="font-medium">0</span></div>
                        <div class="flex justify-between items-center"><span class="score-label">Score:</span> <span id="qualityScore" class="font-medium text-lg">100%</span></div>
                    </div>
                    <div class="mt-2 progress-bar-bg rounded-full h-2.5">
                        <div id="qualityBar" class="progress-bar h-2.5 rounded-full w-full"></div>
                    </div>
                </div>
            </aside>

            <section class="lg:col-span-6">
                <div class="rounded-lg shadow" style="background-color: var(--bg-secondary); border: 1px solid var(--border-color);">
                    <div class="border-b px-4 py-3 flex items-center justify-between" style="border-color: var(--border-color);">
                        <h3 class="font-semibold document-editor-title" style="color: var(--text-primary);">Document Editor</h3>
                        <div class="flex items-center space-x-2">
                            <div class="flex items-center space-x-1 text-sm" style="color: var(--text-secondary);">
                                <i id="statusIcon" class="fas fa-circle text-xs" style="color: var(--success-color);"></i>
                                <span id="statusText" class="ready-status">Ready</span>
                            </div>
                            <button id="autoCorrectionToggle" class="p-1 rounded tooltip" style="color: var(--text-secondary); background-color: var(--bg-alt);">
                                <i class="fas fa-magic text-sm"></i>
                                <span class="tooltiptext auto-correct-tooltip">Auto-correction enabled</span>
                            </button>
                            <button id="undoBtn" class="p-1 rounded tooltip" style="color: var(--text-secondary); background-color: var(--bg-alt);"><i class="fas fa-undo"></i><span class="tooltiptext">Undo (Ctrl+Z)</span></button>
                            <button id="redoBtn" class="p-1 rounded tooltip" style="color: var(--text-secondary); background-color: var(--bg-alt);"><i class="fas fa-redo"></i><span class="tooltiptext">Redo (Ctrl+Y)</span></button>
                        </div>
                    </div>

                    <div class="editor-container custom-scrollbar">
                        <div contenteditable="true" id="textEditor" spellcheck="false" placeholder="Start writing or paste your text here. GrammarPro will help you refine it."></div>
                    </div>

                    <div class="border-t px-4 py-2 flex items-center justify-between text-xs" style="border-color: var(--border-color); background-color: var(--bg-alt); color: var(--text-secondary);">
                        <div class="flex items-center space-x-3">
                            <span>Last saved: <span id="lastSavedTime" class="font-medium">Never</span></span>
                            <span>Language: <span id="currentEditorLang" class="font-medium">English</span></span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span id="selectionInfo"></span>
                            <span id="cursorPosition">Line 1, Col 1</span>
                        </div>
                    </div>
                </div>
                <div id="aiContextPanel" class="mt-4 rounded-lg shadow p-4 animate-fadeIn hidden" style="background-color: var(--bg-secondary); border:1px solid var(--border-color)">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold flex items-center" style="color: var(--primary-color);"><i class="fas fa-wand-magic-sparkles mr-2"></i>AI Actions for Selection ✨</h4>
                        <button id="closeAiContextPanel" class="p-1 rounded" style="color: var(--text-secondary); background-color: var(--bg-alt);"><i class="fas fa-times"></i></button>
                    </div>
                    <p class="text-sm italic mb-2 p-2 rounded" id="selectedTextPreview" style="color: var(--text-secondary); background-color: var(--bg-alt);"></p>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="rewrite-formal">Formalize</button>
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="rewrite-casual">Casualize</button>
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="rewrite-shorter">Shorten</button>
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="rewrite-longer">Lengthen</button>
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="summarize">Summarize</button>
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="explain">Explain</button>
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="enhance-fluency">Enhance Fluency ✨</button>
                        <button class="btn btn-secondary btn-sm ai-action-btn" data-action="adjust-tone-selection">Adjust Tone ✨</button>
                    </div>
                    <div id="aiContextOutput" class="mt-3 text-sm p-2 rounded hidden whitespace-pre-wrap" style="background-color: var(--bg-alt); border:1px solid var(--border-color); max-height: 200px; overflow-y: auto;"></div>
                </div>
            </section>

            <aside class="lg:col-span-3 space-y-6">
                <div class="rounded-lg shadow p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-color);">
                    <h3 class="font-semibold mb-3 flex items-center suggestions-title" style="color: var(--text-primary);">
                        <i class="fas fa-lightbulb mr-2" style="color: var(--warning-color);"></i> Suggestions
                    </h3>
                    <div id="suggestionsList" class="space-y-3 max-h-60 overflow-y-auto custom-scrollbar">
                        <div class="text-sm text-center py-8" style="color: var(--text-secondary);">
                            <i class="fas fa-search text-3xl mb-2"></i>
                            <p class="start-typing-text">Start typing or check text to see suggestions.</p>
                        </div>
                    </div>
                </div>

                <div class="rounded-lg shadow p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-color);">
                    <h3 class="font-semibold mb-3 flex items-center tone-analysis-title" style="color: var(--text-primary);">
                        <i class="fas fa-comments mr-2" style="color: var(--info-color);"></i> Tone Analysis
                    </h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between"><span>Overall Tone:</span> <span id="toneOverall" class="font-medium" style="color: var(--text-accent);">Neutral</span></div>
                        <div class="flex justify-between"><span>Formality:</span> <span id="toneFormality" class="font-medium">Professional (75%)</span></div>
                        <div class="flex justify-between"><span>Confidence:</span> <span id="toneConfidence" class="font-medium">Confident (80%)</span></div>
                        <button id="adjustToneBtn" class="btn btn-secondary w-full mt-2 btn-sm adjust-tone-btn"><i class="fas fa-sliders-h mr-1"></i>Adjust Tone ✨</button>
                    </div>
                </div>

                <div class="rounded-lg shadow p-4" style="background-color: var(--bg-secondary); border: 1px solid var(--border-color);">
                    <h3 class="font-semibold mb-3 flex items-center recent-prompts-title" style="color: var(--text-primary);">
                        <i class="fas fa-history mr-2" style="color: var(--primary-color);"></i> Recent AI Prompts
                    </h3>
                    <div id="recentPromptsList" class="space-y-2 text-xs max-h-40 overflow-y-auto custom-scrollbar">
                        <p class="text-center py-2" style="color: var(--text-secondary);">No recent prompts.</p>
                    </div>
                </div>
            </aside>
        </div>
    </main>

    <div id="settingsModal" class="fixed inset-0 modal-overlay z-[100] hidden items-center justify-center p-4 animate-fadeIn">
        <div class="settings-modal-content rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div class="flex items-center justify-between p-5 border-b" style="border-color: var(--border-color);">
                <h2 class="text-xl font-semibold">Settings</h2>
                <div class="flex items-center">
                    <label for="settingsSearchInput"></label><input type="search" id="settingsSearchInput" placeholder="Search settings..." class="text-sm border rounded px-2 py-1 mr-4 focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-secondary); border-color: var(--border-color); color: var(--text-primary);">
                    <button id="closeSettingsBtn" class="p-1 rounded" style="color: var(--text-secondary); background-color: var(--bg-alt);"><i class="fas fa-times text-xl"></i></button>
                </div>
            </div>

            <div class="flex flex-1 overflow-hidden">
                <nav class="w-1/4 settings-nav p-4 border-r space-y-1 custom-scrollbar overflow-y-auto" style="border-color: var(--border-color);">
                    <button class="settings-nav-item w-full text-left px-3 py-2 rounded flex items-center" data-section="language"><i class="fas fa-language fa-fw mr-2"></i>Language</button>
                    <button class="settings-nav-item w-full text-left px-3 py-2 rounded flex items-center" data-section="features"><i class="fas fa-cogs fa-fw mr-2"></i>Features</button>
                    <button class="settings-nav-item w-full text-left px-3 py-2 rounded flex items-center" data-section="dictionary"><i class="fas fa-book fa-fw mr-2"></i>Dictionary</button>
                    <button class="settings-nav-item w-full text-left px-3 py-2 rounded flex items-center" data-section="appearance"><i class="fas fa-palette fa-fw mr-2"></i>Appearance</button>
                    <button class="settings-nav-item w-full text-left px-3 py-2 rounded flex items-center" data-section="assistance"><i class="fas fa-user-graduate fa-fw mr-2"></i>Writing Aid</button>
                    <button class="settings-nav-item w-full text-left px-3 py-2 rounded flex items-center" data-section="advanced"><i class="fas fa-sliders-h fa-fw mr-2"></i>Advanced</button>
                </nav>

                <div class="flex-1 p-6 settings-panel custom-scrollbar">
                    <div id="settings-section-language" class="settings-section-content space-y-6">
                        <h3 class="text-lg font-medium mb-1">Language Settings</h3>
                        <p class="text-sm text-gray-500 mb-4">Configure your preferred languages and dialects.</p>
                        <div>
                            <label for="primaryLanguage" class="block text-sm font-medium mb-1">Primary Writing Language</label>
                            <select id="primaryLanguage" class="w-full border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-secondary); border-color: var(--border-color); color: var(--text-primary);"></select>
                            <p class="text-xs mt-1" style="color: var(--text-secondary);">Default language for checks and suggestions.</p>
                        </div>
                        <div>
                            <label for="dialectSelect" class="block text-sm font-medium mb-1">Regional Dialect</label>
                            <select id="dialectSelect" class="w-full border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-secondary); border-color: var(--border-color); color: var(--text-primary);"></select>
                            <p class="text-xs mt-1" style="color: var(--text-secondary);">Specify regional variations (e.g., US vs UK English).</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium">Auto-detect UI Language</label>
                                <p class="text-xs" style="color: var(--text-secondary);">Set UI based on browser/system settings.</p>
                            </div>
                            <label class="toggle-switch"><input type="checkbox" id="autoDetectUILanguage" checked><span class="slider"></span></label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium">Enable RTL Layout for Arabic</label>
                                <p class="text-xs" style="color: var(--text-secondary);">Automatically switch to Right-to-Left for Arabic.</p>
                            </div>
                            <label class="toggle-switch"><input type="checkbox" id="rtlSupportArabic" checked><span class="slider"></span></label>
                        </div>
                    </div>

                    <div id="settings-section-features" class="settings-section-content space-y-6 hidden">
                        <h3 class="text-lg font-medium mb-1">Feature Customization</h3>
                        <p class="text-sm text-gray-500 mb-4">Enable or disable specific writing assistance features.</p>
                        <h4 class="text-md font-medium">Generative AI Features ✨</h4>
                        <div class="space-y-3 pl-4 border-l-2" style="border-color: var(--primary-color);">
                            <div class="flex items-center justify-between">
                                <div><label class="text-sm font-medium">Show AI suggestions on text selection</label><p class="text-xs" style="color: var(--text-secondary);">Quick AI actions for selected text.</p></div>
                                <label class="toggle-switch"><input type="checkbox" id="aiOnSelection" checked><span class="slider"></span></label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div><label class="text-sm font-medium">AI-based quick reply suggestions</label><p class="text-xs" style="color: var(--text-secondary);">Get ideas for replies (mocked).</p></div>
                                <label class="toggle-switch"><input type="checkbox" id="aiQuickReplies" checked><span class="slider"></span></label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div><label class="text-sm font-medium">View and reuse recent prompt history</label><p class="text-xs" style="color: var(--text-secondary);">Access your recent AI prompts.</p></div>
                                <label class="toggle-switch"><input type="checkbox" id="aiPromptHistory" checked><span class="slider"></span></label>
                            </div>
                        </div>
                        <h4 class="text-md font-medium mt-4">Auto-Correction Features ✨</h4>
                        <div class="space-y-3 pl-4 border-l-2" style="border-color: var(--success-color);">
                            <div class="flex items-center justify-between">
                                <div><label class="text-sm font-medium">Automatic Text Correction</label><p class="text-xs" style="color: var(--text-secondary);">Automatically fix common typos and grammar errors.</p></div>
                                <label class="toggle-switch"><input type="checkbox" id="autoCorrection" checked><span class="slider"></span></label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div><label class="text-sm font-medium">Real-time Correction</label><p class="text-xs" style="color: var(--text-secondary);">Apply corrections as you type.</p></div>
                                <label class="toggle-switch"><input type="checkbox" id="realTimeCorrection" checked><span class="slider"></span></label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div><label class="text-sm font-medium">Sentence Enhancement</label><p class="text-xs" style="color: var(--text-secondary);">Suggest improvements for clarity and style.</p></div>
                                <label class="toggle-switch"><input type="checkbox" id="sentenceEnhancement" checked><span class="slider"></span></label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div><label class="text-sm font-medium">Show Correction Feedback</label><p class="text-xs" style="color: var(--text-secondary);">Display notifications when corrections are applied.</p></div>
                                <label class="toggle-switch"><input type="checkbox" id="showCorrectionFeedback" checked><span class="slider"></span></label>
                            </div>
                        </div>
                        <h4 class="text-md font-medium mt-4">Core Checking Features</h4>
                        <div class="space-y-3 pl-4 border-l-2" style="border-color: var(--primary-color);">
                            <div class="flex items-center justify-between"><label class="text-sm font-medium">Real-time Grammar Checking</label><label class="toggle-switch"><input type="checkbox" id="grammarCheck" checked><span class="slider"></span></label></div>
                            <div class="flex items-center justify-between"><label class="text-sm font-medium">Real-time Spell Checking</label><label class="toggle-switch"><input type="checkbox" id="spellCheck" checked><span class="slider"></span></label></div>
                            <div class="flex items-center justify-between"><label class="text-sm font-medium">Style Suggestions</label><label class="toggle-switch"><input type="checkbox" id="styleCheck" checked><span class="slider"></span></label></div>
                        </div>
                    </div>

                    <div id="settings-section-dictionary" class="settings-section-content space-y-6 hidden">
                        <h3 class="text-lg font-medium mb-1">Personal Dictionary</h3>
                        <p class="text-sm text-gray-500 mb-4">Manage words that GrammarPro should ignore.</p>
                        <div>
                            <label for="newWordInput" class="block text-sm font-medium mb-1">Add Word to Dictionary</label>
                            <div class="flex space-x-2">
                                <input type="text" id="newWordInput" placeholder="Enter word..." class="flex-1 border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-primary); border-color: var(--border-color); color: var(--text-primary);">
                                <button id="addWordBtn" class="btn btn-primary btn-sm"><i class="fas fa-plus mr-1"></i>Add</button>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <label class="text-sm font-medium">Your Dictionary Words (<span id="dictionaryCount">0</span>)</label>
                                <div class="space-x-2">
                                    <button id="importDictBtn" class="btn btn-secondary btn-sm"><i class="fas fa-upload mr-1"></i>Import</button>
                                    <button id="exportDictBtn" class="btn btn-secondary btn-sm"><i class="fas fa-download mr-1"></i>Export</button>
                                </div>
                            </div>
                            <div id="dictionaryListContainer" class="max-h-48 overflow-y-auto border rounded p-3 custom-scrollbar" style="background-color: var(--bg-alt); border-color: var(--border-color);">
                                <p class="text-center text-sm" style="color: var(--text-secondary);">Dictionary is empty.</p>
                            </div>
                        </div>
                        <div>
                            <label for="dictionaryLanguageSelect" class="block text-sm font-medium mb-1">Language for Dictionary View</label>
                            <select id="dictionaryLanguageSelect" class="w-full border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-secondary); border-color: var(--border-color); color: var(--text-primary);"></select>
                            <p class="text-xs mt-1" style="color: var(--text-secondary);">View dictionary entries for a specific language.</p>
                        </div>
                    </div>

                    <div id="settings-section-appearance" class="settings-section-content space-y-6 hidden">
                        <h3 class="text-lg font-medium mb-1">Appearance</h3>
                        <p class="text-sm text-gray-500 mb-4">Customize the look and feel of GrammarPro.</p>
                        <div>
                            <label class="block text-sm font-medium mb-2">Theme</label>
                            <div class="grid grid-cols-3 gap-3">
                                <label class="flex items-center justify-center p-3 border rounded cursor-pointer hover:border-emerald-500 transition-colors" style="border-color: var(--border-color);"><input type="radio" name="theme" value="light" class="mr-2 sr-only"><i class="fas fa-sun text-yellow-500 text-xl mr-2"></i> Light</label>
                                <label class="flex items-center justify-center p-3 border rounded cursor-pointer hover:border-emerald-500 transition-colors" style="border-color: var(--border-color);"><input type="radio" name="theme" value="dark" class="mr-2 sr-only"><i class="fas fa-moon text-blue-400 text-xl mr-2"></i> Dark</label>
                                <label class="flex items-center justify-center p-3 border rounded cursor-pointer hover:border-emerald-500 transition-colors" style="border-color: var(--border-color);"><input type="radio" name="theme" value="system" class="mr-2 sr-only"><i class="fas fa-desktop text-gray-500 text-xl mr-2"></i> System</label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Editor Font Size</label>
                            <div class="flex items-center space-x-2">
                                <button class="btn btn-secondary font-size-btn" data-size="small">Aa</button>
                                <button class="btn btn-secondary font-size-btn active" data-size="normal">Aa</button>
                                <button class="btn btn-secondary font-size-btn" data-size="large">Aa</button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium">High Contrast Mode</label>
                                <p class="text-xs" style="color: var(--text-secondary);">Increases text visibility.</p>
                            </div>
                            <label class="toggle-switch"><input type="checkbox" id="highContrastMode"><span class="slider"></span></label>
                        </div>
                    </div>

                    <div id="settings-section-assistance" class="settings-section-content space-y-6 hidden">
                        <h3 class="text-lg font-medium mb-1">Writing Assistance</h3>
                        <p class="text-sm text-gray-500 mb-4">Fine-tune how GrammarPro assists your writing.</p>
                        <div>
                            <label for="languageProficiency" class="block text-sm font-medium mb-1">Your Language Proficiency (for Primary Language)</label>
                            <select id="languageProficiency" class="w-full border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-secondary); border-color: var(--border-color); color: var(--text-primary);">
                                <option value="native">Native Speaker</option>
                                <option value="advanced" selected>Advanced (C1-C2)</option>
                                <option value="intermediate">Intermediate (B1-B2)</option>
                                <option value="beginner">Beginner (A1-A2)</option>
                            </select>
                            <p class="text-xs mt-1" style="color: var(--text-secondary);">Helps tailor suggestions to your needs (conceptual).</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium">Tone Detection</label>
                                <p class="text-xs" style="color: var(--text-secondary);">Analyze and suggest tone adjustments.</p>
                            </div>
                            <label class="toggle-switch"><input type="checkbox" id="toneDetection" checked><span class="slider"></span></label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium">Plagiarism Detection</label>
                                <p class="text-xs" style="color: var(--text-secondary);">Check for unintentional content overlap (mocked).</p>
                            </div>
                            <label class="toggle-switch"><input type="checkbox" id="plagiarismCheck"><span class="slider"></span></label>
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium">Assistance for Non-native Speakers</label>
                                <p class="text-xs" style="color: var(--text-secondary);">Provide extra guidance for fluency.</p>
                            </div>
                            <label class="toggle-switch"><input type="checkbox" id="nonNativeAssistance" checked><span class="slider"></span></label>
                        </div>
                    </div>

                    <div id="settings-section-advanced" class="settings-section-content space-y-6 hidden">
                        <h3 class="text-lg font-medium mb-1">Advanced</h3>
                        <p class="text-sm text-gray-500 mb-4">Configure advanced operational parameters.</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-sm font-medium">Enable Offline Functionality</label>
                                <p class="text-xs" style="color: var(--text-secondary);">Basic features work offline (settings, dictionary).</p>
                            </div>
                            <label class="toggle-switch"><input type="checkbox" id="offlineMode" checked><span class="slider"></span></label>
                        </div>
                        <div>
                            <label for="realTimeCheckDelay" class="block text-sm font-medium mb-1">Real-time Check Delay (ms)</label>
                            <input type="range" id="realTimeCheckDelay" min="300" max="2000" value="700" class="w-full">
                            <div class="flex justify-between text-xs" style="color: var(--text-secondary);"><span>Fast (300ms)</span> <span id="realTimeCheckDelayValue">700ms</span> <span>Slow (2000ms)</span></div>
                        </div>
                        <div class="mt-6">
                            <button id="resetSettingsBtn" class="btn btn-danger w-full"><i class="fas fa-undo mr-2"></i>Reset All Settings to Default</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-5 border-t flex justify-end space-x-3" style="border-color: var(--border-color); background-color: var(--bg-alt);">
                <button id="cancelSettingsBtn" class="btn btn-secondary">Cancel</button>
                <button id="saveSettingsBtn" class="btn btn-primary"><i class="fas fa-save mr-2"></i>Save Changes</button>
            </div>
        </div>
    </div>

    <div id="helpModal" class="fixed inset-0 modal-overlay z-[100] hidden items-center justify-center p-4 animate-fadeIn">
        <div class="modal-content rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col">
            <div class="flex items-center justify-between p-5 border-b" style="border-color: var(--border-color);">
                <h2 class="text-xl font-semibold">Help & Onboarding</h2>
                <button id="closeHelpBtn" class="p-1 rounded" style="color: var(--text-secondary); background-color: var(--bg-alt);"><i class="fas fa-times text-xl"></i></button>
            </div>
            <div class="p-6 space-y-6 overflow-y-auto custom-scrollbar">
                <div>
                    <h3 class="text-lg font-medium mb-2">Welcome to GrammarPro!</h3>
                    <p class="text-sm" style="color: var(--text-secondary);">GrammarPro is your advanced writing assistant. Here's how to get started:</p>
                    <ol class="list-decimal list-inside space-y-1 mt-2 text-sm pl-4">
                        <li>Select your primary writing language from the header or settings.</li>
                        <li>Choose a writing mode (e.g., Formal, Casual) from the left panel.</li>
                        <li>Type or paste your text into the editor.</li>
                        <li>GrammarPro will automatically highlight issues and offer suggestions.</li>
                        <li>Click on highlighted text to see details and apply corrections.</li>
                        <li>Use the AI tools for rewriting, generating text, and more.</li>
                        <li>Customize your experience in the <i class="fas fa-cog"></i> Settings menu.</li>
                    </ol>
                </div>
                <div>
                    <h3 class="text-lg font-medium mb-2">Suggestion Types</h3>
                    <ul class="space-y-1 text-sm">
                        <li><span class="inline-block w-3 h-3 mr-2 rounded-full" style="background-color: var(--error-color);"></span> Spelling & Critical Grammar Errors</li>
                        <li><span class="inline-block w-3 h-3 mr-2 rounded-full" style="background-color: var(--success-color);"></span> Style & Fluency Suggestions</li>
                        <li><span class="inline-block w-3 h-3 mr-2 rounded-full" style="background-color: var(--info-color);"></span> Clarity Improvements</li>
                        <li><span class="inline-block w-3 h-3 mr-2 rounded-full" style="background-color: var(--warning-color);"></span> Tone Suggestions</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-medium mb-2">Keyboard Shortcuts</h3>
                    <ul class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1 text-sm">
                        <li><kbd class="px-2 py-1 rounded text-xs" style="background-color: var(--bg-alt); border: 1px solid var(--border-color);">Ctrl/Cmd + Space</kbd>: Check Text</li>
                        <li><kbd class="px-2 py-1 rounded text-xs" style="background-color: var(--bg-alt); border: 1px solid var(--border-color);">Ctrl/Cmd + Shift + A</kbd>: AI Rewrite Selection</li>
                        <li><kbd class="px-2 py-1 rounded text-xs" style="background-color: var(--bg-alt); border: 1px solid var(--border-color);">Ctrl/Cmd + ,</kbd>: Open Settings</li>
                        <li><kbd class="px-2 py-1 rounded text-xs" style="background-color: var(--bg-alt); border: 1px solid var(--border-color);">Ctrl/Cmd + S</kbd>: Save (Mock - Auto Saves)</li>
                        <li><kbd class="px-2 py-1 rounded text-xs" style="background-color: var(--bg-alt); border: 1px solid var(--border-color);">Ctrl/Cmd + Z</kbd>: Undo</li>
                        <li><kbd class="px-2 py-1 rounded text-xs" style="background-color: var(--bg-alt); border: 1px solid var(--border-color);">Ctrl/Cmd + Y</kbd>: Redo</li>
                    </ul>
                </div>
            </div>
            <div class="p-4 border-t text-right" style="border-color: var(--border-color); background-color: var(--bg-alt);">
                <button id="gotItHelpBtn" class="btn btn-primary">Got it!</button>
            </div>
        </div>
    </div>
    <div id="confirmModal" class="fixed inset-0 modal-overlay z-[200] hidden items-center justify-center p-4 animate-fadeIn">
        <div class="modal-content rounded-lg shadow-xl w-full max-w-md">
            <div class="p-5">
                <h3 id="confirmTitle" class="text-lg font-semibold mb-2">Confirm Action</h3>
                <p id="confirmMessage" class="text-sm mb-4" style="color: var(--text-secondary);">Are you sure?</p>
            </div>
            <div class="px-5 py-3 border-t flex justify-end space-x-3" style="border-color: var(--border-color); background-color: var(--bg-alt);">
                <button id="confirmCancelBtn" class="btn btn-secondary">Cancel</button>
                <button id="confirmOkBtn" class="btn btn-danger">Confirm</button>
            </div>
        </div>
    </div>
    <div id="customPromptModal" class="fixed inset-0 modal-overlay z-[200] hidden items-center justify-center p-4 animate-fadeIn">
        <div class="modal-content rounded-lg shadow-xl w-full max-w-md">
            <div class="p-5">
                <h3 id="customPromptTitle" class="text-lg font-semibold mb-2">Enter Details</h3>
                <p id="customPromptMessage" class="text-sm mb-2" style="color: var(--text-secondary);"></p>
                <label for="customPromptInputModal"></label><input type="text" id="customPromptInputModal" class="w-full border rounded px-3 py-2 text-sm focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500" style="background-color: var(--bg-primary); border-color: var(--border-color); color: var(--text-primary);">
            </div>
            <div class="px-5 py-3 border-t flex justify-end space-x-3" style="border-color: var(--border-color); background-color: var(--bg-alt);">
                <button id="customPromptCancelBtn" class="btn btn-secondary">Cancel</button>
                <button id="customPromptOkBtn" class="btn btn-primary">Submit</button>
            </div>
        </div>
    </div>


    <div id="loadingOverlay" class="fixed inset-0 modal-overlay hidden items-center justify-center">
        <div class="p-5 rounded-lg shadow-xl flex items-center space-x-3" style="background-color: var(--bg-secondary);">
            <div class="spinner w-6 h-6 border-2 border-transparent rounded-full animate-spin"></div>
            <span id="loadingMessage" style="color: var(--text-primary);">Processing...</span>
        </div>
    </div>

</div>

<script type="module">
    // Firebase and Gemini API related imports could go here if using modules for them.
    // For this example, Gemini API will be called via fetch directly.

    const LinguaFlow = {
        // --- STATE ---
        state: {
            currentLanguage: 'en',
            currentDialect: 'en-US',
            theme: 'system', // 'light', 'dark', 'system'
            fontSize: 'normal', // 'small', 'normal', 'large'
            isRTL: false,
            personalDictionary: {
                // 'en': ['Grammarly', 'frontend'], 'es': ['hola']
            },
            recentPrompts: [],
            settings: {
                autoDetectUILanguage: true,
                rtlSupportArabic: true,
                aiOnSelection: true,
                aiQuickReplies: true,
                aiPromptHistory: true,
                grammarCheck: true,
                spellCheck: true,
                styleCheck: true,
                toneDetection: true,
                plagiarismCheck: false,
                nonNativeAssistance: true,
                offlineMode: true,
                highContrastMode: false,
                realTimeCheckDelay: 700,
                languageProficiency: 'advanced',
                autoCorrection: true,
                sentenceEnhancement: true,
                realTimeCorrection: true,
                showCorrectionFeedback: true,
            },
            editorHistory: {
                undoStack: [],
                redoStack: [],
                maxHistory: 50,
            },
            lastSaved: null,
            currentSelection: null, // To store the current text selection range
            currentAIToneActionCallback: null, // For adjust tone
        },

        // --- CONFIGURATION ---
        config: {
            languages: {
                'en': { name: 'English', flag: '🇺🇸', rtl: false, dialects: { 'en-US': 'American English', 'en-GB': 'British English', 'en-AU': 'Australian English', 'en-CA': 'Canadian English' } },
                'ar': { name: 'Arabic (العربية)', flag: '🇸🇦', rtl: true, dialects: { 'ar-SA': 'Saudi Arabia', 'ar-EG': 'Egypt', 'ar-SY': 'Syria' } },
                'tr': { name: 'Turkish (Türkçe)', flag: '🇹🇷', rtl: false, dialects: { 'tr-TR': 'Turkey' } },
                'es': { name: 'Spanish (Español)', flag: '🇪🇸', rtl: false, dialects: { 'es-ES': 'Spain', 'es-MX': 'Mexico', 'es-AR': 'Argentina' } },
                'de': { name: 'German (Deutsch)', flag: '🇩🇪', rtl: false, dialects: { 'de-DE': 'Germany' } },
                'fr': { name: 'French (Français)', flag: '🇫🇷', rtl: false, dialects: { 'fr-FR': 'France', 'fr-CA': 'Canada' } },
                'nl': { name: 'Dutch (Nederlands)', flag: '🇳🇱', rtl: false, dialects: { 'nl-NL': 'Netherlands' } },
                'it': { name: 'Italian (Italiano)', flag: '🇮🇹', rtl: false, dialects: { 'it-IT': 'Italy' } },
            },
            defaultSettings: null, // To be populated on init
            maxRecentPrompts: 5,
            mockErrorProbability: 0.15, // Chance to find a mock error
            // UI text translations
            translations: {
                'en': {
                    'writing-tools-title': 'Write Tools',
                    'import-document-label': 'Upload File',
                    'drop-files-text': 'Drop files here or',
                    'browse-text': 'browse',
                    'quick-actions-label': 'Quick Actions',
                    'check-all-btn': 'Check Text',
                    'ai-rewrite-btn': 'AI Rewrite ✨',
                    'export-btn': 'Export',
                    'clear-btn': 'Clear Editor',
                    'writing-stats-title': 'Writing Statistics',
                    'words-label': 'Words:',
                    'characters-label': 'Characters:',
                    'errors-label': 'Issues:',
                    'score-label': 'Score:',
                    'document-editor-title': 'Document Editor',
                    'ready-status': 'Ready',
                    'suggestions-title': 'Suggestions',
                    'start-typing-text': 'Start typing or check text to see suggestions.',
                    'tone-analysis-title': 'Tone Analysis',
                    'adjust-tone-btn': 'Adjust Tone ✨',
                    'recent-prompts-title': 'Recent AI Prompts',
                    'writing-modes': ['📝 Casual', '👔 Formal', '🎓 Academic', '🎨 Creative', '💼 Business'],
                    'settings-tooltip': 'Settings',
                    'theme-tooltip': 'Toggle Theme',
                    'help-tooltip': 'Help',
                    'undo-tooltip': 'Undo (Ctrl+Z)',
                    'redo-tooltip': 'Redo (Ctrl+Y)',
                    'language-label': 'Language:',
                    'auto-correct-enabled': 'Auto-correction enabled',
                    'auto-correct-disabled': 'Auto-correction disabled',
                    'text-corrected': 'Text automatically corrected',
                    'sentence-enhanced': 'Sentence enhanced for clarity',
                    'correction-applied': 'Correction applied',
                    'enhancement-applied': 'Enhancement applied',
                    'language-changed': 'Language changed successfully',
                    'ui-updated': 'Interface updated',
                    'placeholder-text': 'Start writing or paste your text here. GrammarPro will help you refine it.',
                    'editor-placeholder': 'Start writing in English...',
                    'settings-title': 'Settings',
                    'help-title': 'Help & Onboarding',
                    'confirm-title': 'Confirm Action',
                    'cancel-btn': 'Cancel',
                    'save-btn': 'Save Changes',
                    'confirm-btn': 'Confirm',
                    'got-it-btn': 'Got it!',
                    'close-btn': 'Close'
                },
                'es': {
                    'writing-tools-title': 'Herramientas de Escritura',
                    'import-document-label': 'Subir Archivo',
                    'drop-files-text': 'Suelta archivos aquí o',
                    'browse-text': 'navega',
                    'quick-actions-label': 'Acciones Rápidas',
                    'check-all-btn': 'Revisar Texto',
                    'ai-rewrite-btn': 'Reescribir con IA ✨',
                    'export-btn': 'Exportar',
                    'clear-btn': 'Limpiar Editor',
                    'writing-stats-title': 'Estadísticas de Escritura',
                    'words-label': 'Palabras:',
                    'characters-label': 'Caracteres:',
                    'errors-label': 'Problemas:',
                    'score-label': 'Puntaje:',
                    'document-editor-title': 'Editor de Documentos',
                    'ready-status': 'Listo',
                    'suggestions-title': 'Sugerencias',
                    'start-typing-text': 'Comienza a escribir o revisa el texto para ver sugerencias.',
                    'tone-analysis-title': 'Análisis de Tono',
                    'adjust-tone-btn': 'Ajustar Tono ✨',
                    'recent-prompts-title': 'Consultas Recientes de IA',
                    'writing-modes': ['📝 Casual', '👔 Formal', '🎓 Académico', '🎨 Creativo', '💼 Negocios'],
                    'settings-tooltip': 'Configuración',
                    'theme-tooltip': 'Cambiar Tema',
                    'help-tooltip': 'Ayuda',
                    'undo-tooltip': 'Deshacer (Ctrl+Z)',
                    'redo-tooltip': 'Rehacer (Ctrl+Y)',
                    'language-label': 'Idioma:',
                    'auto-correct-enabled': 'Corrección automática activada',
                    'auto-correct-disabled': 'Corrección automática desactivada',
                    'text-corrected': 'Texto corregido automáticamente',
                    'sentence-enhanced': 'Oración mejorada para mayor claridad',
                    'correction-applied': 'Corrección aplicada',
                    'enhancement-applied': 'Mejora aplicada',
                    'language-changed': 'Idioma cambiado exitosamente',
                    'ui-updated': 'Interfaz actualizada',
                    'placeholder-text': 'Comienza a escribir o pega tu texto aquí. GrammarPro te ayudará a refinarlo.',
                    'editor-placeholder': 'Comienza a escribir en español...',
                    'settings-title': 'Configuración',
                    'help-title': 'Ayuda y Orientación',
                    'confirm-title': 'Confirmar Acción',
                    'cancel-btn': 'Cancelar',
                    'save-btn': 'Guardar Cambios',
                    'confirm-btn': 'Confirmar',
                    'got-it-btn': '¡Entendido!',
                    'close-btn': 'Cerrar'
                },
                'de': {
                    'writing-tools-title': 'Schreibwerkzeuge',
                    'writing-mode-label': 'Schreibmodus',
                    'import-document-label': 'Dokument Importieren',
                    'drop-files-text': 'Dateien hier ablegen oder',
                    'browse-text': 'durchsuchen',
                    'quick-actions-label': 'Schnellaktionen',
                    'check-all-btn': 'Text Prüfen',
                    'ai-rewrite-btn': 'KI-Umschreiben ✨',
                    'export-btn': 'Exportieren',
                    'clear-btn': 'Editor Leeren',
                    'writing-stats-title': 'Schreibstatistiken',
                    'words-label': 'Wörter:',
                    'characters-label': 'Zeichen:',
                    'errors-label': 'Probleme:',
                    'score-label': 'Punktzahl:',
                    'document-editor-title': 'Dokumenteneditor',
                    'ready-status': 'Bereit',
                    'suggestions-title': 'Vorschläge',
                    'start-typing-text': 'Beginnen Sie zu tippen oder prüfen Sie den Text, um Vorschläge zu sehen.',
                    'tone-analysis-title': 'Tonanalyse',
                    'adjust-tone-btn': 'Ton Anpassen ✨',
                    'recent-prompts-title': 'Aktuelle KI-Abfragen',
                    'writing-modes': ['📝 Informell', '👔 Formell', '🎓 Akademisch', '🎨 Kreativ', '💼 Geschäftlich'],
                    'settings-tooltip': 'Einstellungen',
                    'theme-tooltip': 'Thema Wechseln',
                    'help-tooltip': 'Hilfe',
                    'undo-tooltip': 'Rückgängig (Strg+Z)',
                    'redo-tooltip': 'Wiederholen (Strg+Y)',
                    'language-label': 'Sprache:',
                    'auto-correct-enabled': 'Autokorrektur aktiviert',
                    'auto-correct-disabled': 'Autokorrektur deaktiviert',
                    'text-corrected': 'Text automatisch korrigiert',
                    'sentence-enhanced': 'Satz für bessere Klarheit verbessert',
                    'correction-applied': 'Korrektur angewendet',
                    'enhancement-applied': 'Verbesserung angewendet',
                    'language-changed': 'Sprache erfolgreich geändert',
                    'ui-updated': 'Benutzeroberfläche aktualisiert',
                    'placeholder-text': 'Beginnen Sie zu schreiben oder fügen Sie Ihren Text hier ein. GrammarPro wird Ihnen helfen, ihn zu verfeinern.',
                    'editor-placeholder': 'Beginnen Sie auf Deutsch zu schreiben...',
                    'settings-title': 'Einstellungen',
                    'help-title': 'Hilfe & Einführung',
                    'confirm-title': 'Aktion Bestätigen',
                    'cancel-btn': 'Abbrechen',
                    'save-btn': 'Änderungen Speichern',
                    'confirm-btn': 'Bestätigen',
                    'got-it-btn': 'Verstanden!',
                    'close-btn': 'Schließen'
                },
                'ar': {
                    'writing-tools-title': 'أدوات الكتابة',
                    'writing-mode-label': 'وضع الكتابة',
                    'import-document-label': 'استيراد المستند',
                    'drop-files-text': 'أسقط الملفات هنا أو',
                    'browse-text': 'تصفح',
                    'quick-actions-label': 'إجراءات سريعة',
                    'check-all-btn': 'فحص النص',
                    'ai-rewrite-btn': 'إعادة كتابة بالذكاء الاصطناعي ✨',
                    'export-btn': 'تصدير',
                    'clear-btn': 'مسح المحرر',
                    'writing-stats-title': 'إحصائيات الكتابة',
                    'words-label': 'كلمات:',
                    'characters-label': 'أحرف:',
                    'errors-label': 'مشكلات:',
                    'score-label': 'النتيجة:',
                    'document-editor-title': 'محرر المستندات',
                    'ready-status': 'جاهز',
                    'suggestions-title': 'اقتراحات',
                    'start-typing-text': 'ابدأ الكتابة أو افحص النص لرؤية الاقتراحات.',
                    'tone-analysis-title': 'تحليل النبرة',
                    'adjust-tone-btn': 'ضبط النبرة ✨',
                    'recent-prompts-title': 'استعلامات الذكاء الاصطناعي الأخيرة',
                    'writing-modes': ['📝 غير رسمي', '👔 رسمي', '🎓 أكاديمي', '🎨 إبداعي', '💼 أعمال'],
                    'settings-tooltip': 'الإعدادات',
                    'theme-tooltip': 'تبديل السمة',
                    'help-tooltip': 'مساعدة',
                    'undo-tooltip': 'تراجع (Ctrl+Z)',
                    'redo-tooltip': 'إعادة (Ctrl+Y)',
                    'language-label': 'اللغة:',
                    'auto-correct-enabled': 'التصحيح التلقائي مفعل',
                    'auto-correct-disabled': 'التصحيح التلقائي معطل',
                    'text-corrected': 'تم تصحيح النص تلقائياً',
                    'sentence-enhanced': 'تم تحسين الجملة للوضوح',
                    'correction-applied': 'تم تطبيق التصحيح',
                    'enhancement-applied': 'تم تطبيق التحسين',
                    'language-changed': 'تم تغيير اللغة بنجاح',
                    'ui-updated': 'تم تحديث الواجهة',
                    'placeholder-text': 'ابدأ الكتابة أو الصق نصك هنا. سيساعدك GrammarPro على تحسينه.',
                    'editor-placeholder': 'ابدأ الكتابة بالعربية...',
                    'settings-title': 'الإعدادات',
                    'help-title': 'المساعدة والتوجيه',
                    'confirm-title': 'تأكيد الإجراء',
                    'cancel-btn': 'إلغاء',
                    'save-btn': 'حفظ التغييرات',
                    'confirm-btn': 'تأكيد',
                    'got-it-btn': 'فهمت!',
                    'close-btn': 'إغلاق'
                },
                'tr': { // Turkish translations
                    'writing-tools-title': 'Yazma Araçları',
                    'writing-mode-label': 'Yazma Modu',
                    'import-document-label': 'Belge İçe Aktar',
                    'drop-files-text': 'Dosyaları buraya bırakın veya',
                    'browse-text': 'gözat',
                    'quick-actions-label': 'Hızlı İşlemler',
                    'check-all-btn': 'Metni Kontrol Et',
                    'ai-rewrite-btn': 'AI Yeniden Yaz ✨',
                    'export-btn': 'Dışa Aktar',
                    'clear-btn': 'Editörü Temizle',
                    'writing-stats-title': 'Yazma İstatistikleri',
                    'words-label': 'Kelimeler:',
                    'characters-label': 'Karakterler:',
                    'errors-label': 'Sorunlar:',
                    'score-label': 'Puan:',
                    'document-editor-title': 'Belge Düzenleyici',
                    'ready-status': 'Hazır',
                    'suggestions-title': 'Öneriler',
                    'start-typing-text': 'Öneri görmek için yazmaya başlayın veya metninizi kontrol edin.',
                    'tone-analysis-title': 'Ton Analizi',
                    'adjust-tone-btn': 'Tonu Ayarla ✨',
                    'recent-prompts-title': 'Son AI İstemleri',
                    'writing-modes': ['📝 Gayri resmi', '👔 Resmi', '🎓 Akademik', '🎨 Yaratıcı', '💼 İş'],
                    'settings-tooltip': 'Ayarlar',
                    'theme-tooltip': 'Tema Değiştir',
                    'help-tooltip': 'Yardım',
                    'undo-tooltip': 'Geri Al (Ctrl+Z)',
                    'redo-tooltip': 'Yinele (Ctrl+Y)',
                    'language-label': 'Dil:',
                    'auto-correct-enabled': 'Otomatik düzeltme etkin',
                    'auto-correct-disabled': 'Otomatik düzeltme devre dışı',
                    'text-corrected': 'Metin otomatik olarak düzeltildi',
                    'sentence-enhanced': 'Cümle netlik için geliştirildi',
                    'correction-applied': 'Düzeltme uygulandı',
                    'enhancement-applied': 'Geliştirme uygulandı',
                    'language-changed': 'Dil başarıyla değiştirildi',
                    'ui-updated': 'Arayüz güncellendi',
                    'placeholder-text': 'Yazmaya başlayın veya metninizi buraya yapıştırın. GrammarPro onu geliştirmenize yardımcı olacak.',
                    'editor-placeholder': 'Türkçe yazmaya başlayın...',
                    'settings-title': 'Ayarlar',
                    'help-title': 'Yardım ve Rehberlik',
                    'confirm-title': 'Eylemi Onayla',
                    'cancel-btn': 'İptal',
                    'save-btn': 'Değişiklikleri Kaydet',
                    'confirm-btn': 'Onayla',
                    'got-it-btn': 'Anladım!',
                    'close-btn': 'Kapat'
                },
                'fr': { // French translations
                    'writing-tools-title': 'Outils d\'écriture',
                    'writing-mode-label': 'Mode d\'écriture',
                    'import-document-label': 'Importer un document',
                    'drop-files-text': 'Déposez les fichiers ici ou',
                    'browse-text': 'parcourir',
                    'quick-actions-label': 'Actions rapides',
                    'check-all-btn': 'Vérifier le texte',
                    'ai-rewrite-btn': 'Réécriture IA ✨',
                    'export-btn': 'Exporter',
                    'clear-btn': 'Vider l\'éditeur',
                    'writing-stats-title': 'Statistiques d\'écriture',
                    'words-label': 'Mots:',
                    'characters-label': 'Caractères:',
                    'errors-label': 'Problèmes:',
                    'score-label': 'Score:',
                    'document-editor-title': 'Éditeur de document',
                    'ready-status': 'Prêt',
                    'suggestions-title': 'Suggestions',
                    'start-typing-text': 'Commencez à taper ou vérifiez votre texte pour voir les suggestions.',
                    'tone-analysis-title': 'Analyse de ton',
                    'adjust-tone-btn': 'Ajuster le ton ✨',
                    'recent-prompts-title': 'Requêtes IA récentes',
                    'writing-modes': ['📝 Informel', '👔 Formel', '🎓 Académique', '🎨 Créatif', '💼 Professionnel'],
                    'settings-tooltip': 'Paramètres',
                    'theme-tooltip': 'Changer de thème',
                    'help-tooltip': 'Aide',
                    'undo-tooltip': 'Annuler (Ctrl+Z)',
                    'redo-tooltip': 'Rétablir (Ctrl+Y)',
                    'language-label': 'Langue:',
                    'auto-correct-enabled': 'Correction automatique activée',
                    'auto-correct-disabled': 'Correction automatique désactivée',
                    'text-corrected': 'Texte corrigé automatiquement',
                    'sentence-enhanced': 'Phrase améliorée pour plus de clarté',
                    'correction-applied': 'Correction appliquée',
                    'enhancement-applied': 'Amélioration appliquée',
                    'language-changed': 'Langue changée avec succès',
                    'ui-updated': 'Interface mise à jour',
                    'placeholder-text': 'Commencez à écrire ou collez votre texte ici. GrammarPro vous aidera à l\'améliorer.',
                    'editor-placeholder': 'Commencez à écrire en français...',
                    'settings-title': 'Paramètres',
                    'help-title': 'Aide et orientation',
                    'confirm-title': 'Confirmer l\'action',
                    'cancel-btn': 'Annuler',
                    'save-btn': 'Enregistrer les modifications',
                    'confirm-btn': 'Confirmer',
                    'got-it-btn': 'Compris!',
                    'close-btn': 'Fermer'
                },
                'nl': { // Dutch translations
                    'writing-tools-title': 'Schrijftools',
                    'writing-mode-label': 'Schrijfmodus',
                    'import-document-label': 'Document importeren',
                    'drop-files-text': 'Sleep bestanden hierheen of',
                    'browse-text': 'bladeren',
                    'quick-actions-label': 'Snelle acties',
                    'check-all-btn': 'Tekst controleren',
                    'ai-rewrite-btn': 'AI Herschrijven ✨',
                    'export-btn': 'Exporteren',
                    'clear-btn': 'Editor wissen',
                    'writing-stats-title': 'Schrijfstatistieken',
                    'words-label': 'Woorden:',
                    'characters-label': 'Tekens:',
                    'errors-label': 'Problemen:',
                    'score-label': 'Score:',
                    'document-editor-title': 'Documentbewerker',
                    'ready-status': 'Klaar',
                    'suggestions-title': 'Suggesties',
                    'start-typing-text': 'Begin met typen of controleer uw tekst om suggesties te zien.',
                    'tone-analysis-title': 'Toonanalyse',
                    'adjust-tone-btn': 'Toon aanpassen ✨',
                    'recent-prompts-title': 'Recente AI-prompts',
                    'writing-modes': ['📝 Informeel', '👔 Formeel', '🎓 Academisch', '🎨 Creatief', '💼 Zakelijk'],
                    'settings-tooltip': 'Instellingen',
                    'theme-tooltip': 'Thema wijzigen',
                    'help-tooltip': 'Help',
                    'undo-tooltip': 'Ongedaan maken (Ctrl+Z)',
                    'redo-tooltip': 'Opnieuw (Ctrl+Y)',
                    'language-label': 'Taal:',
                    'auto-correct-enabled': 'Automatische correctie ingeschakeld',
                    'auto-correct-disabled': 'Automatische correctie uitgeschakeld',
                    'text-corrected': 'Tekst automatisch gecorrigeerd',
                    'sentence-enhanced': 'Zin verbeterd voor duidelijkheid',
                    'correction-applied': 'Correctie toegepast',
                    'enhancement-applied': 'Verbetering toegepast',
                    'language-changed': 'Taal succesvol gewijzigd',
                    'ui-updated': 'Interface bijgewerkt',
                    'placeholder-text': 'Begin met typen of plak uw tekst hier. GrammarPro helpt u deze te verfijnen.',
                    'editor-placeholder': 'Begin met schrijven in het Nederlands...',
                    'settings-title': 'Instellingen',
                    'help-title': 'Help en begeleiding',
                    'confirm-title': 'Actie bevestigen',
                    'cancel-btn': 'Annuleren',
                    'save-btn': 'Wijzigingen opslaan',
                    'confirm-btn': 'Bevestigen',
                    'got-it-btn': 'Begrepen!',
                    'close-btn': 'Sluiten'
                },
                'it': { // Italian translations
                    'writing-tools-title': 'Strumenti di scrittura',
                    'writing-mode-label': 'Modalità di scrittura',
                    'import-document-label': 'Importa documento',
                    'drop-files-text': 'Trascina i file qui o',
                    'browse-text': 'sfoglia',
                    'quick-actions-label': 'Azioni rapide',
                    'check-all-btn': 'Controlla testo',
                    'ai-rewrite-btn': 'Riscrivi con IA ✨',
                    'export-btn': 'Esporta',
                    'clear-btn': 'Svuota editor',
                    'writing-stats-title': 'Statistiche di scrittura',
                    'words-label': 'Parole:',
                    'characters-label': 'Caratteri:',
                    'errors-label': 'Problemi:',
                    'score-label': 'Punteggio:',
                    'document-editor-title': 'Editor di documenti',
                    'ready-status': 'Pronto',
                    'suggestions-title': 'Suggerimenti',
                    'start-typing-text': 'Inizia a digitare o controlla il testo per vedere i suggerimenti.',
                    'tone-analysis-title': 'Analisi del tono',
                    'adjust-tone-btn': 'Regola tono ✨',
                    'recent-prompts-title': 'Prompt AI recenti',
                    'writing-modes': ['📝 Informale', '👔 Formale', '🎓 Accademico', '🎨 Creativo', '💼 Aziendale'],
                    'settings-tooltip': 'Impostazioni',
                    'theme-tooltip': 'Cambia tema',
                    'help-tooltip': 'Aiuto',
                    'undo-tooltip': 'Annulla (Ctrl+Z)',
                    'redo-tooltip': 'Ripeti (Ctrl+Y)',
                    'language-label': 'Lingua:',
                    'auto-correct-enabled': 'Correzione automatica abilitata',
                    'auto-correct-disabled': 'Correzione automatica disabilitata',
                    'text-corrected': 'Testo corretto automaticamente',
                    'sentence-enhanced': 'Frase migliorata per chiarezza',
                    'correction-applied': 'Correzione applicata',
                    'enhancement-applied': 'Miglioramento applicato',
                    'language-changed': 'Lingua cambiata con successo',
                    'ui-updated': 'Interfaccia aggiornata',
                    'placeholder-text': 'Inizia a scrivere o incolla il tuo testo qui. GrammarPro ti aiuterà a perfezionarlo.',
                    'editor-placeholder': 'Inizia a scrivere in italiano...',
                    'settings-title': 'Impostazioni',
                    'help-title': 'Aiuto e orientamento',
                    'confirm-title': 'Conferma azione',
                    'cancel-btn': 'Annulla',
                    'save-btn': 'Salva modifiche',
                    'confirm-btn': 'Conferma',
                    'got-it-btn': 'Capito!',
                    'close-btn': 'Chiudi'
                },
                // Add other language translations as needed
            },
        },

        // --- DOM ELEMENTS ---
        elements: {}, // To be populated on init

        // --- INITIALIZATION ---
        init() {
            this.config.defaultSettings = JSON.parse(JSON.stringify(this.state.settings)); // Deep copy
            this.cacheDOMElements();
            this.loadState(); // Load saved state first
            this.populateLanguageSelectors();
            this.applySettings();
            this.setupEventListeners();
            this.updateDictionaryList();
            this.updateRecentPromptsList();
            this.updateEditorStats();
            this.elements.textEditor.focus();
            this.initEditorHistory();
            this.showHelpModalIfFirstVisit();

            if (this.state.settings.autoDetectUILanguage) {
                this.autoSetUILanguage();
            } else {
                this.setLanguage(this.state.currentLanguage, this.state.currentDialect);
            }

            // Initial check if editor has content
            if (this.elements.textEditor.textContent.trim()) {
                this.analyzeText(false); // No debounce on initial load
            }
        },

        cacheDOMElements() {
            const ids = [
                'app', 'languageSelectorHeader', 'settingsBtn', 'themeToggleBtn', 'helpBtn',
                'writingMode', 'fileDropZone', 'fileInput', 'checkTextBtn', 'aiRewriteSelectionBtn', 'brainstormBtn',
                'exportBtn', 'clearBtn', 'aiPromptInput', 'generateTextBtn', 'wordCount', 'charCount',
                'errorCount', 'qualityScore', 'qualityBar', 'statusIcon', 'statusText', 'undoBtn', 'redoBtn',
                'textEditor', 'lastSavedTime', 'currentEditorLang', 'selectionInfo', 'cursorPosition',
                'aiContextPanel', 'selectedTextPreview', 'closeAiContextPanel', 'aiContextOutput',
                'suggestionsList', 'toneOverall', 'toneFormality', 'toneConfidence', 'adjustToneBtn',
                'recentPromptsList', 'settingsModal', 'settingsSearchInput', 'closeSettingsBtn',
                'primaryLanguage', 'dialectSelect', 'autoDetectUILanguage', 'rtlSupportArabic',
                'aiOnSelection', 'aiQuickReplies', 'aiPromptHistory', 'grammarCheck', 'spellCheck',
                'styleCheck', 'newWordInput', 'addWordBtn', 'dictionaryCount', 'importDictBtn', 'exportDictBtn',
                'dictionaryListContainer', 'dictionaryLanguageSelect', 'highContrastMode', 'languageProficiency',
                'toneDetection', 'plagiarismCheck', 'nonNativeAssistance', 'offlineMode', 'realTimeCheckDelay',
                'realTimeCheckDelayValue', 'resetSettingsBtn', 'cancelSettingsBtn', 'saveSettingsBtn', 'helpModal',
                'closeHelpBtn', 'gotItHelpBtn', 'confirmModal', 'confirmTitle', 'confirmMessage',
                'confirmCancelBtn', 'confirmOkBtn', 'loadingOverlay', 'loadingMessage',
                'customPromptModal', 'customPromptTitle', 'customPromptMessage', 'customPromptInputModal', 'customPromptCancelBtn', 'customPromptOkBtn',
                'autoCorrection', 'realTimeCorrection', 'sentenceEnhancement', 'showCorrectionFeedback', 'autoCorrectionToggle'
            ];
            ids.forEach(id => this.elements[id] = document.getElementById(id));

            this.elements.themeRadios = document.querySelectorAll('input[name="theme"]');
            this.elements.fontSizeBtns = document.querySelectorAll('.font-size-btn');
            this.elements.settingsNavItems = document.querySelectorAll('.settings-nav-item');
            this.elements.settingsSections = document.querySelectorAll('.settings-section-content');
            this.elements.aiActionBtns = document.querySelectorAll('.ai-action-btn');
        },

        // --- STATE MANAGEMENT (Load/Save) ---
        loadState() {
            try {
                const savedStateString = localStorage.getItem('GrammarProState');
                if (savedStateString) {
                    const savedState = JSON.parse(savedStateString);
                    // Merge carefully, especially nested objects like settings and dictionary
                    this.state.currentLanguage = savedState.currentLanguage || 'en';
                    this.state.currentDialect = savedState.currentDialect || this.config.languages[this.state.currentLanguage].dialects[Object.keys(this.config.languages[this.state.currentLanguage].dialects)[0]];
                    this.state.theme = savedState.theme || 'system';
                    this.state.fontSize = savedState.fontSize || 'normal';
                    this.state.personalDictionary = savedState.personalDictionary || {};
                    this.state.recentPrompts = savedState.recentPrompts || [];
                    this.state.lastSaved = savedState.lastSaved ? new Date(savedState.lastSaved) : null;

                    if (savedState.settings) {
                        this.state.settings = { ...this.config.defaultSettings, ...savedState.settings };
                    }
                    if (savedState.editorContent) {
                        this.elements.textEditor.innerHTML = savedState.editorContent;
                    }
                }
            } catch (error) {
                console.error("Error loading state from localStorage:", error);
                // Fallback to defaults if loading fails
            }
            this.updateLastSavedDisplay();
        },

        saveState() {
            try {
                const stateToSave = {
                    currentLanguage: this.state.currentLanguage,
                    currentDialect: this.state.currentDialect,
                    theme: this.state.theme,
                    fontSize: this.state.fontSize,
                    personalDictionary: this.state.personalDictionary,
                    recentPrompts: this.state.recentPrompts,
                    settings: this.state.settings,
                    lastSaved: this.state.lastSaved ? this.state.lastSaved.toISOString() : null,
                    editorContent: this.elements.textEditor.innerHTML, // Save editor content
                };
                localStorage.setItem('GrammarProState', JSON.stringify(stateToSave));
                this.state.lastSaved = new Date();
                this.updateLastSavedDisplay();
            } catch (error) {
                console.error("Error saving state to localStorage:", error);
                if (error.name === 'QuotaExceededError') {
                    this.showToast("Storage full. Could not save settings.", "error");
                }
            }
        },

        updateLastSavedDisplay() {
            if (this.state.lastSaved) {
                this.elements.lastSavedTime.textContent = this.state.lastSaved.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            } else {
                this.elements.lastSavedTime.textContent = "Never";
            }
        },

        // --- EVENT LISTENERS ---
        setupEventListeners() {
            // Header
            this.elements.languageSelectorHeader.addEventListener('change', (e) => this.handleLanguageChange(e.target.value));
            this.elements.settingsBtn.addEventListener('click', () => this.toggleModal(this.elements.settingsModal, true));
            this.elements.themeToggleBtn.addEventListener('click', () => this.cycleTheme());
            this.elements.helpBtn.addEventListener('click', () => this.toggleModal(this.elements.helpModal, true));

            // Left Sidebar
            this.elements.writingMode.addEventListener('change', () => this.analyzeText());
            this.elements.fileDropZone.addEventListener('click', () => this.elements.fileInput.click());
            this.elements.fileDropZone.addEventListener('dragover', this.handleDragOver);
            this.elements.fileDropZone.addEventListener('dragleave', this.handleDragLeave);
            this.elements.fileDropZone.addEventListener('drop', this.handleFileDrop.bind(this));
            this.elements.fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files));
            this.elements.checkTextBtn.addEventListener('click', () => this.analyzeText(false));
            this.elements.aiRewriteSelectionBtn.addEventListener('click', () => this.handleAIAction('rewrite-selection'));
            this.elements.brainstormBtn.addEventListener('click', () => this.handleBrainstormIdeas());
            this.elements.exportBtn.addEventListener('click', () => this.exportText());
            this.elements.clearBtn.addEventListener('click', () => this.confirmClearEditor());
            this.elements.generateTextBtn.addEventListener('click', () => this.handleAIGenerateText());


            // Editor
            this.elements.textEditor.addEventListener('input', this.debounce(this.handleEditorInput.bind(this), this.state.settings.realTimeCheckDelay));
            this.elements.textEditor.addEventListener('paste', this.handleEditorPaste.bind(this));
            this.elements.textEditor.addEventListener('keydown', this.handleEditorKeydown.bind(this));
            this.elements.textEditor.addEventListener('mouseup', this.handleEditorMouseUp.bind(this));
            this.elements.textEditor.addEventListener('focus', () => this.elements.textEditor.classList.add('focused'));
            this.elements.textEditor.addEventListener('blur', () => this.elements.textEditor.classList.remove('focused'));
            document.addEventListener('selectionchange', this.debounce(this.updateCursorAndSelectionInfo.bind(this), 100));


            // Editor Toolbar
            this.elements.autoCorrectionToggle.addEventListener('click', () => this.toggleAutoCorrection());
            this.elements.undoBtn.addEventListener('click', () => this.undo());
            this.elements.redoBtn.addEventListener('click', () => this.redo());

            // AI Context Panel
            this.elements.closeAiContextPanel.addEventListener('click', () => this.toggleAIContextPanel(false));
            this.elements.aiActionBtns.forEach(btn => {
                btn.addEventListener('click', (e) => this.handleAIAction(e.target.dataset.action));
            });
            this.elements.adjustToneBtn.addEventListener('click', () => this.handleAIAction('adjust-tone-selection')); // For right sidebar button

            // Settings Modal
            this.elements.closeSettingsBtn.addEventListener('click', () => this.toggleModal(this.elements.settingsModal, false));
            this.elements.cancelSettingsBtn.addEventListener('click', () => this.toggleModal(this.elements.settingsModal, false));
            this.elements.saveSettingsBtn.addEventListener('click', () => this.saveSettingsFromModal());
            this.elements.settingsNavItems.forEach(item => item.addEventListener('click', (e) => this.switchSettingsSection(e.currentTarget.dataset.section)));
            this.elements.settingsSearchInput.addEventListener('input', this.debounce(this.handleSettingsSearch.bind(this), 300));

            // Settings fields
            this.elements.primaryLanguage.addEventListener('change', (e) => this.handlePrimaryLanguageChangeForSettings(e.target.value));
            this.elements.themeRadios.forEach(radio => radio.addEventListener('change', (e) => this.setTheme(e.target.value)));
            this.elements.fontSizeBtns.forEach(btn => btn.addEventListener('click', (e) => this.setFontSize(e.currentTarget.dataset.size)));
            this.elements.addWordBtn.addEventListener('click', () => this.addWordToDictionary());
            this.elements.newWordInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') this.addWordToDictionary(); });
            this.elements.importDictBtn.addEventListener('click', () => this.importDictionary());
            this.elements.exportDictBtn.addEventListener('click', () => this.exportDictionary());
            this.elements.dictionaryLanguageSelect.addEventListener('change', () => this.updateDictionaryList());
            this.elements.resetSettingsBtn.addEventListener('click', () => this.confirmResetSettings());
            this.elements.realTimeCheckDelay.addEventListener('input', (e) => {
                this.elements.realTimeCheckDelayValue.textContent = `${e.target.value}ms`;
            });
            if (this.elements.highContrastMode) {
                this.elements.highContrastMode.addEventListener('change', (e) => {
                    this.state.settings.highContrastMode = e.target.checked;
                    this.updateHighContrast();
                });
            }


            // Help Modal
            this.elements.closeHelpBtn.addEventListener('click', () => this.toggleModal(this.elements.helpModal, false));
            this.elements.gotItHelpBtn.addEventListener('click', () => this.toggleModal(this.elements.helpModal, false));

            // Confirm Modal
            this.elements.confirmCancelBtn.addEventListener('click', () => this.toggleModal(this.elements.confirmModal, false));

            // Custom Prompt Modal
            this.elements.customPromptCancelBtn.addEventListener('click', () => this.toggleModal(this.elements.customPromptModal, false));
            this.elements.customPromptInputModal.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && this.state.currentAIToneActionCallback) {
                    this.elements.customPromptOkBtn.click();
                }
            });


            // Global listener for Esc key to close modals
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    if (!this.elements.settingsModal.classList.contains('hidden')) this.toggleModal(this.elements.settingsModal, false);
                    if (!this.elements.helpModal.classList.contains('hidden')) this.toggleModal(this.elements.helpModal, false);
                    if (!this.elements.confirmModal.classList.contains('hidden')) this.toggleModal(this.elements.confirmModal, false);
                    if (!this.elements.customPromptModal.classList.contains('hidden')) this.toggleModal(this.elements.customPromptModal, false);
                    this.hideSuggestionPopup();
                    this.toggleAIContextPanel(false);
                }
                // Keyboard shortcuts
                if ((e.ctrlKey || e.metaKey)) {
                    switch (e.key.toLowerCase()) {
                        case ' ':
                            if (document.activeElement !== this.elements.aiPromptInput && document.activeElement !== this.elements.customPromptInputModal) {
                                e.preventDefault();
                                this.analyzeText(false);
                            }
                            break;
                        case 's':
                            e.preventDefault();
                            this.saveState();
                            this.showToast("Content auto-saved!", "success");
                            break;
                        case ',':
                            e.preventDefault();
                            this.toggleModal(this.elements.settingsModal, true);
                            break;
                        case 'z':
                            if (document.activeElement === this.elements.textEditor) {
                                e.preventDefault();
                                this.undo();
                            }
                            break;
                        case 'y':
                            if (document.activeElement === this.elements.textEditor) {
                                e.preventDefault();
                                this.redo();
                            }
                            break;
                        case 'e':
                            e.preventDefault();
                            this.exportText();
                            break;
                    }
                    if (e.shiftKey && e.key.toLowerCase() === 'a') {
                        e.preventDefault();
                        this.handleAIAction('rewrite-selection');
                    }
                }
            });
            // Close suggestion popup when clicking outside
            document.addEventListener('click', (e) => {
                const popup = document.getElementById('suggestionPopup');
                if (popup && !popup.contains(e.target) && !e.target.closest('[data-error-type]')) {
                    this.hideSuggestionPopup();
                }
            });
        },

        // --- UI UPDATES & THEMING ---
        applySettings() {
            this.setTheme(this.state.theme);
            this.setFontSize(this.state.fontSize);
            this.setLanguage(this.state.currentLanguage, this.state.currentDialect);

            for (const key in this.state.settings) {
                if (this.elements[key] && typeof this.elements[key].checked !== 'undefined') {
                    this.elements[key].checked = this.state.settings[key];
                }
            }
            if (this.elements.realTimeCheckDelay) {
                this.elements.realTimeCheckDelay.value = this.state.settings.realTimeCheckDelay;
                this.elements.realTimeCheckDelayValue.textContent = `${this.state.settings.realTimeCheckDelay}ms`;
            }
            if(this.elements.languageProficiency) {
                this.elements.languageProficiency.value = this.state.settings.languageProficiency;
            }

            // Update auto-correction UI
            this.updateAutoCorrectionUI();
        },

        populateLanguageSelectors() {
            const selectors = [this.elements.languageSelectorHeader, this.elements.primaryLanguage, this.elements.dictionaryLanguageSelect];
            selectors.forEach(selector => {
                if (!selector) return;
                selector.innerHTML = '';
                if (selector === this.elements.primaryLanguage) {
                    const autoOption = document.createElement('option');
                    autoOption.value = "auto";
                    autoOption.textContent = "🌐 Auto-detect System Language";
                    selector.appendChild(autoOption);
                }
                for (const langCode in this.config.languages) {
                    const lang = this.config.languages[langCode];
                    const option = document.createElement('option');
                    option.value = langCode;
                    option.textContent = `${lang.flag} ${lang.name}`;
                    selector.appendChild(option);
                }
            });
        },

        handleLanguageChange(langCode) {
            if (langCode === "auto") {
                this.autoSetUILanguage();
            } else {
                const defaultDialect = Object.keys(this.config.languages[langCode].dialects)[0];
                this.setLanguage(langCode, defaultDialect);
                this.updateUILanguage(langCode);
            }
        },

        handlePrimaryLanguageChangeForSettings(langCode) {
            if (langCode === "auto") {
                this.elements.dialectSelect.innerHTML = '<option value="">N/A for Auto-detect</option>';
                this.elements.dialectSelect.disabled = true;
            } else {
                this.populateDialectSelector(langCode, this.elements.dialectSelect);
                this.elements.dialectSelect.disabled = false;
                const currentDialects = this.config.languages[langCode].dialects;
                const currentSelectedDialectInModal = this.elements.dialectSelect.value;
                if (!currentDialects[currentSelectedDialectInModal]) {
                    this.elements.dialectSelect.value = Object.keys(currentDialects)[0];
                }
            }
        },

        populateDialectSelector(langCode, selectorElement) {
            selectorElement.innerHTML = '';
            const langData = this.config.languages[langCode];
            if (langData && langData.dialects) {
                for (const dialectCode in langData.dialects) {
                    const option = document.createElement('option');
                    option.value = dialectCode;
                    option.textContent = langData.dialects[dialectCode];
                    selectorElement.appendChild(option);
                }
                selectorElement.disabled = Object.keys(langData.dialects).length <= 1;
            } else {
                selectorElement.innerHTML = '<option value="">N/A</option>';
                selectorElement.disabled = true;
            }
        },

        setLanguage(langCode, dialectCode) {
            if (langCode === 'auto') {
                this.autoSetUILanguage();
                return;
            }

            if (!this.config.languages[langCode]) {
                console.warn(`Language ${langCode} not supported. Defaulting to English.`);
                langCode = 'en';
                dialectCode = 'en-US';
            }
            if (!this.config.languages[langCode].dialects[dialectCode]) {
                console.warn(`Dialect ${dialectCode} not supported for ${langCode}. Defaulting to first available.`);
                dialectCode = Object.keys(this.config.languages[langCode].dialects)[0];
            }

            const previousLang = this.state.currentLanguage;
            this.state.currentLanguage = langCode;
            this.state.currentDialect = dialectCode;
            const langData = this.config.languages[langCode];
            this.state.isRTL = langData.rtl && this.state.settings.rtlSupportArabic;

            // Update document language and direction
            document.documentElement.lang = langCode.split('-')[0];
            document.documentElement.dir = this.state.isRTL ? 'rtl' : 'ltr';
            this.elements.app.classList.toggle('rtl', this.state.isRTL);

            // Update language selectors
            this.elements.languageSelectorHeader.value = langCode;
            if(this.elements.primaryLanguage.value !== 'auto') {
                this.elements.primaryLanguage.value = langCode;
            }
            this.populateDialectSelector(langCode, this.elements.dialectSelect);
            this.elements.dialectSelect.value = dialectCode;

            // Update editor language display
            this.elements.currentEditorLang.textContent = `${langData.flag} ${langData.name}`;

            // Apply UI language translations (this will also update placeholder)
            this.updateUILanguage(langCode);

            // Re-analyze text with new language
            this.analyzeText(false);
            this.saveState();

            // Show language change confirmation
            const translations = this.config.translations[langCode] || this.config.translations['en'];
            if (previousLang !== langCode) {
                this.showToast(
                    `${translations['language-changed'] || 'Language changed successfully'} - ${langData.name}`,
                    'success',
                    2500
                );
            }
        },

        updateUILanguage(langCode) {
            // If translations for this language don't exist, fall back to English
            const translations = this.config.translations[langCode] || this.config.translations['en'];

            // Update all text elements with translations
            for (const [key, value] of Object.entries(translations)) {
                if (Array.isArray(value)) continue; // Skip arrays like writing-modes
                const elements = document.getElementsByClassName(key);
                for (let i = 0; i < elements.length; i++) {
                    elements[i].textContent = value;
                }
            }

            // Update editor placeholder
            this.elements.textEditor.setAttribute('placeholder',
                translations['placeholder-text'] || translations['editor-placeholder'] || 'Start writing...');

            // Update writing modes in dropdown if language-specific labels exist
            if (translations['writing-modes']) {
                const writingModeSelect = this.elements.writingMode;
                const modes = writingModeSelect.querySelectorAll('option');
                if (modes.length === 5 && translations['writing-modes'].length >= 5) {
                    for (let i = 0; i < 5; i++) {
                        modes[i].textContent = translations['writing-modes'][i];
                    }
                }
            }

            // Update tooltips using translation keys
            this.updateTooltips(translations);

            // Update modal titles and buttons
            this.updateModalTexts(translations);

            // Update language selector label
            const langLabel = document.querySelector('.text-sm[style*="color: var(--text-secondary)"]');
            if (langLabel && langLabel.textContent.includes('Language:')) {
                langLabel.textContent = translations['language-label'] || 'Language:';
            }

            // Show feedback that UI has been updated
            this.showToast(translations['ui-updated'] || 'Interface updated', 'success', 1500);
        },

        updateTooltips(translations) {
            // Update all tooltips with proper translations
            const tooltipMappings = [
                { element: this.elements.settingsBtn, key: 'settings-tooltip' },
                { element: this.elements.themeToggleBtn, key: 'theme-tooltip' },
                { element: this.elements.helpBtn, key: 'help-tooltip' },
                { element: this.elements.undoBtn, key: 'undo-tooltip' },
                { element: this.elements.redoBtn, key: 'redo-tooltip' }
            ];

            tooltipMappings.forEach(({ element, key }) => {
                if (element) {
                    const tooltip = element.querySelector('.tooltiptext');
                    if (tooltip && translations[key]) {
                        tooltip.textContent = translations[key];
                    }
                }
            });

            // Update auto-correction tooltip separately since it's dynamic
            this.updateAutoCorrectionUI();
        },

        updateModalTexts(translations) {
            // Update modal titles and button texts
            const modalMappings = [
                { selector: '#settingsModal h2', key: 'settings-title' },
                { selector: '#helpModal h2', key: 'help-title' },
                { selector: '#confirmTitle', key: 'confirm-title' },
                { selector: '#cancelSettingsBtn', key: 'cancel-btn' },
                { selector: '#saveSettingsBtn', key: 'save-btn' },
                { selector: '#confirmOkBtn', key: 'confirm-btn' },
                { selector: '#gotItHelpBtn', key: 'got-it-btn' },
                { selector: '#closeSettingsBtn .sr-only', key: 'close-btn' },
                { selector: '#closeHelpBtn .sr-only', key: 'close-btn' }
            ];

            modalMappings.forEach(({ selector, key }) => {
                const element = document.querySelector(selector);
                if (element && translations[key]) {
                    element.textContent = translations[key];
                }
            });
        },

        autoSetUILanguage() {
            const browserLang = (navigator.language || navigator.userLanguage || 'en').split('-')[0];
            const supportedLangCode = Object.keys(this.config.languages).find(lc => lc.startsWith(browserLang));

            let finalLangCode = 'en';
            let finalDialectCode = 'en-US';

            if (supportedLangCode) {
                finalLangCode = supportedLangCode;
                finalDialectCode = Object.keys(this.config.languages[supportedLangCode].dialects)[0];
            }

            this.state.currentLanguage = finalLangCode;
            this.state.currentDialect = finalDialectCode;
            this.setLanguage(finalLangCode, finalDialectCode);
            this.updateUILanguage(finalLangCode);
        },

        cycleTheme() {
            const themes = ['light', 'dark', 'system'];
            let currentIndex = themes.indexOf(this.state.theme);
            currentIndex = (currentIndex + 1) % themes.length;
            this.setTheme(themes[currentIndex]);
        },

        setTheme(theme) {
            this.state.theme = theme;

            let effectiveBaseTheme = theme;
            if (theme === 'system') {
                effectiveBaseTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            }
            document.documentElement.setAttribute('data-theme', effectiveBaseTheme);

            if (this.elements.themeToggleBtn) {
                const icon = this.elements.themeToggleBtn.querySelector('i');
                if (theme === 'light') icon.className = 'fas fa-moon text-lg';
                else if (theme === 'dark') icon.className = 'fas fa-sun text-lg';
                else icon.className = 'fas fa-desktop text-lg';
            }

            if (this.elements.themeRadios) {
                this.elements.themeRadios.forEach(radio => radio.checked = radio.value === theme);
            }

            this.updateHighContrast();
            this.saveState();
        },

        updateHighContrast() {
            const highContrastEnabled = this.state.settings.highContrastMode;
            const docElement = document.documentElement;

            if (highContrastEnabled) {
                docElement.classList.add('high-contrast-mode');
            } else {
                docElement.classList.remove('high-contrast-mode');
            }
        },

        toggleAutoCorrection() {
            this.state.settings.autoCorrection = !this.state.settings.autoCorrection;
            this.updateAutoCorrectionUI();
            this.saveState();

            const translations = this.config.translations[this.state.currentLanguage] || this.config.translations['en'];
            const message = this.state.settings.autoCorrection ?
                translations['auto-correct-enabled'] || 'Auto-correction enabled' :
                translations['auto-correct-disabled'] || 'Auto-correction disabled';

            this.showToast(message, 'info', 2000);
        },

        updateAutoCorrectionUI() {
            if (!this.elements.autoCorrectionToggle) return;

            const isEnabled = this.state.settings.autoCorrection;
            const button = this.elements.autoCorrectionToggle;
            const icon = button.querySelector('i');
            const tooltip = button.querySelector('.tooltiptext');

            if (isEnabled) {
                button.style.color = 'var(--success-color)';
                button.style.backgroundColor = 'color-mix(in srgb, var(--success-color) 20%, transparent)';
                icon.className = 'fas fa-magic text-sm';
            } else {
                button.style.color = 'var(--text-secondary)';
                button.style.backgroundColor = 'var(--bg-alt)';
                icon.className = 'fas fa-magic-wand-sparkles text-sm';
            }

            const translations = this.config.translations[this.state.currentLanguage] || this.config.translations['en'];
            tooltip.textContent = isEnabled ?
                translations['auto-correct-enabled'] || 'Auto-correction enabled' :
                translations['auto-correct-disabled'] || 'Auto-correction disabled';
        },


        setFontSize(size) {
            this.state.fontSize = size;
            const appElement = this.elements.app;
            appElement.classList.remove('font-size-small', 'font-size-normal', 'font-size-large');
            appElement.classList.add(`font-size-${size}`);

            this.elements.fontSizeBtns.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.size === size);
                if (btn.dataset.size === size) {
                    btn.style.backgroundColor = 'var(--primary-color)';
                    btn.style.color = 'white';
                } else {
                    btn.style.backgroundColor = 'var(--bg-secondary)';
                    btn.style.color = 'var(--text-primary)';
                }
            });
            this.saveState();
        },

        // --- EDITOR FUNCTIONALITY ---
        initEditorHistory() {
            this.state.editorHistory.undoStack = [this.elements.textEditor.innerHTML];
            this.state.editorHistory.redoStack = [];
            this.updateUndoRedoButtons();
        },

        recordHistory() {
            const currentContent = this.elements.textEditor.innerHTML;
            const lastRecordedContent = this.state.editorHistory.undoStack[this.state.editorHistory.undoStack.length - 1];

            if (currentContent !== lastRecordedContent) {
                this.state.editorHistory.undoStack.push(currentContent);
                if (this.state.editorHistory.undoStack.length > this.state.editorHistory.maxHistory) {
                    this.state.editorHistory.undoStack.shift();
                }
                this.state.editorHistory.redoStack = [];
                this.updateUndoRedoButtons();
            }
        },

        undo() {
            if (this.state.editorHistory.undoStack.length > 1) {
                const currentState = this.state.editorHistory.undoStack.pop();
                this.state.editorHistory.redoStack.push(currentState);
                this.elements.textEditor.innerHTML = this.state.editorHistory.undoStack[this.state.editorHistory.undoStack.length - 1];
                this.updateUndoRedoButtons();
                this.analyzeText(false);
                this.saveState();
            }
        },

        redo() {
            if (this.state.editorHistory.redoStack.length > 0) {
                const nextState = this.state.editorHistory.redoStack.pop();
                this.state.editorHistory.undoStack.push(nextState);
                this.elements.textEditor.innerHTML = nextState;
                this.updateUndoRedoButtons();
                this.analyzeText(false);
                this.saveState();
            }
        },

        updateUndoRedoButtons() {
            this.elements.undoBtn.disabled = this.state.editorHistory.undoStack.length <= 1;
            this.elements.redoBtn.disabled = this.state.editorHistory.redoStack.length === 0;
            this.elements.undoBtn.style.opacity = this.elements.undoBtn.disabled ? 0.5 : 1;
            this.elements.redoBtn.style.opacity = this.elements.redoBtn.disabled ? 0.5 : 1;
        },

        handleEditorInput() {
            this.updateEditorStats();

            // Real-time auto-correction
            if (this.state.settings.autoCorrection && this.state.settings.realTimeCorrection) {
                this.debounce(() => this.performAutoCorrection(), 500)();
            }

            // Real-time sentence enhancement
            if (this.state.settings.sentenceEnhancement) {
                this.debounce(() => this.performSentenceEnhancement(), 1000)();
            }

            if (this.state.settings.grammarCheck || this.state.settings.spellCheck || this.state.settings.styleCheck) {
                this.analyzeText(true);
            }
            this.recordHistory();
            this.saveState();
        },

        handleEditorPaste(event) {
            event.preventDefault();
            const text = (event.clipboardData || window.clipboardData).getData('text/plain');
            document.execCommand('insertText', false, text);
        },

        handleEditorKeydown(event) {
            if (event.key === 'Tab') {
                event.preventDefault();
                document.execCommand('insertText', false, '    ');
            }
        },

        handleEditorMouseUp() {
            const selection = window.getSelection();
            if (selection && selection.toString().trim().length > 0 && this.state.settings.aiOnSelection) {
                this.state.currentSelection = selection.getRangeAt(0).cloneRange();
                this.elements.selectedTextPreview.textContent = `"${selection.toString().substring(0, 100)}${selection.toString().length > 100 ? '...' : ''}"`;
                this.toggleAIContextPanel(true);
            } else {
                this.toggleAIContextPanel(false);
                this.state.currentSelection = null;
            }
            this.updateCursorAndSelectionInfo();
        },

        updateCursorAndSelectionInfo() {
            const selection = window.getSelection();
            if (!selection || selection.rangeCount === 0 || !this.elements.textEditor.contains(selection.anchorNode)) {
                this.elements.selectionInfo.textContent = "";
                this.elements.cursorPosition.textContent = "Line 1, Col 1";
                return;
            }

            const range = selection.getRangeAt(0);
            const selectedText = range.toString();

            if (selectedText.length > 0) {
                const wordCount = selectedText.trim().split(/\s+/).length;
                this.elements.selectionInfo.textContent = `${wordCount} word(s) selected`;
            } else {
                this.elements.selectionInfo.textContent = "";
            }

            // Get the actual text position by calculating from the beginning of the editor
            const caretPosition = this.getCaretPosition();
            const fullText = this.elements.textEditor.textContent;
            const preCaretText = fullText.substring(0, caretPosition);
            const lines = preCaretText.split('\n');
            const lineNumber = lines.length;
            const columnNumber = lines[lines.length - 1].length + 1;
            this.elements.cursorPosition.textContent = `Line ${lineNumber}, Col ${columnNumber}`;
        },

        getCaretPosition() {
            const selection = window.getSelection();
            if (!selection.rangeCount) return 0;

            const range = selection.getRangeAt(0);
            const preCaretRange = range.cloneRange();
            preCaretRange.selectNodeContents(this.elements.textEditor);
            preCaretRange.setEnd(range.endContainer, range.endOffset);
            return preCaretRange.toString().length;
        },

        setCaretPosition(position) {
            const editor = this.elements.textEditor;
            const textNodes = this.getTextNodes(editor);
            let currentPosition = 0;

            for (let i = 0; i < textNodes.length; i++) {
                const node = textNodes[i];
                const nodeLength = node.textContent.length;

                if (currentPosition + nodeLength >= position) {
                    const range = document.createRange();
                    const selection = window.getSelection();

                    const offset = position - currentPosition;
                    range.setStart(node, Math.min(offset, nodeLength));
                    range.collapse(true);

                    selection.removeAllRanges();
                    selection.addRange(range);
                    return;
                }
                currentPosition += nodeLength;
            }

            // If position is beyond text, place cursor at end
            if (textNodes.length > 0) {
                const lastNode = textNodes[textNodes.length - 1];
                const range = document.createRange();
                const selection = window.getSelection();

                range.setStart(lastNode, lastNode.textContent.length);
                range.collapse(true);

                selection.removeAllRanges();
                selection.addRange(range);
            }
        },

        getTextNodes(element) {
            const textNodes = [];
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }

            return textNodes;
        },

        updateEditorStats() {
            const text = this.elements.textEditor.textContent;
            const words = text.trim() ? text.trim().split(/\s+/).length : 0;
            const characters = text.length;
            this.elements.wordCount.textContent = words;
            this.elements.charCount.textContent = characters;

            const errorCount = parseInt(this.elements.errorCount.textContent) || 0;
            const score = Math.max(0, Math.round(100 - (errorCount * 5 / Math.max(1, words / 100)) ) );
            this.elements.qualityScore.textContent = `${score}%`;
            this.elements.qualityBar.style.width = `${score}%`;
            this.elements.qualityBar.style.backgroundColor = score < 50 ? 'var(--error-color)' : score < 80 ? 'var(--warning-color)' : 'var(--success-color)';
        },

        clearEditor() {
            this.elements.textEditor.innerHTML = '';
            this.clearSuggestions();
            this.updateEditorStats();
            this.initEditorHistory();
            this.showToast("Editor cleared.", "info");
            this.saveState();
        },

        confirmClearEditor() {
            this.showConfirmModal("Clear Editor", "Are you sure you want to clear all content from the editor? This action cannot be undone.", () => {
                this.clearEditor();
            });
        },

        exportText() {
            const text = this.elements.textEditor.textContent;
            if (!text.trim()) {
                this.showToast("Nothing to export.", "warning");
                return;
            }
            const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `GrammarPro_Document_${new Date().toISOString().slice(0,10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            this.showToast("Document exported.", "success");
        },

        // --- FILE HANDLING ---
        handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.borderColor = 'var(--primary-color)';
            event.currentTarget.style.backgroundColor = 'var(--bg-alt)';
        },
        handleDragLeave(event) {
            event.currentTarget.style.borderColor = 'var(--border-color)';
            event.currentTarget.style.backgroundColor = 'transparent';
        },
        handleFileDrop(event) {
            event.preventDefault();
            this.handleDragLeave({currentTarget: event.currentTarget});
            this.handleFileSelect(event.dataTransfer.files);
        },
        async handleFileSelect(files) {
            if (!files || files.length === 0) return;
            const file = files[0];

            this.showLoading(true, `Loading ${file.name}...`);
            try {
                let textContent = "";
                if (file.type === "text/plain" || file.type === "text/markdown" || file.name.endsWith(".md")) {
                    textContent = await file.text();
                } else if (file.name.endsWith(".docx")) {
                    this.showToast("DOCX import is a conceptual feature. Pasting content is recommended.", "info");
                    textContent = `(Mock DOCX content from ${file.name})`;
                } else {
                    this.showToast(`Unsupported file type: ${file.type || 'unknown'}. Please use .txt, .md.`, "error");
                    this.showLoading(false);
                    return;
                }
                this.elements.textEditor.textContent = textContent;
                this.analyzeText(false);
                this.initEditorHistory();
                this.showToast(`${file.name} loaded.`, "success");
            } catch (error) {
                console.error("Error loading file:", error);
                this.showToast(`Error loading file: ${error.message}`, "error");
            } finally {
                this.showLoading(false);
            }
        },

        // --- TEXT ANALYSIS & SUGGESTIONS ---
        analyzeText(useDebounce = true) {
            if (useDebounce) {
                this.debounce(() => this._performAnalysis(), this.state.settings.realTimeCheckDelay)();
            } else {
                this._performAnalysis();
            }
        },

        _performAnalysis() {
            const text = this.elements.textEditor.textContent;
            if (!text.trim()) {
                this.clearSuggestions();
                this.elements.errorCount.textContent = '0';
                this.updateEditorStats();
                return;
            }
            this.updateStatus("Analyzing...", "info", true);

            const errors = this.mockFindErrors(text);
            this.highlightErrorsInEditor(errors);
            this.displaySuggestions(errors);
            this.elements.errorCount.textContent = errors.length;
            this.updateEditorStats();
            this.updateMockToneAnalysis(text);
            this.updateStatus("Ready", "success");
        },

        mockFindErrors(text) {
            const errors = [];
            const words = text.split(/(\s+)/);
            let charIndex = 0;

            const commonTypos = { "teh": "the", "wierd": "weird", "recieve": "receive", "seperate": "separate" };
            const commonGrammar = { "your awesome": "you're awesome", "their going": "they're going", "it is a": "it's a" };
            const styleWords = ["utilize", "therefore", "furthermore", "commence"];

            words.forEach(word => {
                const originalWord = word;
                const lowerWord = word.toLowerCase().trim();
                if (lowerWord.length === 0) {
                    charIndex += originalWord.length;
                    return;
                }

                const currentLangDict = this.state.personalDictionary[this.state.currentLanguage] || [];
                if (currentLangDict.some(dictWord => dictWord.toLowerCase() === lowerWord)) {
                    charIndex += originalWord.length;
                    return;
                }

                let errorFound = false;
                if (this.state.settings.spellCheck && commonTypos[lowerWord]) {
                    errors.push({ type: 'spelling', text: originalWord, suggestions: [commonTypos[lowerWord]], explanation: `Possible typo for "${commonTypos[lowerWord]}"`, start: charIndex, end: charIndex + originalWord.length });
                    errorFound = true;
                }
                if (!errorFound && this.state.settings.grammarCheck) {
                    for (const phrase in commonGrammar) {
                        if (text.substring(charIndex).toLowerCase().startsWith(phrase)) {
                            errors.push({ type: 'grammar', text: text.substring(charIndex, charIndex + phrase.length), suggestions: [commonGrammar[phrase]], explanation: `Consider using "${commonGrammar[phrase]}"`, start: charIndex, end: charIndex + phrase.length });
                            errorFound = true;
                            break;
                        }
                    }
                }
                if (!errorFound && this.state.settings.styleCheck && styleWords.includes(lowerWord) && Math.random() < this.config.mockErrorProbability) {
                    errors.push({ type: 'style', text: originalWord, suggestions: [`Consider simpler: ${lowerWord === 'utilize' ? 'use' : 'alternative'}`], explanation: `"${originalWord}" could be simpler.`, start: charIndex, end: charIndex + originalWord.length });
                    errorFound = true;
                }
                if (!errorFound && Math.random() < (this.config.mockErrorProbability / 2) && lowerWord.length > 3) {
                    errors.push({ type: 'clarity', text: originalWord, suggestions: [`Rephrase for clarity?`], explanation: `Is "${originalWord}" clear?`, start: charIndex, end: charIndex + originalWord.length });
                }

                charIndex += originalWord.length;
            });
            return errors;
        },

        highlightErrorsInEditor(errors) {
            const editor = this.elements.textEditor;
            const selection = window.getSelection();
            let originalRange = null;
            if (selection.rangeCount > 0 && editor.contains(selection.anchorNode)) {
                originalRange = selection.getRangeAt(0).cloneRange();
            }

            let textContent = editor.textContent;
            let newHTML = "";
            let lastIndex = 0;

            errors.sort((a, b) => a.start - b.start);

            errors.forEach(error => {
                if (error.start < lastIndex) return;

                newHTML += this.escapeHTML(textContent.substring(lastIndex, error.start));
                const errorClass = this.getErrorClass(error.type);
                const errorSpan = document.createElement('span');
                errorSpan.className = errorClass;
                errorSpan.textContent = this.escapeHTML(error.text);
                errorSpan.dataset.errorType = error.type;
                errorSpan.dataset.suggestions = JSON.stringify(error.suggestions);
                errorSpan.dataset.explanation = error.explanation;
                errorSpan.dataset.startIndex = error.start;
                errorSpan.dataset.originalText = error.text;
                errorSpan.addEventListener('click', (e) => this.showSuggestionPopup(e.currentTarget));
                newHTML += errorSpan.outerHTML;
                lastIndex = error.end;
            });
            newHTML += this.escapeHTML(textContent.substring(lastIndex));

            editor.innerHTML = newHTML;

            if (originalRange) {
                try {
                    selection.removeAllRanges();
                    selection.addRange(originalRange);
                } catch (e) {
                    const range = document.createRange();
                    range.selectNodeContents(editor);
                    range.collapse(false);
                    selection.removeAllRanges();
                    selection.addRange(range);
                }
            }
        },

        escapeHTML(str) {
            return str.replace(/[&<>"']/g, function (match) {
                return { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' }[match];
            });
        },

        getErrorClass(type) {
            switch (type) {
                case 'spelling': return 'spelling-error';
                case 'grammar': return 'grammar-error';
                case 'style': return 'style-suggestion';
                case 'clarity': return 'clarity-improvement';
                case 'tone': return 'tone-suggestion';
                default: return 'unknown-error';
            }
        },

        displaySuggestions(errors) {
            const list = this.elements.suggestionsList;
            if (errors.length === 0) {
                list.innerHTML = `<div class="text-sm text-center py-8" style="color: var(--success-color);"><i class="fas fa-check-circle text-3xl mb-2"></i><p>No issues found!</p></div>`;
                return;
            }
            list.innerHTML = errors.map((error, index) => `
                <div class="p-3 rounded border animate-fadeIn suggestion-card" style="border-color: var(--border-color); background-color: var(--bg-alt);" data-suggestion-index="${index}">
                    <div class="flex items-start justify-between">
                        <div>
                            <div class="flex items-center mb-1">
                                <span class="inline-block w-2.5 h-2.5 mr-2 rounded-full ${this.getErrorIndicatorColor(error.type)}"></span>
                                <span class="text-sm font-medium capitalize" style="color: var(--text-primary);">${error.type}</span>
                            </div>
                            <p class="text-xs mb-1" style="color: var(--text-secondary);">${error.explanation}</p>
                            <p class="text-xs font-semibold" style="color: var(--text-accent);">Original: <em class="font-normal" style="color: var(--text-secondary); text-decoration: line-through;">${this.escapeHTML(error.text)}</em></p>
                        </div>
                        <button class="btn btn-secondary btn-sm view-fix-btn" data-error-ref="${error.start}-${error.text.length}">View Fix</button>
                    </div>
                </div>
            `).join('');

            list.querySelectorAll('.view-fix-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const errorRef = e.currentTarget.dataset.errorRef.split('-');
                    const start = parseInt(errorRef[0]);
                    const targetErrorSpan = this.elements.textEditor.querySelector(`span[data-start-index="${start}"]`); // Corrected selector
                    if (targetErrorSpan) {
                        targetErrorSpan.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        this.showSuggestionPopup(targetErrorSpan);
                    }
                });
            });
        },

        getErrorIndicatorColor(type) {
            switch (type) {
                case 'spelling': case 'grammar': return 'bg-red-500';
                case 'style': return 'bg-green-500';
                case 'clarity': return 'bg-blue-500';
                case 'tone': return 'bg-yellow-500';
                default: return 'bg-gray-400';
            }
        },

        showSuggestionPopup(errorSpan) {
            this.hideSuggestionPopup();
            const type = errorSpan.dataset.errorType;
            const suggestions = JSON.parse(errorSpan.dataset.suggestions);
            const explanation = errorSpan.dataset.explanation;
            const originalText = errorSpan.dataset.originalText;

            const popup = document.createElement('div');
            popup.id = 'suggestionPopup';
            popup.className = 'suggestion-popup absolute p-4 rounded-lg shadow-lg w-64 animate-fadeIn';

            let html = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="text-sm font-semibold capitalize flex items-center"><span class="inline-block w-2 h-2 mr-2 rounded-full ${this.getErrorIndicatorColor(type)}"></span>${type} Issue</h4>
                    <button id="closeSuggestionPopupBtn" class="p-1 rounded -mr-1 -mt-1" style="color: var(--text-secondary);"><i class="fas fa-times text-xs"></i></button>
                </div>
                <p class="text-xs mb-2" style="color: var(--text-secondary);">${explanation}</p>
                <div class="space-y-1 max-h-32 overflow-y-auto custom-scrollbar">`;

            if (suggestions && suggestions.length > 0) {
                suggestions.forEach(sugg => {
                    html += `<button class="block w-full text-left text-xs p-1.5 rounded hover-bg-alt apply-suggestion-btn" data-suggestion="${this.escapeHTML(sugg)}">${this.escapeHTML(sugg)}</button>`;
                });
            } else {
                html += `<p class="text-xs text-center" style="color: var(--text-secondary);">No specific suggestions.</p>`;
            }

            html += `</div>
                     <button class="block w-full text-left text-xs p-1.5 rounded hover-bg-alt mt-2 add-to-dict-btn" data-word="${this.escapeHTML(originalText)}"><i class="fas fa-book mr-1"></i> Add "${this.escapeHTML(originalText)}" to Dictionary</button>
                     <button class="block w-full text-left text-xs p-1.5 rounded hover-bg-alt ignore-suggestion-btn"><i class="fas fa-eye-slash mr-1"></i> Ignore</button>`;
            popup.innerHTML = html;

            document.body.appendChild(popup);
            this.positionPopupNearElement(popup, errorSpan);

            popup.querySelector('#closeSuggestionPopupBtn').addEventListener('click', () => this.hideSuggestionPopup());
            popup.querySelectorAll('.apply-suggestion-btn').forEach(btn => {
                btn.addEventListener('click', () => this.applySuggestion(errorSpan, btn.dataset.suggestion));
            });
            popup.querySelector('.add-to-dict-btn').addEventListener('click', (e) => this.addWordToDictionaryFromSuggestion(e.currentTarget.dataset.word, errorSpan));
            popup.querySelector('.ignore-suggestion-btn').addEventListener('click', () => this.ignoreSuggestion(errorSpan));
        },

        hideSuggestionPopup() {
            const popup = document.getElementById('suggestionPopup');
            if (popup) popup.remove();
        },

        applySuggestion(errorSpan, suggestionText) {
            const editor = this.elements.textEditor;
            errorSpan.textContent = suggestionText;
            errorSpan.className = '';
            errorSpan.removeAttribute('data-error-type');
            errorSpan.removeAttribute('data-suggestions');
            errorSpan.removeAttribute('data-explanation');

            const textNode = document.createTextNode(errorSpan.textContent);
            errorSpan.parentNode.replaceChild(textNode, errorSpan);

            this.hideSuggestionPopup();
            this.recordHistory();
            this.analyzeText(false);
            this.saveState();
        },

        addWordToDictionaryFromSuggestion(word, errorSpan) {
            this.addWordToDictionary(word);
            this.hideSuggestionPopup();
        },

        ignoreSuggestion(errorSpan) {
            const textNode = document.createTextNode(errorSpan.textContent);
            errorSpan.parentNode.replaceChild(textNode, errorSpan);
            this.hideSuggestionPopup();
            this.recordHistory();
            this.saveState();
        },

        clearSuggestions() {
            this.elements.suggestionsList.innerHTML = `<div class="text-sm text-center py-8" style="color: var(--text-secondary);"><i class="fas fa-search text-3xl mb-2"></i><p class="start-typing-text">Start typing or check text to see suggestions.</p></div>`;
        },

        updateMockToneAnalysis(text) {
            if (!this.state.settings.toneDetection) {
                this.elements.toneOverall.textContent = "Disabled";
                this.elements.toneFormality.textContent = "N/A";
                this.elements.toneConfidence.textContent = "N/A";
                return;
            }
            let overall = "Neutral";
            let formalityScore = 70;
            let confidenceScore = 80;

            if (text.match(/please|thank you|sorry/gi)) overall = "Polite";
            if (text.match(/!|\b(great|amazing|wonderful)\b/gi)) { overall = "Enthusiastic"; confidenceScore += 10; formalityScore -=10; }
            if (text.match(/\b(must|require|demand)\b/gi)) { overall = "Assertive"; formalityScore += 10; }
            if (text.match(/\b(maybe|perhaps|could)\b/gi)) { overall = "Uncertain"; confidenceScore -=15; }

            formalityScore = Math.max(0, Math.min(100, formalityScore));
            confidenceScore = Math.max(0, Math.min(100, confidenceScore));

            this.elements.toneOverall.textContent = overall;
            this.elements.toneFormality.textContent = `${formalityScore > 75 ? 'Formal' : formalityScore < 40 ? 'Casual' : 'Neutral'} (${formalityScore}%)`;
            this.elements.toneConfidence.textContent = `${confidenceScore > 75 ? 'Confident' : confidenceScore < 40 ? 'Hesitant' : 'Neutral'} (${confidenceScore}%)`;
        },

        // --- DICTIONARY ---
        addWordToDictionary(wordToAdd = null) {
            const word = wordToAdd || this.elements.newWordInput.value.trim();
            if (!word) {
                this.showToast("Please enter a word.", "warning");
                return;
            }
            const lang = this.state.currentLanguage === 'auto' ? (navigator.language || navigator.userLanguage || 'en').split('-')[0] : this.state.currentLanguage;

            if (!this.state.personalDictionary[lang]) {
                this.state.personalDictionary[lang] = [];
            }
            if (!this.state.personalDictionary[lang].map(w => w.toLowerCase()).includes(word.toLowerCase())) {
                this.state.personalDictionary[lang].push(word);
                this.state.personalDictionary[lang].sort((a,b) => a.localeCompare(b));
                this.updateDictionaryList();
                this.saveState();
                this.analyzeText(false);
                this.showToast(`"${word}" added to ${this.config.languages[lang].name} dictionary.`, "success");
            } else {
                this.showToast(`"${word}" is already in the dictionary.`, "info");
            }
            if (!wordToAdd) this.elements.newWordInput.value = '';
        },

        removeWordFromDictionary(word, lang) {
            if (this.state.personalDictionary[lang]) {
                this.state.personalDictionary[lang] = this.state.personalDictionary[lang].filter(w => w.toLowerCase() !== word.toLowerCase());
                if (this.state.personalDictionary[lang].length === 0) {
                    delete this.state.personalDictionary[lang];
                }
                this.updateDictionaryList();
                this.saveState();
                this.analyzeText(false);
                this.showToast(`"${word}" removed from dictionary.`, "info");
            }
        },

        updateDictionaryList() {
            const langForDictView = this.elements.dictionaryLanguageSelect.value;
            const words = this.state.personalDictionary[langForDictView] || [];
            this.elements.dictionaryCount.textContent = words.length;
            const container = this.elements.dictionaryListContainer;

            if (words.length === 0) {
                container.innerHTML = `<p class="text-center text-sm" style="color: var(--text-secondary);">Dictionary for ${this.config.languages[langForDictView].name} is empty.</p>`;
                return;
            }
            container.innerHTML = words.map(w => `
                <div class="flex items-center justify-between text-sm p-1.5 rounded hover-bg-alt">
                    <span>${this.escapeHTML(w)}</span>
                    <button class="text-red-500 hover:text-red-700 text-xs remove-word-btn" data-word="${this.escapeHTML(w)}" data-lang="${langForDictView}"><i class="fas fa-trash"></i></button>
                </div>
            `).join('');

            container.querySelectorAll('.remove-word-btn').forEach(btn => {
                btn.addEventListener('click', (e) => this.removeWordFromDictionary(e.currentTarget.dataset.word, e.currentTarget.dataset.lang));
            });
        },

        importDictionary() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json,.txt';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                this.showLoading(true, "Importing dictionary...");
                try {
                    const text = await file.text();
                    const currentImportLang = this.state.currentLanguage === 'auto' ? (navigator.language || navigator.userLanguage || 'en').split('-')[0] : this.state.currentLanguage;

                    if (file.name.endsWith('.json')) {
                        const jsonData = JSON.parse(text);
                        if (Array.isArray(jsonData)) {
                            if (!this.state.personalDictionary[currentImportLang]) this.state.personalDictionary[currentImportLang] = [];
                            jsonData.forEach(w => {
                                if (typeof w === 'string' && w.trim() && !this.state.personalDictionary[currentImportLang].map(wd => wd.toLowerCase()).includes(w.toLowerCase())) {
                                    this.state.personalDictionary[currentImportLang].push(w.trim());
                                }
                            });
                        } else if (typeof jsonData === 'object') {
                            Object.keys(jsonData).forEach(lang => {
                                if (this.config.languages[lang] && Array.isArray(jsonData[lang])) {
                                    if (!this.state.personalDictionary[lang]) this.state.personalDictionary[lang] = [];
                                    jsonData[lang].forEach(w => {
                                        if (typeof w === 'string' && w.trim() && !this.state.personalDictionary[lang].map(wd => wd.toLowerCase()).includes(w.toLowerCase())) {
                                            this.state.personalDictionary[lang].push(w.trim());
                                        }
                                    });
                                }
                            });
                        }
                    } else {
                        const importedWords = text.split(/\r?\n/).map(w => w.trim()).filter(Boolean);
                        if (!this.state.personalDictionary[currentImportLang]) this.state.personalDictionary[currentImportLang] = [];
                        importedWords.forEach(w => {
                            if (!this.state.personalDictionary[currentImportLang].map(wd => wd.toLowerCase()).includes(w.toLowerCase())) {
                                this.state.personalDictionary[currentImportLang].push(w);
                            }
                        });
                    }
                    Object.keys(this.state.personalDictionary).forEach(lang => this.state.personalDictionary[lang].sort((a,b)=>a.localeCompare(b)));
                    this.updateDictionaryList();
                    this.saveState();
                    this.analyzeText(false);
                    this.showToast("Dictionary imported successfully.", "success");
                } catch (err) {
                    console.error("Error importing dictionary:", err);
                    this.showToast("Failed to import dictionary. Invalid file format.", "error");
                } finally {
                    this.showLoading(false);
                }
            };
            input.click();
        },

        exportDictionary() {
            if (Object.keys(this.state.personalDictionary).length === 0) {
                this.showToast("Dictionary is empty.", "warning");
                return;
            }
            const json = JSON.stringify(this.state.personalDictionary, null, 2);
            const blob = new Blob([json], { type: 'application/json;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'GrammarPro_Dictionary.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            this.showToast("Dictionary exported.", "success");
        },

        // --- AI FEATURES (Gemini API Integration) ---
        toggleAIContextPanel(show, text = null) {
            if (show && text && text.trim().length > 0) {
                this.elements.selectedTextPreview.textContent = `"${text.substring(0,100)}${text.length > 100 ? '...' : ''}"`;
                this.elements.aiContextPanel.classList.remove('hidden');
                this.elements.aiContextOutput.classList.add('hidden').textContent = '';
            } else if (show && this.state.currentSelection) {
                const selectedText = this.state.currentSelection.toString();
                if (selectedText.trim().length > 0) {
                    this.elements.selectedTextPreview.textContent = `"${selectedText.substring(0,100)}${selectedText.length > 100 ? '...' : ''}"`;
                    this.elements.aiContextPanel.classList.remove('hidden');
                    this.elements.aiContextOutput.classList.add('hidden').textContent = '';
                } else {
                    this.elements.aiContextPanel.classList.add('hidden');
                }
            }
            else {
                this.elements.aiContextPanel.classList.add('hidden');
            }
        },

        async handleBrainstormIdeas() {
            const selection = window.getSelection();
            let contextText = "";
            if (selection && selection.toString().trim().length > 0 && this.elements.textEditor.contains(selection.anchorNode)) {
                contextText = selection.toString().trim();
            } else {
                contextText = this.elements.textEditor.textContent.trim();
            }

            if (!contextText) {
                this.showToast("Please select text or write something in the editor to brainstorm ideas.", "warning");
                return;
            }

            const prompt = `Brainstorm ideas, related topics, or create a short outline based on the following text: "${contextText}"`;
            this.addRecentPrompt("Brainstorm ideas");
            this.showLoading(true, "AI is brainstorming...");

            try {
                const aiResponse = await this.callGeminiAPI(prompt);
                this.elements.selectedTextPreview.textContent = `Brainstorming based on: "${contextText.substring(0,100)}${contextText.length > 100 ? '...' : ''}"`;
                this.elements.aiContextOutput.innerHTML = aiResponse.replace(/\n/g, '<br>'); // Preserve line breaks
                this.elements.aiContextOutput.classList.remove('hidden');
                this.elements.aiContextPanel.classList.remove('hidden'); // Ensure panel is visible
                this.showToast("Brainstorming complete!", "success");
            } catch (error) {
                console.error("Brainstorming Error:", error);
                this.showToast(`Brainstorming Error: ${error.message}`, "error");
                this.elements.aiContextOutput.textContent = `Error: ${error.message}`;
                this.elements.aiContextOutput.classList.remove('hidden');
                this.elements.aiContextPanel.classList.remove('hidden');
            } finally {
                this.showLoading(false);
            }
        },

        async handleAIAction(action) {
            let promptText = "";
            let selectedText = "";
            if (this.state.currentSelection) {
                selectedText = this.state.currentSelection.toString().trim();
            }

            if (!selectedText && !['generate-text', 'brainstorm', 'adjust-tone-selection'].includes(action) ) {
                this.showToast("Please select some text first for this action.", "warning");
                return;
            }
            if (!selectedText && action === 'adjust-tone-selection' && this.elements.adjustToneBtn.closest('#aiContextPanel')) {
                this.showToast("Please select some text first to adjust its tone.", "warning");
                return;
            }
            if (action === 'adjust-tone-selection') { // Triggered by either sidebar or context panel
                if (!selectedText && this.elements.textEditor.textContent.trim().length > 0) {
                    // If adjustToneBtn from sidebar is clicked and no text selected, but editor has content
                    // We could offer to analyze the whole document, but for simplicity now, require selection
                    const userConfirmed = await this.showCustomPromptModal(
                        "Adjust Tone for Entire Document?",
                        "No text is selected. Do you want to try adjusting the tone for the entire document? This might take longer. Enter desired tone (e.g., formal, casual):",
                        "formal"
                    );
                    if (userConfirmed && userConfirmed.trim()) {
                        selectedText = this.elements.textEditor.textContent.trim(); // Use whole document
                        // Temporarily create a range for the whole document for replaceSelectedText
                        const tempRange = document.createRange();
                        tempRange.selectNodeContents(this.elements.textEditor);
                        this.state.currentSelection = tempRange; // This is a bit of a hack for replaceSelectedText
                    } else {
                        this.showToast("Tone adjustment cancelled or no tone provided.", "info");
                        return;
                    }
                } else if (!selectedText) {
                    this.showToast("Please select some text to adjust its tone.", "warning");
                    return;
                }

                // If text is selected (or whole doc confirmed for tone adjustment)
                const desiredTone = await this.showCustomPromptModal(
                    "Adjust Tone ✨",
                    "Enter the desired tone (e.g., more professional, friendly, humorous, skeptical):",
                    "more professional" // Default placeholder
                );

                if (desiredTone && desiredTone.trim()) {
                    promptText = `Rewrite the following text to make it sound ${desiredTone}: "${selectedText}"`;
                    this.addRecentPrompt(`Adjust tone to: ${desiredTone}`);
                } else {
                    this.showToast("Tone adjustment cancelled or no tone provided.", "info");
                    return; // User cancelled or entered nothing
                }

            } else {
                switch(action) {
                    case 'rewrite-selection':
                        promptText = `Rewrite the following text to improve its clarity and conciseness, keeping the original meaning. Adapt it for a ${this.elements.writingMode.value} tone. Text: "${selectedText}"`;
                        break;
                    case 'rewrite-formal':
                        promptText = `Rewrite the following text in a more formal tone: "${selectedText}"`;
                        break;
                    case 'rewrite-casual':
                        promptText = `Rewrite the following text in a more casual and friendly tone: "${selectedText}"`;
                        break;
                    case 'rewrite-shorter':
                        promptText = `Make the following text shorter and more concise: "${selectedText}"`;
                        break;
                    case 'rewrite-longer':
                        promptText = `Expand on the following text, adding more detail or explanation: "${selectedText}"`;
                        break;
                    case 'summarize':
                        promptText = `Summarize the following text: "${selectedText}"`;
                        break;
                    case 'explain':
                        promptText = `Explain the meaning or concept in the following text: "${selectedText}"`;
                        break;
                    case 'enhance-fluency':
                        promptText = `Rewrite the following text to improve its fluency and make it sound more natural for a native speaker, while preserving the original meaning. Text: "${selectedText}"`;
                        break;
                    default:
                        // If it's not 'adjust-tone-selection' and not other known actions, it might be an error or unhandled.
                        if(action !== 'adjust-tone-selection') { // Already handled above
                            this.showToast("Unknown AI action.", "error");
                            return;
                        }
                }
                if(promptText) this.addRecentPrompt(promptText.split(':')[0]);
            }

            if (!promptText) return; // If no prompt was generated (e.g., user cancelled tone input)


            this.showLoading(true, "AI is thinking...");
            try {
                const aiResponse = await this.callGeminiAPI(promptText);
                if ((action.startsWith('rewrite-') || action === 'enhance-fluency' || action === 'adjust-tone-selection') && this.state.currentSelection) {
                    this.replaceSelectedText(aiResponse);
                    this.toggleAIContextPanel(false);
                } else {
                    this.elements.aiContextOutput.innerHTML = aiResponse.replace(/\n/g, '<br>');
                    this.elements.aiContextOutput.classList.remove('hidden');
                    this.elements.aiContextPanel.classList.remove('hidden'); // Ensure panel is visible
                }
                this.showToast("AI action completed!", "success");
            } catch (error) {
                console.error("AI Action Error:", error);
                this.showToast(`AI Error: ${error.message}`, "error");
                this.elements.aiContextOutput.textContent = `Error: ${error.message}`;
                this.elements.aiContextOutput.classList.remove('hidden');
                this.elements.aiContextPanel.classList.remove('hidden');
            } finally {
                this.showLoading(false);
            }
        },

        async handleAIGenerateText() {
            const userPrompt = this.elements.aiPromptInput.value.trim();
            if (!userPrompt) {
                this.showToast("Please enter a prompt for AI generation.", "warning");
                return;
            }

            this.addRecentPrompt(`Generate: ${userPrompt.substring(0,30)}...`);
            this.showLoading(true, "AI is generating text...");
            try {
                const aiResponse = await this.callGeminiAPI(userPrompt);
                const editor = this.elements.textEditor;
                const selection = window.getSelection();
                if (selection.rangeCount > 0 && editor.contains(selection.anchorNode)) {
                    const range = selection.getRangeAt(0);
                    range.deleteContents();
                    range.insertNode(document.createTextNode(aiResponse + " "));
                    range.collapse(false);
                } else {
                    editor.textContent += (editor.textContent.length > 0 ? "\n\n" : "") + aiResponse;
                }
                this.elements.aiPromptInput.value = "";
                this.analyzeText(false);
                this.recordHistory();
                this.showToast("AI text generated!", "success");
            } catch (error) {
                console.error("AI Generation Error:", error);
                this.showToast(`AI Generation Error: ${error.message}`, "error");
            } finally {
                this.showLoading(false);
            }
        },

        replaceSelectedText(newText) {
            if (this.state.currentSelection) {
                const editor = this.elements.textEditor;
                editor.focus();

                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(this.state.currentSelection);

                document.execCommand('insertText', false, newText);

                this.state.currentSelection = null;
                this.analyzeText(false);
                this.recordHistory();
            }
        },

        async callGeminiAPI(prompt) {
            const apiKey = "";
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

            const payload = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
            };

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API Error (${response.status}): ${errorData.error?.message || 'Unknown error'}`);
            }

            const result = await response.json();

            if (result.candidates && result.candidates.length > 0 &&
                result.candidates[0].content && result.candidates[0].content.parts &&
                result.candidates[0].content.parts.length > 0) {
                return result.candidates[0].content.parts[0].text;
            } else {
                console.warn("Unexpected API response structure:", result);
                throw new Error("Failed to get a valid response from AI.");
            }
        },

        addRecentPrompt(promptText) {
            if (!this.state.settings.aiPromptHistory) return;

            this.state.recentPrompts.unshift(promptText);
            if (this.state.recentPrompts.length > this.config.maxRecentPrompts) {
                this.state.recentPrompts.pop();
            }
            this.updateRecentPromptsList();
            this.saveState();
        },

        updateRecentPromptsList() {
            const list = this.elements.recentPromptsList;
            if (this.state.recentPrompts.length === 0) {
                list.innerHTML = `<p class="text-center py-2" style="color: var(--text-secondary);">No recent prompts.</p>`;
                return;
            }
            list.innerHTML = this.state.recentPrompts.map(prompt => `
                <div class="p-1.5 rounded cursor-pointer hover-bg-alt recent-prompt-item" style="background-color: var(--bg-alt); border: 1px solid var(--border-color);" title="Click to reuse: ${this.escapeHTML(prompt)}">
                    ${this.escapeHTML(prompt.length > 40 ? prompt.substring(0, 37) + '...' : prompt)}
                </div>
            `).join('');

            list.querySelectorAll('.recent-prompt-item').forEach(item => {
                item.addEventListener('click', () => {
                    const fullPromptText = this.state.recentPrompts.find(p => item.textContent.startsWith(p.substring(0,37)));
                    if (fullPromptText) {
                        if (fullPromptText.startsWith("Generate: ")) {
                            this.elements.aiPromptInput.value = fullPromptText.replace("Generate: ", "");
                            this.elements.aiPromptInput.focus();
                            this.showToast("Prompt loaded into generator.", "info");
                        } else {
                            navigator.clipboard.writeText(fullPromptText)
                                .then(() => this.showToast("Rewrite action copied to clipboard.", "info"))
                                .catch(err => this.showToast("Could not copy prompt.", "error"));
                        }
                    }
                });
            });
        },

        // --- MODALS & UI UTILITIES ---
        toggleModal(modalElement, forceShow) {
            if (forceShow === true) modalElement.classList.remove('hidden', 'items-center', 'justify-center');
            else if (forceShow === false) modalElement.classList.add('hidden');
            else modalElement.classList.toggle('hidden');

            if (!modalElement.classList.contains('hidden')) {
                modalElement.classList.add('flex', 'items-center', 'justify-center');
            } else {
                modalElement.classList.remove('flex', 'items-center', 'justify-center');
            }

            if (modalElement === this.elements.settingsModal && !modalElement.classList.contains('hidden')) {
                this.loadSettingsIntoModalUI();
                this.switchSettingsSection('language');
                this.elements.settingsNavItems[0].classList.add('active');
            }
        },

        showConfirmModal(title, message, onConfirm) {
            this.elements.confirmTitle.textContent = title;
            this.elements.confirmMessage.textContent = message;
            this.toggleModal(this.elements.confirmModal, true);

            const oldOkBtn = this.elements.confirmOkBtn;
            const newOkBtn = oldOkBtn.cloneNode(true);
            oldOkBtn.parentNode.replaceChild(newOkBtn, oldOkBtn);
            this.elements.confirmOkBtn = newOkBtn;

            this.elements.confirmOkBtn.onclick = () => {
                onConfirm();
                this.toggleModal(this.elements.confirmModal, false);
            };
        },

        showCustomPromptModal(title, message, placeholder = "") {
            return new Promise((resolve) => {
                this.elements.customPromptTitle.textContent = title;
                this.elements.customPromptMessage.textContent = message;
                this.elements.customPromptInputModal.value = "";
                this.elements.customPromptInputModal.placeholder = placeholder;
                this.toggleModal(this.elements.customPromptModal, true);
                this.elements.customPromptInputModal.focus();

                this.state.currentAIToneActionCallback = (inputValue) => {
                    this.toggleModal(this.elements.customPromptModal, false);
                    this.state.currentAIToneActionCallback = null; // Clear callback
                    resolve(inputValue);
                };

                // Remove previous listener before adding a new one
                const oldOkBtn = this.elements.customPromptOkBtn;
                const newOkBtn = oldOkBtn.cloneNode(true);
                oldOkBtn.parentNode.replaceChild(newOkBtn, oldOkBtn);
                this.elements.customPromptOkBtn = newOkBtn;

                this.elements.customPromptOkBtn.onclick = () => {
                    if (this.state.currentAIToneActionCallback) {
                        this.state.currentAIToneActionCallback(this.elements.customPromptInputModal.value);
                    }
                };
                // Cancel button should also resolve with null or empty
                const oldCancelBtn = this.elements.customPromptCancelBtn;
                const newCancelBtn = oldCancelBtn.cloneNode(true);
                oldCancelBtn.parentNode.replaceChild(newCancelBtn, oldCancelBtn);
                this.elements.customPromptCancelBtn = newCancelBtn;
                this.elements.customPromptCancelBtn.onclick = () => {
                    if (this.state.currentAIToneActionCallback) {
                        this.state.currentAIToneActionCallback(null); // Indicate cancellation
                    }
                };
            });
        },


        showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `fixed bottom-5 right-5 p-4 rounded-lg shadow-md text-sm z-[9999] animate-fadeIn`;
            let bgColor, textColor, iconClass;

            switch(type) {
                case 'success': bgColor = 'var(--success-color)'; textColor = 'white'; iconClass = 'fas fa-check-circle'; break;
                case 'error': bgColor = 'var(--error-color)'; textColor = 'white'; iconClass = 'fas fa-times-circle'; break;
                case 'warning': bgColor = 'var(--warning-color)'; textColor = 'var(--text-primary)'; iconClass = 'fas fa-exclamation-triangle'; break;
                default: bgColor = 'var(--info-color)'; textColor = 'white'; iconClass = 'fas fa-info-circle';
            }
            toast.style.backgroundColor = bgColor;
            toast.style.color = textColor;
            toast.innerHTML = `<i class="${iconClass} mr-2"></i> ${message}`;

            document.body.appendChild(toast);
            setTimeout(() => {
                toast.style.animation = 'fadeOut 0.5s forwards';
                setTimeout(() => toast.remove(), 500);
            }, duration);
        },

        showLoading(show, message = "Processing...") {
            if (show) {
                this.elements.loadingMessage.textContent = message;
                this.elements.loadingOverlay.classList.remove('hidden');
                this.elements.loadingOverlay.classList.add('flex');
            } else {
                this.elements.loadingOverlay.classList.add('hidden');
                this.elements.loadingOverlay.classList.remove('flex');
            }
        },

        updateStatus(message, type = 'info', isLoading = false) {
            this.elements.statusText.textContent = message;
            const icon = this.elements.statusIcon;
            icon.classList.remove('fa-check-circle', 'fa-times-circle', 'fa-info-circle', 'fa-spinner', 'fa-spin');
            icon.style.color = 'var(--text-secondary)';

            if (isLoading) {
                icon.classList.add('fas', 'fa-spinner', 'fa-spin');
                icon.style.color = 'var(--primary-color)';
            } else {
                switch(type) {
                    case 'success': icon.classList.add('fas', 'fa-check-circle'); icon.style.color = 'var(--success-color)'; break;
                    case 'error': icon.classList.add('fas', 'fa-times-circle'); icon.style.color = 'var(--error-color)'; break;
                    default: icon.classList.add('fas', 'fa-info-circle'); icon.style.color = 'var(--info-color)';
                }
                setTimeout(() => {
                    if (this.elements.statusText.textContent === message) {
                        this.elements.statusText.textContent = "Ready";
                        this.elements.statusIcon.className = 'fas fa-circle text-xs';
                        this.elements.statusIcon.style.color = 'var(--success-color)';
                    }
                }, 3000);
            }
        },

        positionPopupNearElement(popupElement, targetElement) {
            const targetRect = targetElement.getBoundingClientRect();
            const popupRect = popupElement.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            let top = targetRect.bottom + window.scrollY + 5;
            let left = targetRect.left + window.scrollX;

            if (top + popupRect.height > viewportHeight + window.scrollY) {
                top = targetRect.top + window.scrollY - popupRect.height - 5;
            }
            if (left + popupRect.width > viewportWidth + window.scrollX) {
                left = viewportWidth + window.scrollX - popupRect.width - 10;
            }
            if (left < window.scrollX + 10) {
                left = window.scrollX + 10;
            }

            popupElement.style.top = `${top}px`;
            popupElement.style.left = `${left}px`;
        },

        // --- SETTINGS MODAL LOGIC ---
        loadSettingsIntoModalUI() {
            this.elements.primaryLanguage.value = this.state.currentLanguage;
            this.populateDialectSelector(this.state.currentLanguage, this.elements.dialectSelect);
            this.elements.dialectSelect.value = this.state.currentDialect;
            this.elements.dialectSelect.disabled = this.state.currentLanguage === 'auto' || Object.keys(this.config.languages[this.state.currentLanguage]?.dialects || {}).length <= 1;

            this.elements.themeRadios.forEach(radio => radio.checked = radio.value === this.state.theme);
            this.elements.fontSizeBtns.forEach(btn => {
                const isActive = btn.dataset.size === this.state.fontSize;
                btn.classList.toggle('active', isActive);
                btn.style.backgroundColor = isActive ? 'var(--primary-color)' : 'var(--bg-secondary)';
                btn.style.color = isActive ? 'white' : 'var(--text-primary)';
            });

            for (const key in this.state.settings) {
                if (this.elements[key] && typeof this.elements[key].checked !== 'undefined') {
                    this.elements[key].checked = this.state.settings[key];
                }
            }
            this.elements.realTimeCheckDelay.value = this.state.settings.realTimeCheckDelay;
            this.elements.realTimeCheckDelayValue.textContent = `${this.state.settings.realTimeCheckDelay}ms`;
            this.elements.languageProficiency.value = this.state.settings.languageProficiency;

            this.elements.dictionaryLanguageSelect.value = this.state.currentLanguage === 'auto' ? (navigator.language || navigator.userLanguage || 'en').split('-')[0] : this.state.currentLanguage;
            this.updateDictionaryList();
        },

        saveSettingsFromModal() {
            const newLangInModal = this.elements.primaryLanguage.value;
            let newDialectInModal;

            if (newLangInModal === 'auto') {
                this.state.currentLanguage = 'auto';
                this.state.currentDialect = '';
                this.autoSetUILanguage();
            } else {
                newDialectInModal = this.elements.dialectSelect.value || Object.keys(this.config.languages[newLangInModal].dialects)[0];
                this.setLanguage(newLangInModal, newDialectInModal);
            }

            for (const key in this.state.settings) {
                if (this.elements[key] && typeof this.elements[key].checked !== 'undefined') {
                    this.state.settings[key] = this.elements[key].checked;
                }
            }
            this.state.settings.realTimeCheckDelay = parseInt(this.elements.realTimeCheckDelay.value);
            this.state.settings.languageProficiency = this.elements.languageProficiency.value;

            this.applySettings();
            this.saveState();
            this.toggleModal(this.elements.settingsModal, false);
            this.showToast("Settings saved!", "success");
        },

        switchSettingsSection(sectionId) {
            this.elements.settingsSections.forEach(section => {
                section.classList.toggle('hidden', section.id !== `settings-section-${sectionId}`);
            });
            this.elements.settingsNavItems.forEach(item => {
                item.classList.toggle('active', item.dataset.section === sectionId);
            });
        },

        handleSettingsSearch() {
            const searchTerm = this.elements.settingsSearchInput.value.toLowerCase();
            this.elements.settingsSections.forEach(section => {
                let sectionHasMatch = false;
                const settingItems = section.querySelectorAll('div > label, .flex.items-center.justify-between, h3, h4');

                settingItems.forEach(item => {
                    const textContent = item.textContent.toLowerCase();
                    if (textContent.includes(searchTerm)) {
                        item.style.display = '';
                        let parentHeader = item.closest('.settings-section-content').querySelector('h3, h4');
                        if(parentHeader && parentHeader.style.display === 'none') parentHeader.style.display = '';
                        sectionHasMatch = true;
                    } else {
                        item.style.display = 'none';
                    }
                });
                if (!searchTerm.trim()) {
                    settingItems.forEach(item => item.style.display = '');
                    sectionHasMatch = true;
                }
            });
        },

        confirmResetSettings() {
            this.showConfirmModal("Reset Settings", "Are you sure you want to reset all settings to their defaults? This will not affect your dictionary.", () => {
                this.state.settings = JSON.parse(JSON.stringify(this.config.defaultSettings));
                this.state.theme = 'system';
                this.state.fontSize = 'normal';
                this.applySettings();
                this.loadSettingsIntoModalUI();
                this.showToast("Settings reset to default.", "success");
            });
        },

        showHelpModalIfFirstVisit() {
            if (!localStorage.getItem('GrammarProVisited')) {
                this.toggleModal(this.elements.helpModal, true);
                localStorage.setItem('GrammarProVisited', 'true');
            }
        },

        // --- AUTO-CORRECTION AND ENHANCEMENT ---
        performAutoCorrection() {
            if (!this.state.settings.autoCorrection) return;

            const text = this.elements.textEditor.textContent;
            if (!text.trim()) return;

            const corrections = this.findAutoCorrections(text);
            if (corrections.length > 0) {
                this.applyCorrections(corrections);
            }
        },

        findAutoCorrections(text) {
            const corrections = [];
            const commonCorrections = {
                // Common typos
                'teh': 'the',
                'wierd': 'weird',
                'recieve': 'receive',
                'seperate': 'separate',
                'definately': 'definitely',
                'occured': 'occurred',
                'neccessary': 'necessary',
                'accomodate': 'accommodate',
                'embarass': 'embarrass',
                'maintainance': 'maintenance',

                // Grammar corrections
                'your welcome': "you're welcome",
                'its a': "it's a",
                'their going': "they're going",
                'there going': "they're going",
                'your going': "you're going",
                'could of': 'could have',
                'should of': 'should have',
                'would of': 'would have',

                // Capitalization
                'i am': 'I am',
                'i was': 'I was',
                'i have': 'I have',
                'i will': 'I will',
                'i can': 'I can',
                'i think': 'I think'
            };

            // Find corrections in the text
            for (const [incorrect, correct] of Object.entries(commonCorrections)) {
                const regex = new RegExp(`\\b${incorrect}\\b`, 'gi');
                let match;
                while ((match = regex.exec(text)) !== null) {
                    corrections.push({
                        start: match.index,
                        end: match.index + match[0].length,
                        original: match[0],
                        correction: this.preserveCase(match[0], correct),
                        type: 'auto-correction'
                    });
                }
            }

            return corrections;
        },

        preserveCase(original, correction) {
            if (original === original.toUpperCase()) {
                return correction.toUpperCase();
            } else if (original[0] === original[0].toUpperCase()) {
                return correction.charAt(0).toUpperCase() + correction.slice(1).toLowerCase();
            } else {
                return correction.toLowerCase();
            }
        },

        applyCorrections(corrections) {
            if (corrections.length === 0) return;

            const editor = this.elements.textEditor;

            // Save current cursor position
            const currentPosition = this.getCaretPosition();

            let text = editor.textContent;
            let positionOffset = 0;

            // Sort corrections by position (reverse order to maintain indices)
            corrections.sort((a, b) => b.start - a.start);

            corrections.forEach(correction => {
                const beforeText = text.substring(0, correction.start);
                const afterText = text.substring(correction.end);
                text = beforeText + correction.correction + afterText;

                // Calculate how the cursor position should be adjusted
                if (correction.start <= currentPosition) {
                    positionOffset += (correction.correction.length - correction.original.length);
                }
            });

            // Update editor content
            editor.textContent = text;

            // Restore cursor position with adjustment for corrections
            const newPosition = Math.max(0, currentPosition + positionOffset);
            this.setCaretPosition(newPosition);

            // Show feedback
            const translations = this.config.translations[this.state.currentLanguage] || this.config.translations['en'];
            if (this.state.settings.showCorrectionFeedback) {
                this.showToast(
                    `${corrections.length} ${translations['correction-applied'] || 'correction(s) applied'}`,
                    'success',
                    2000
                );
            }

            this.analyzeText(false);
        },

        performSentenceEnhancement() {
            if (!this.state.settings.sentenceEnhancement) return;

            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;

            const text = this.elements.textEditor.textContent;
            if (!text.trim()) return;

            const enhancements = this.findSentenceEnhancements(text);
            if (enhancements.length > 0) {
                this.applySentenceEnhancements(enhancements);
            }
        },

        findSentenceEnhancements(text) {
            const enhancements = [];
            const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

            sentences.forEach((sentence, index) => {
                const trimmed = sentence.trim();
                if (trimmed.length < 5) return; // Skip very short sentences

                // Check for enhancement opportunities
                const enhancement = this.getSentenceEnhancement(trimmed);
                if (enhancement) {
                    const sentenceStart = text.indexOf(trimmed);
                    if (sentenceStart !== -1) {
                        enhancements.push({
                            start: sentenceStart,
                            end: sentenceStart + trimmed.length,
                            original: trimmed,
                            enhancement: enhancement,
                            type: 'sentence-enhancement'
                        });
                    }
                }
            });

            return enhancements;
        },

        getSentenceEnhancement(sentence) {
            // Simple sentence enhancement rules
            const enhancements = {
                // Improve clarity
                'very good': 'excellent',
                'very bad': 'terrible',
                'very big': 'enormous',
                'very small': 'tiny',
                'a lot of': 'many',
                'lots of': 'numerous',

                // Remove redundancy
                'in order to': 'to',
                'due to the fact that': 'because',
                'at this point in time': 'now',
                'in the event that': 'if',

                // Improve formality
                'gonna': 'going to',
                'wanna': 'want to',
                'gotta': 'have to',
                'kinda': 'kind of',
                'sorta': 'sort of'
            };

            let enhanced = sentence;
            let hasChanges = false;

            for (const [phrase, improvement] of Object.entries(enhancements)) {
                const regex = new RegExp(`\\b${phrase}\\b`, 'gi');
                if (regex.test(enhanced)) {
                    enhanced = enhanced.replace(regex, improvement);
                    hasChanges = true;
                }
            }

            return hasChanges ? enhanced : null;
        },

        applySentenceEnhancements(enhancements) {
            if (enhancements.length === 0) return;

            // For now, just show suggestions rather than auto-applying
            // This is more conservative and gives users control
            const translations = this.config.translations[this.state.currentLanguage] || this.config.translations['en'];

            if (this.state.settings.showCorrectionFeedback) {
                this.showToast(
                    `${enhancements.length} ${translations['sentence-enhanced'] || 'sentence enhancement(s) available'}`,
                    'info',
                    3000
                );
            }

            // Add enhancements to suggestions panel
            this.displayEnhancementSuggestions(enhancements);
        },

        displayEnhancementSuggestions(enhancements) {
            const suggestionsList = this.elements.suggestionsList;

            enhancements.forEach(enhancement => {
                const suggestionDiv = document.createElement('div');
                suggestionDiv.className = 'p-3 rounded border cursor-pointer hover:bg-gray-50 transition-colors';
                suggestionDiv.style.borderColor = 'var(--info-color)';
                suggestionDiv.style.backgroundColor = 'var(--bg-primary)';

                suggestionDiv.innerHTML = `
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-blue-500 mt-1"></i>
                        <div class="flex-1">
                            <p class="text-sm font-medium" style="color: var(--text-primary);">Sentence Enhancement</p>
                            <p class="text-xs" style="color: var(--text-secondary);">Original: "${enhancement.original}"</p>
                            <p class="text-xs" style="color: var(--info-color);">Enhanced: "${enhancement.enhancement}"</p>
                            <button class="mt-2 px-2 py-1 text-xs rounded" style="background-color: var(--info-color); color: white;">Apply Enhancement</button>
                        </div>
                    </div>
                `;

                suggestionDiv.querySelector('button').addEventListener('click', () => {
                    this.applyEnhancement(enhancement);
                    suggestionDiv.remove();
                });

                suggestionsList.appendChild(suggestionDiv);
            });
        },

        applyEnhancement(enhancement) {
            const editor = this.elements.textEditor;

            // Save current cursor position
            const currentPosition = this.getCaretPosition();

            let text = editor.textContent;
            const beforeText = text.substring(0, enhancement.start);
            const afterText = text.substring(enhancement.end);
            text = beforeText + enhancement.enhancement + afterText;

            editor.textContent = text;

            // Calculate position adjustment and restore cursor
            let positionOffset = 0;
            if (enhancement.start <= currentPosition) {
                positionOffset = enhancement.enhancement.length - enhancement.original.length;
            }
            const newPosition = Math.max(0, currentPosition + positionOffset);
            this.setCaretPosition(newPosition);

            const translations = this.config.translations[this.state.currentLanguage] || this.config.translations['en'];
            this.showToast(translations['enhancement-applied'] || 'Enhancement applied', 'success');

            this.analyzeText(false);
            this.recordHistory();
        },

        // --- UTILITIES ---
        debounce(func, delay) {
            let timeout;
            return (...args) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), delay);
            };
        },
    };

    // --- START APPLICATION ---
    LinguaFlow.init();

    // Make LinguaFlow globally accessible for debugging if needed
    window.LinguaFlow = LinguaFlow;

</script>
</body>
</html>