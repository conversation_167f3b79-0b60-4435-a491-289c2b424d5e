import { useCallback } from 'react';
import { useAppStore } from '../../store';
import { Language } from './translations';

/**
 * Hook to manage language functionality
 * @returns Object with language state and functions
 */
export const useLanguage = () => {
  const { 
    currentLanguage, 
    setCurrentLanguage,
    translations
  } = useAppStore();
  
  /**
   * Update the application language
   */
  const updateLanguage = useCallback((language: Language) => {
    setCurrentLanguage(language);
    
    // Update document attributes
    document.documentElement.lang = language;
    
    // Set RTL attribute if needed
    if (translations.languages[language].rtl) {
      document.documentElement.dir = 'rtl';
    } else {
      document.documentElement.dir = 'ltr';
    }
    
    // Save preference
    localStorage.setItem('preferredLanguage', language);
  }, [setCurrentLanguage, translations]);
  
  /**
   * Detect system language
   */
  const detectSystemLanguage = useCallback((): Language => {
    const browserLang = navigator.language.split('-')[0];
    return (browserLang as Language) in translations.languages ? (browserLang as Language) : 'en';
  }, [translations]);
  
  /**
   * Get translation for a key
   */
  const t = useCallback((key: string): string => {
    return translations.strings[currentLanguage]?.[key] || 
           translations.strings.en[key] || 
           key;
  }, [currentLanguage, translations]);
  
  /**
   * Check if current language is RTL
   */
  const isRtl = useCallback((): boolean => {
    return translations.languages[currentLanguage]?.rtl || false;
  }, [currentLanguage, translations]);
  
  return {
    currentLanguage,
    languages: translations.languages,
    updateLanguage,
    detectSystemLanguage,
    t,
    isRtl
  };
};
