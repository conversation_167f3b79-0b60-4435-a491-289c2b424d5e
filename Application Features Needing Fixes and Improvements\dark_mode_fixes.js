/**
 * Enhanced dark mode functionality for GrammarPro application
 * Properly applies theme changes to all UI components
 */

/**
 * Toggle between light, dark, and auto themes
 */
function toggleTheme() {
  const themes = ['light', 'dark', 'auto'];
  const currentIndex = themes.indexOf(state.theme);
  state.theme = themes[(currentIndex + 1) % themes.length];
  updateTheme();
  saveSettings();
}

/**
 * Update the application theme based on current theme setting
 * This enhanced function ensures all components update correctly
 */
function updateTheme() {
  const html = document.documentElement;
  const themeToggle = document.getElementById('themeToggle');
  let currentTheme;
  
  if (state.theme === 'auto') {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    currentTheme = prefersDark ? 'dark' : 'light';
    html.setAttribute('data-theme', currentTheme);
    if (themeToggle) {
      themeToggle.innerHTML = '<i class="fas fa-adjust text-lg"></i>';
      themeToggle.setAttribute('data-i18n-title', 'autoTheme');
    }
  } else {
    currentTheme = state.theme;
    html.setAttribute('data-theme', currentTheme);
    if (themeToggle) {
      themeToggle.innerHTML = currentTheme === 'dark' ? 
        '<i class="fas fa-sun text-lg"></i>' : 
        '<i class="fas fa-moon text-lg"></i>';
      themeToggle.setAttribute('data-i18n-title', currentTheme === 'dark' ? 'lightTheme' : 'darkTheme');
    }
  }
  
  // Force component updates that might not respond to CSS variables
  updateComponentsForTheme(currentTheme);
}

/**
 * Update specific components that need special handling for theme changes
 * @param {string} theme - The current theme ('light' or 'dark')
 */
function updateComponentsForTheme(theme) {
  const isDark = theme === 'dark';
  
  // Update modal backgrounds
  document.querySelectorAll('.bg-white').forEach(element => {
    // Only update elements that should change with theme
    if (shouldUpdateElementForTheme(element)) {
      element.classList.toggle('bg-white', !isDark);
      element.classList.toggle('bg-gray-800', isDark);
    }
  });
  
  // Update text colors
  document.querySelectorAll('.text-gray-900, .text-gray-800, .text-gray-700').forEach(element => {
    if (shouldUpdateElementForTheme(element)) {
      element.classList.toggle('text-gray-900', !isDark);
      element.classList.toggle('text-gray-800', !isDark);
      element.classList.toggle('text-gray-700', !isDark);
      element.classList.toggle('text-gray-100', isDark);
      element.classList.toggle('text-gray-200', isDark);
      element.classList.toggle('text-gray-300', isDark);
    }
  });
  
  // Update borders
  document.querySelectorAll('.border-gray-200, .border-gray-300').forEach(element => {
    if (shouldUpdateElementForTheme(element)) {
      element.classList.toggle('border-gray-200', !isDark);
      element.classList.toggle('border-gray-300', !isDark);
      element.classList.toggle('border-gray-600', isDark);
      element.classList.toggle('border-gray-700', isDark);
    }
  });
  
  // Update editor background
  const editor = document.getElementById('textEditor');
  if (editor) {
    editor.style.backgroundColor = isDark ? 'var(--bg-primary)' : 'white';
    editor.style.color = isDark ? 'var(--text-primary)' : 'var(--text-primary)';
  }
  
  // Update suggestion cards
  document.querySelectorAll('.suggestion-card').forEach(card => {
    card.style.backgroundColor = isDark ? 'var(--bg-secondary)' : 'white';
    card.style.borderColor = isDark ? 'var(--border-color)' : 'var(--border-color)';
  });
  
  // Update buttons
  updateButtonsForTheme(isDark);
}

/**
 * Determine if an element should be updated for theme changes
 * @param {Element} element - The DOM element to check
 * @returns {boolean} - Whether the element should be updated
 */
function shouldUpdateElementForTheme(element) {
  // Skip elements that should maintain their color regardless of theme
  const skipClasses = [
    'writing-stats',
    'ai-suggestion',
    'primary-color',
    'secondary-color',
    'accent-color',
    'btn-primary'
  ];
  
  for (const cls of skipClasses) {
    if (element.classList.contains(cls) || element.closest(`.${cls}`)) {
      return false;
    }
  }
  
  return true;
}

/**
 * Update button styles for the current theme
 * @param {boolean} isDark - Whether dark mode is active
 */
function updateButtonsForTheme(isDark) {
  // Update secondary buttons
  document.querySelectorAll('.btn-secondary').forEach(button => {
    button.style.backgroundColor = isDark ? 'var(--bg-secondary)' : 'var(--bg-secondary)';
    button.style.color = isDark ? 'var(--text-primary)' : 'var(--text-primary)';
    button.style.borderColor = isDark ? 'var(--border-color)' : 'var(--border-color)';
  });
  
  // Settings buttons
  document.querySelectorAll('.settings-nav-item').forEach(button => {
    if (!button.classList.contains('active')) {
      button.style.backgroundColor = isDark ? 'var(--bg-secondary)' : 'var(--bg-secondary)';
      button.style.color = isDark ? 'var(--text-primary)' : 'var(--text-primary)';
    }
  });
}

/**
 * Initialize theme functionality
 * Sets up event listeners and loads initial theme
 */
function initializeTheme() {
  // Set up theme toggle event listener
  const themeToggle = document.getElementById('themeToggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', toggleTheme);
  }
  
  // Add CSS for smooth theme transitions
  addThemeTransitionStyles();
  
  // Load saved theme or default to light
  const savedTheme = state.theme || 'light';
  state.theme = savedTheme;
  updateTheme();
  
  // Listen for system theme changes if in auto mode
  if (state.theme === 'auto') {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', updateTheme);
  }
}

/**
 * Add CSS for smooth theme transitions
 */
function addThemeTransitionStyles() {
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    * {
      transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
    }
    
    /* Dark mode specific styles */
    [data-theme="dark"] .suggestion-card {
      background-color: var(--bg-secondary);
      border-color: var(--border-color);
    }
    
    [data-theme="dark"] .editor-container {
      background-color: var(--bg-primary);
      color: var(--text-primary);
    }
    
    [data-theme="dark"] .btn-secondary {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
      border-color: var(--border-color);
    }
    
    [data-theme="dark"] .btn-secondary:hover {
      background-color: var(--border-color);
    }
    
    [data-theme="dark"] .modal-content {
      background-color: var(--bg-primary);
      color: var(--text-primary);
    }
    
    [data-theme="dark"] .settings-nav-item:not(.active) {
      color: var(--text-primary);
    }
    
    [data-theme="dark"] .settings-nav-item:not(.active):hover {
      background-color: var(--border-color);
    }
  `;
  document.head.appendChild(styleElement);
}
